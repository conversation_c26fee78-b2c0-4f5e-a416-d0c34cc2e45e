// Vercel serverless function для объединения видео фрагментов
// POST /api/video-combine

export default async function handler(req, res) {
  console.log(`[VIDEO COMBINE] ${req.method} request received`);

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { videoUrls, aspectRatio, resolution, userId } = req.body;

    if (!videoUrls || !Array.isArray(videoUrls) || videoUrls.length === 0) {
      return res.status(400).json({ error: 'videoUrls is required and must be a non-empty array' });
    }

    console.log('[VIDEO COMBINE] Combining videos:', { videoUrls, aspectRatio, resolution, userId });

    // TODO: Реализовать реальное объединение видео через FFmpeg
    // ВРЕМЕННОЕ РЕШЕНИЕ: Создаем "плейлист" из всех видео
    // В реальной реализации здесь будет:
    // 1. Скачивание всех видео
    // 2. Объединение через FFmpeg
    // 3. Загрузка результата в Supabase Storage
    // 4. Возврат URL объединенного видео

    // Пока что возвращаем JSON с информацией о всех видео
    const combinedVideoData = {
      type: 'playlist',
      videos: videoUrls,
      totalDuration: videoUrls.length * 5, // Примерно 5 сек на видео
      aspectRatio: aspectRatio,
      resolution: resolution,
      message: 'Это временная реализация - показывает все видео по очереди'
    };

    // Для совместимости возвращаем первое видео как URL
    const combinedVideoUrl = videoUrls[0];

    // Симуляция времени обработки
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('[VIDEO COMBINE] Success:', combinedVideoUrl);

    return res.status(200).json({
      success: true,
      videoUrl: combinedVideoUrl, // Первое видео для совместимости
      combinedData: combinedVideoData, // Полная информация о всех видео
      allVideos: videoUrls, // Все видео для фронтенда
      message: 'Videos combined successfully (temporary implementation - showing first video only)'
    });

  } catch (error) {
    console.error('[VIDEO COMBINE] Error:', error);
    return res.status(500).json({ 
      error: 'Failed to combine videos',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
