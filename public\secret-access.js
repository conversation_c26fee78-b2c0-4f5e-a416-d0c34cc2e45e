// Скрипт для обработки секретных функций на сайте
console.log('Secret access helper loaded');

// Функция для проверки секретной комбинации (Konami Code)
(function() {
  // Секретный код Konami
  const secretCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];
  let keyPressHistory = [];
  let lastKeyTime = Date.now();
  
  document.addEventListener('keydown', function(e) {
    const key = e.code;
    const currentTime = Date.now();
    
    // Сбрасываем историю, если прошло более 2 секунд
    if (currentTime - lastKeyTime > 2000) {
      keyPressHistory = [];
    }
    
    lastKeyTime = currentTime;
    keyPressHistory.push(key);
    
    // Сохраняем только последние N клавиш
    if (keyPressHistory.length > secretCode.length) {
      keyPressHistory.shift();
    }
    
    // Проверяем, совпадает ли последовательность
    if (keyPressHistory.length === secretCode.length && 
        keyPressHistory.every((key, i) => key === secretCode[i])) {
      // Секретная комбинация введена правильно
      triggerSecretAction();
    }
  });
  
  // Функция для выполнения секретного действия
  function triggerSecretAction() {
    console.log('Secret code activated!');
    
    // Отправляем событие в приложение React с количеством кредитов
    const event = new CustomEvent('secretCodeActivated', {
      detail: { credits: 300 }
    });
    window.dispatchEvent(event);
    
    // Визуальный эффект активации
    const body = document.body;
    const flashElement = document.createElement('div');
    flashElement.style.position = 'fixed';
    flashElement.style.top = '0';
    flashElement.style.left = '0';
    flashElement.style.width = '100vw';
    flashElement.style.height = '100vh';
    flashElement.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    flashElement.style.zIndex = '9999';
    flashElement.style.pointerEvents = 'none';
    flashElement.style.transition = 'opacity 0.5s ease-out';
    
    body.appendChild(flashElement);
    
    // Анимация вспышки
    setTimeout(() => {
      flashElement.style.opacity = '0';
      setTimeout(() => {
        body.removeChild(flashElement);
      }, 500);
    }, 100);
  }
})();
