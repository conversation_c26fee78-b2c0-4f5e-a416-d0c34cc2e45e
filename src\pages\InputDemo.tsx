import InvertedInput from '@/components/ui/inverted-input'

const InputDemo = () => {
  const handleSubmit = () => {
    console.log('Submit clicked')
  }

  return (
    <div className="min-h-screen w-full flex items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-2xl">
        <InvertedInput
          onSubmit={handleSubmit}
          placeholder="Enter text..."
        />
      </div>
    </div>
  )
}

export default InputDemo