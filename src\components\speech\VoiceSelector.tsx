import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Mic, Users, User, Play, Pause } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { SYSTEM_VOICES, ELEVENLABS_VOICES, getUserVoices, getCommunityVoices } from '@/utils/speechApi';
import { generateGradientAvatar } from '@/utils/audioUtils';
import { toast } from 'sonner';

interface Voice {
  id: string;
  name: string;
  type: 'system' | 'user' | 'community';
  category?: string;
  creator_id?: string;
  audio_url?: string;
  usage_count?: number;
}

interface VoiceSelectorProps {
  selectedVoice: Voice | null;
  onVoiceSelect: (voice: Voice) => void;
  className?: string;
  model?: string; // Добавляем модель для определения доступных голосов
}

const VoiceSelector: React.FC<VoiceSelectorProps> = ({
  selectedVoice,
  onVoiceSelect,
  className,
  model
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [userVoices, setUserVoices] = useState<Voice[]>([]);
  const [communityVoices, setCommunityVoices] = useState<Voice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  // Системные голоса с переводами
  const systemVoices: Voice[] = SYSTEM_VOICES.map(voice => ({
    id: voice.id,
    name: voice.name,
    type: 'system' as const,
    category: voice.category
  }));

  // ElevenLabs голоса
  const elevenlabsVoices: Voice[] = ELEVENLABS_VOICES.map(voice => ({
    id: voice.id,
    name: voice.name,
    type: 'system' as const,
    category: 'ElevenLabs'
  }));

  // Определяем, какие голоса показывать в зависимости от модели
  const isElevenLabsModel = model?.includes('elevenlabs');
  const availableSystemVoices = isElevenLabsModel ? elevenlabsVoices : systemVoices;

  // Загрузка голосов пользователя и сообщества
  useEffect(() => {
    if (isOpen && user) {
      loadVoices();
    }
  }, [isOpen, user]);

  const loadVoices = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Загружаем голоса пользователя
      const userVoicesData = await getUserVoices(user.id);
      const formattedUserVoices: Voice[] = userVoicesData.map(voice => ({
        id: voice.voice_id,
        name: voice.name,
        type: 'user' as const,
        creator_id: voice.user_id,
        audio_url: voice.audio_url
      }));
      setUserVoices(formattedUserVoices);

      // Загружаем голоса сообщества
      const communityVoicesData = await getCommunityVoices();
      const formattedCommunityVoices: Voice[] = communityVoicesData
        .filter(voice => voice.user_id !== user.id) // Исключаем свои голоса
        .map(voice => ({
          id: voice.voice_id,
          name: voice.name,
          type: 'community' as const,
          creator_id: voice.user_id,
          audio_url: voice.audio_url,
          usage_count: voice.usage_count || 0
        }));
      setCommunityVoices(formattedCommunityVoices);

    } catch (error) {
      console.error('Error loading voices:', error);
      toast.error('Ошибка загрузки голосов');
    } finally {
      setIsLoading(false);
    }
  };

  // Воспроизведение демо голоса
  const playVoiceDemo = async (voice: Voice) => {
    // Останавливаем текущее воспроизведение
    if (audioElement) {
      audioElement.pause();
      setAudioElement(null);
      setPlayingVoice(null);
    }

    // Для системных голосов пока нет демо
    if (voice.type === 'system') {
      toast.info('Демо для системных голосов пока недоступно');
      return;
    }

    if (!voice.audio_url) {
      toast.error('Демо недоступно для этого голоса');
      return;
    }

    try {
      const audio = new Audio(voice.audio_url);
      setAudioElement(audio);
      setPlayingVoice(voice.id);

      audio.addEventListener('ended', () => {
        setPlayingVoice(null);
        setAudioElement(null);
      });

      audio.addEventListener('error', () => {
        toast.error('Ошибка воспроизведения демо');
        setPlayingVoice(null);
        setAudioElement(null);
      });

      await audio.play();
    } catch (error) {
      console.error('Demo play error:', error);
      toast.error('Ошибка воспроизведения демо');
      setPlayingVoice(null);
      setAudioElement(null);
    }
  };

  // Остановка воспроизведения
  const stopVoiceDemo = () => {
    if (audioElement) {
      audioElement.pause();
      setAudioElement(null);
      setPlayingVoice(null);
    }
  };

  // Выбор голоса
  const handleVoiceSelect = (voice: Voice) => {
    onVoiceSelect(voice);
    setIsOpen(false);
    stopVoiceDemo();
  };

  // Компонент карточки голоса
  const VoiceCard: React.FC<{ voice: Voice }> = ({ voice }) => {
    const isSelected = selectedVoice?.id === voice.id;
    const isPlaying = playingVoice === voice.id;

    // Используем градиент из ElevenLabs голосов или генерируем для других
    const gradient = voice.category === 'ElevenLabs'
      ? ELEVENLABS_VOICES.find(v => v.id === voice.id)?.gradient || generateGradientAvatar(voice.name)
      : generateGradientAvatar(voice.name);

    return (
      <div
        className={cn(
          "p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md",
          isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"
        )}
        onClick={() => handleVoiceSelect(voice)}
      >
        <div className="flex items-center gap-3">
          {/* Аватар */}
          <div
            className="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-sm"
            style={{ background: gradient }}
          >
            {voice.name.charAt(0).toUpperCase()}
          </div>

          {/* Информация о голосе */}
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm truncate">{voice.name}</div>
            <div className="flex items-center gap-2 mt-1">
              {voice.category && (
                <Badge variant="secondary" className="text-xs">
                  {voice.category}
                </Badge>
              )}
              {voice.type === 'community' && voice.usage_count !== undefined && (
                <span className="text-xs text-foreground/60">
                  {voice.usage_count} использований
                </span>
              )}
            </div>
          </div>

          {/* Кнопка демо */}
          {(voice.type === 'user' || voice.type === 'community') && voice.audio_url && (
            <Button
              variant="ghost"
              size="sm"
              className="flex-shrink-0 p-1"
              onClick={(e) => {
                e.stopPropagation();
                if (isPlaying) {
                  stopVoiceDemo();
                } else {
                  playVoiceDemo(voice);
                }
              }}
            >
              {isPlaying ? <Pause size={16} /> : <Play size={16} />}
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={cn("justify-start", className)}
        >
          <Mic size={16} className="mr-2" />
          {selectedVoice ? selectedVoice.name : t('speechGen.selectVoice')}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[80vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle>{t('speechGen.voiceLibrary.title')}</DialogTitle>
          <DialogDescription>
            Выберите голос для генерации речи из доступных категорий
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="default" className="flex-1">
          <div className="px-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="default" className="flex items-center gap-2">
                <Mic size={16} />
                {t('speechGen.voiceLibrary.default')}
              </TabsTrigger>
              <TabsTrigger value="yours" className="flex items-center gap-2">
                <User size={16} />
                {t('speechGen.voiceLibrary.yours')}
              </TabsTrigger>
              <TabsTrigger value="community" className="flex items-center gap-2">
                <Users size={16} />
                {t('speechGen.voiceLibrary.community')}
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 px-6 pb-6">
            {/* Системные голоса */}
            <TabsContent value="default" className="mt-4">
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-2">
                  {availableSystemVoices.map((voice) => (
                    <VoiceCard key={voice.id} voice={voice} />
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Голоса пользователя */}
            <TabsContent value="yours" className="mt-4">
              <ScrollArea className="h-[400px] pr-4">
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                  </div>
                ) : userVoices.length > 0 ? (
                  <div className="space-y-2">
                    {userVoices.map((voice) => (
                      <VoiceCard key={voice.id} voice={voice} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-foreground/60 py-8">
                    <Mic size={48} className="mx-auto mb-4 opacity-50" />
                    <p>{t('speechGen.voiceLibrary.noUserVoices')}</p>
                    <p className="text-sm mt-2">
                      {t('speechGen.voiceLibrary.createFirstVoice')}
                    </p>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            {/* Голоса сообщества */}
            <TabsContent value="community" className="mt-4">
              <ScrollArea className="h-[400px] pr-4">
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                  </div>
                ) : communityVoices.length > 0 ? (
                  <div className="space-y-2">
                    {communityVoices.map((voice) => (
                      <VoiceCard key={voice.id} voice={voice} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-foreground/60 py-8">
                    <Users size={48} className="mx-auto mb-4 opacity-50" />
                    <p>{t('speechGen.voiceLibrary.noCommunityVoices')}</p>
                    <p className="text-sm mt-2">
                      {t('speechGen.voiceLibrary.beFirstToShare')}
                    </p>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default VoiceSelector;