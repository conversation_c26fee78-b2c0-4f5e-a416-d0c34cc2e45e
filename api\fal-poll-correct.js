// Правильный фоновый поллинг для FAL API
// POST /api/fal-poll-correct

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL POLL CORRECT] ${req.method} request received`);

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility = 'public', cost } = req.body;

    console.log('[FAL POLL CORRECT] Starting polling for:', { requestId, model, userId });

    if (!requestId || !model || !userId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Запускаем фоновый процесс polling (не ждем его завершения)
    pollFalRequestCorrect(requestId, model, userId, prompt, visibility, cost)
      .catch(error => {
        console.error(`[FAL POLL CORRECT] Background polling failed for ${requestId}:`, error);
      });

    return res.status(200).json({ message: 'Background polling started' });

  } catch (error) {
    console.error('[FAL POLL CORRECT] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Правильный фоновый polling статуса FAL API с сохранением в БД
 */
async function pollFalRequestCorrect(requestId, model, userId, prompt, visibility, cost) {
  const falKey = process.env.FAL_KEY;
  if (!falKey) {
    console.error('[FAL POLL CORRECT] FAL_KEY not found');
    return;
  }

  const maxAttempts = 120; // 20 минут максимум
  const baseDelay = 10000; // 10 секунд базовая задержка

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`[FAL POLL CORRECT] Checking status (attempt ${attempt}/${maxAttempts}) for ${requestId}`);

      // Проверяем статус используя правильный API
      const statusResponse = await fetch(`https://queue.fal.run/${model}/requests/${requestId}/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Key ${falKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!statusResponse.ok) {
        console.error(`[FAL POLL CORRECT] Status check failed: ${statusResponse.status}`);
        await new Promise(resolve => setTimeout(resolve, baseDelay));
        continue;
      }

      const statusData = await statusResponse.json();
      console.log(`[FAL POLL CORRECT] Status: ${statusData.status} for ${requestId}`);

      if (statusData.status === 'COMPLETED') {
        console.log(`[FAL POLL CORRECT] Generation completed for ${requestId}, fetching result`);

        // Получаем результат используя правильный API
        const resultResponse = await fetch(`https://queue.fal.run/${model}/requests/${requestId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!resultResponse.ok) {
          console.error(`[FAL POLL CORRECT] Result fetch failed: ${resultResponse.status}`);
          return;
        }

        const result = await resultResponse.json();
        console.log(`[FAL POLL CORRECT] Got result for ${requestId}:`, JSON.stringify(result, null, 2));

        if (result.video && result.video.url) {
          // Сохраняем видео в БД
          await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility, requestId);
          console.log(`[FAL POLL CORRECT] Video saved to database for ${requestId}`);
        } else {
          console.error(`[FAL POLL CORRECT] No video URL in result for ${requestId}. Full result:`, result);

          // Проверяем, есть ли ошибка в результате
          if (result.error) {
            console.error(`[FAL POLL CORRECT] Error in result: ${result.error}`);
          }
        }

        return; // Завершаем поллинг
      }

      if (statusData.status === 'FAILED') {
        console.error(`[FAL POLL CORRECT] Generation failed for ${requestId}:`, statusData.error);
        return;
      }

      // Статус все еще "IN_QUEUE" или "IN_PROGRESS", ждем
      const delay = Math.min(baseDelay * Math.pow(1.1, attempt - 1), 30000); // Увеличиваем задержку, максимум 30 сек
      console.log(`[FAL POLL CORRECT] Status: ${statusData.status}, waiting ${delay}ms before next check...`);
      await new Promise(resolve => setTimeout(resolve, delay));

    } catch (error) {
      console.error(`[FAL POLL CORRECT] Error during polling (attempt ${attempt}):`, error);
      await new Promise(resolve => setTimeout(resolve, baseDelay));
    }
  }

  console.error(`[FAL POLL CORRECT] Polling timeout after ${maxAttempts} attempts for ${requestId}`);
}

/**
 * Сохраняет видео в базу данных
 */
async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[FAL POLL CORRECT] Saving video to database: ${videoUrl}`);

    // Скачиваем видео
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[FAL POLL CORRECT] Downloaded video, size: ${videoBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    // Загружаем видео в Supabase Storage
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[FAL POLL CORRECT] Video uploaded to Storage: ${supabaseVideoUrl}`);

    // Генерируем thumbnail
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[FAL POLL CORRECT] Thumbnail uploaded: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[FAL POLL CORRECT] Error generating thumbnail:`, thumbnailError);
    }

    // Определяем стоимость
    let cost = 100;
    if (model.includes('standard')) cost = 75;
    else if (model.includes('pro')) cost = 120;
    else if (model.includes('master')) cost = 320;

    // Сохраняем в БД
    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId,
      duration: 5,
      cost: cost,
      visibility: visibility
    });

    console.log(`[FAL POLL CORRECT] Video generation saved to database with ID: ${savedGeneration.id}`);

    // Автоматически создаем GIF превью для dashboard
    try {
      const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site';
      await fetch(`${baseUrl}/api/video/create-gif`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoUrl: supabaseVideoUrl,
          generationId: savedGeneration.id,
          userId: userId
        })
      });
      console.log(`[FAL POLL CORRECT] GIF creation started for generation ${savedGeneration.id}`);
    } catch (gifError) {
      console.error(`[FAL POLL CORRECT] Error creating GIF thumbnail:`, gifError);
    }

  } catch (error) {
    console.error(`[FAL POLL CORRECT] Error saving video to database:`, error);
    throw error;
  }
}
