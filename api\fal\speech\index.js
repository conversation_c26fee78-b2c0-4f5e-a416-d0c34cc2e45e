// Serverless function for Fal AI Speech API (ElevenLabs TTS models)
// Supports: fal-ai/elevenlabs/tts/turbo-v2.5, fal-ai/elevenlabs/tts/multilingual-v2

import fetch from "node-fetch";
import { updateUserCredits } from "../../utils/database.js";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Функция расчёта стоимости генерации речи для ElevenLabs TTS
function getFalSpeechGenerationCost(model, text) {
  if (!model || !text) return 0;

  const textLength = text.length;

  if (model.includes("elevenlabs/tts/turbo-v2.5")) {
    // 20 кредитов за 1000 символов
    const cost = Math.ceil((textLength / 1000) * 20);
    return Math.max(5, cost); // Минимум 5 кредитов
  }

  if (model.includes("elevenlabs/tts/multilingual-v2")) {
    // 40 кредитов за 1000 символов
    const cost = Math.ceil((textLength / 1000) * 40);
    return Math.max(5, cost); // Минимум 5 кредитов
  }

  return 0;
}

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { model, input, userId } = req.body;

    console.log('[FAL SPEECH] Received request:', { model, userId, inputKeys: Object.keys(input || {}) });

    if (!model) {
      return res.status(400).json({ error: 'Model is required' });
    }

    if (!input?.text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL SPEECH] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Рассчитываем стоимость
    const cost = getFalSpeechGenerationCost(model, input.text);
    console.log(`[FAL SPEECH] Generation cost: ${cost} credits`);

    // Списываем кредиты, если указан userId
    if (userId && cost > 0) {
      try {
        console.log(`[FAL SPEECH] Deducting ${cost} credits from user ${userId}`);
        await updateUserCredits(userId, -cost, 'generation', `Fal Speech Generation (${model})`);
      } catch (creditError) {
        console.error('[FAL SPEECH] Error deducting credits:', creditError);
        return res.status(400).json({ error: 'Insufficient credits or credit deduction failed' });
      }
    }

    // Подготавливаем input для Fal API
    let processedInput = { ...input };

    // Удаляем параметры, которые не нужны для Fal API
    delete processedInput.userId;
    delete processedInput.metadata_visibility;

    console.log('[FAL SPEECH] Sending request to Fal API:', { model, input: processedInput });

    // Добавляем webhook URL для получения результата
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site';
    const webhookUrl = `${baseUrl}/api/fal/webhook`;

    // Добавляем метаданные для webhook'а
    const requestBody = {
      ...processedInput,
      webhook_url: webhookUrl,
      metadata: {
        userId: userId,
        text: processedInput.text,
        voice: processedInput.voice || 'Rachel',
        model: model,
        visibility: input.metadata_visibility || 'public'
      }
    };

    console.log('[FAL SPEECH] Request body with webhook:', requestBody);

    // Отправляем запрос к Fal API
    // FAL API для ElevenLabs TTS ожидает параметры напрямую, а не в объекте input
    const falResponse = await fetch(`https://queue.fal.run/${model}`, {
      method: 'POST',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL SPEECH] API error:', errorText);

      // Возвращаем кредиты в случае ошибки
      if (userId && cost > 0) {
        try {
          await updateUserCredits(userId, cost, 'refund', `Fal Speech API Error Refund (${model})`);
          console.log(`[FAL SPEECH] Refunded ${cost} credits to user ${userId}`);
        } catch (refundError) {
          console.error('[FAL SPEECH] Error refunding credits:', refundError);
        }
      }

      return res.status(falResponse.status).json({
        error: 'Fal API error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL SPEECH] API response:', result);

    // Не запускаем фоновый polling - используем реальное время генерации
    console.log(`[FAL SPEECH] Request submitted successfully: ${result.request_id}`);

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL SPEECH] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
