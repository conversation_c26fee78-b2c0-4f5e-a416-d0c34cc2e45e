import React from 'react';
import { useTranslation } from 'react-i18next';

const ServiceAgreement: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Договор оказания услуг</h1>
      
      <div className="prose prose-lg max-w-none">
        <p className="mb-4">Дата последнего обновления: 15 июля 2024 г.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">1. Общие положения</h2>
        <p>Настоящий Договор оказания услуг (далее – "Договор") заключается между Самозанятым Богданом Евгеньевичем Друковским, ИНН: 550147543516 (далее – "Исполнитель") и физическим или юридическим лицом, акцептовавшим настоящую оферту (далее – "Заказчик").</p>
        <p>Настоящий Договор является публичной офертой и определяет условия использования сервиса UMA AI (далее – "Сервис").</p>
        <p>Акцептом настоящей оферты является регистрация на Сервисе и/или внесение платы за использование Сервиса.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">2. Предмет договора</h2>
        <p>Исполнитель предоставляет Заказчику доступ к Сервису для генерации изображений и видео с использованием технологий искусственного интеллекта, а Заказчик обязуется принять и оплатить услуги в соответствии с условиями настоящего Договора.</p>
        <p>Объем предоставляемых услуг, их стоимость и сроки выполнения определяются выбранным Заказчиком тарифным планом, указанным на сайте Сервиса.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">3. Права и обязанности сторон</h2>
        <h3 className="text-xl font-semibold mt-6 mb-2">3.1. Исполнитель обязуется:</h3>
        <ul className="list-disc pl-6 mb-4">
          <li>Предоставить Заказчику доступ к Сервису после оплаты выбранного тарифного плана.</li>
          <li>Обеспечить круглосуточную доступность Сервиса, за исключением времени проведения профилактических работ.</li>
          <li>Обеспечивать техническую поддержку Заказчика по вопросам работы Сервиса.</li>
          <li>Обеспечивать конфиденциальность информации, предоставленной Заказчиком.</li>
          <li>Своевременно информировать Заказчика об изменениях в работе Сервиса.</li>
        </ul>
        
        <h3 className="text-xl font-semibold mt-6 mb-2">3.2. Исполнитель имеет право:</h3>
        <ul className="list-disc pl-6 mb-4">
          <li>Временно приостанавливать работу Сервиса для проведения профилактических работ.</li>
          <li>Изменять функциональность Сервиса без предварительного уведомления Заказчика.</li>
          <li>Отказать в предоставлении услуг Заказчику в случае нарушения им условий настоящего Договора.</li>
          <li>Использовать обезличенные данные о генерациях для улучшения алгоритмов Сервиса.</li>
        </ul>
        
        <h3 className="text-xl font-semibold mt-6 mb-2">3.3. Заказчик обязуется:</h3>
        <ul className="list-disc pl-6 mb-4">
          <li>Предоставлять достоверную информацию при регистрации на Сервисе.</li>
          <li>Своевременно оплачивать услуги в соответствии с выбранным тарифным планом.</li>
          <li>Не передавать свои учетные данные третьим лицам.</li>
          <li>Не использовать Сервис для создания контента, нарушающего законодательство РФ или права третьих лиц.</li>
          <li>Не предпринимать действий, направленных на нарушение нормальной работы Сервиса.</li>
        </ul>
        
        <h3 className="text-xl font-semibold mt-6 mb-2">3.4. Заказчик имеет право:</h3>
        <ul className="list-disc pl-6 mb-4">
          <li>Использовать Сервис в соответствии с его функциональным назначением.</li>
          <li>Получать техническую поддержку по вопросам работы Сервиса.</li>
          <li>Использовать сгенерированный контент в личных и коммерческих целях.</li>
          <li>Отказаться от использования Сервиса в любое время.</li>
        </ul>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">4. Стоимость услуг и порядок расчетов</h2>
        <p>Стоимость услуг определяется в соответствии с выбранным Заказчиком тарифным планом, информация о котором размещена на сайте Сервиса.</p>
        <p>Оплата услуг производится Заказчиком путем 100% предоплаты.</p>
        <p>Оплата производится в российских рублях безналичным способом через платежные системы, указанные на сайте Сервиса.</p>
        <p>Исполнитель не является плательщиком НДС в связи с применением специального налогового режима для самозанятых.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">5. Ответственность сторон</h2>
        <p>Исполнитель не несет ответственности за содержание и последствия использования контента, созданного Заказчиком с помощью Сервиса.</p>
        <p>Исполнитель не гарантирует, что Сервис будет соответствовать всем требованиям Заказчика, а также не гарантирует бесперебойную работу Сервиса.</p>
        <p>Заказчик несет полную ответственность за соблюдение законодательства РФ при использовании Сервиса и сгенерированного контента.</p>
        <p>В случае нарушения Заказчиком условий настоящего Договора, Исполнитель имеет право приостановить или прекратить доступ Заказчика к Сервису без возврата оплаченных средств.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">6. Интеллектуальная собственность</h2>
        <p>Исключительные права на Сервис, включая программное обеспечение, дизайн, торговые марки и другие объекты интеллектуальной собственности, принадлежат Исполнителю.</p>
        <p>Заказчик получает неисключительное право использования сгенерированного с помощью Сервиса контента без ограничений, включая коммерческое использование.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">7. Срок действия и расторжение договора</h2>
        <p>Настоящий Договор вступает в силу с момента акцепта оферты Заказчиком и действует до полного исполнения сторонами своих обязательств.</p>
        <p>Договор может быть расторгнут по инициативе любой из сторон с предварительным уведомлением другой стороны за 10 календарных дней.</p>
        <p>В случае расторжения Договора по инициативе Заказчика, условия возврата денежных средств определяются Политикой возврата средств, размещенной на сайте Сервиса.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">8. Заключительные положения</h2>
        <p>Все споры и разногласия, возникающие между сторонами по настоящему Договору, решаются путем переговоров. В случае невозможности достижения согласия, споры подлежат рассмотрению в суде по месту нахождения Исполнителя.</p>
        <p>Исполнитель оставляет за собой право вносить изменения в настоящий Договор с уведомлением Заказчика путем размещения новой редакции Договора на сайте Сервиса.</p>
        <p>Во всем, что не предусмотрено настоящим Договором, стороны руководствуются действующим законодательством РФ.</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">9. Контактная информация</h2>
        <p><strong>Исполнитель:</strong> Самозанятый Друковский Богдан Евгеньевич</p>
        <p><strong>ИНН:</strong> 550147543516</p>
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Телефон:</strong> +79509540097</p>
      </div>
    </div>
  );
};

export default ServiceAgreement;