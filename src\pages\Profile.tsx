import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import AppLayout from '@/components/layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { getUserCredits } from '@/utils/database';
import { Coins, User, Mail, Key } from 'lucide-react';
import { BuyCreditsDialog } from '@/components/ui/BuyCreditsDialog';

const Profile = () => {
  const { t, i18n } = useTranslation();
  const { user, signOut } = useAuth();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [credits, setCredits] = useState<number>(0);
  const [isLoadingCredits, setIsLoadingCredits] = useState(false);
  
  // Загрузка данных пользователя
  useEffect(() => {
    if (user) {
      setEmail(user.email || '');
      setName(user.user_metadata?.full_name || user.email?.split('@')[0] || '');
      loadUserCredits();
    }
  }, [user]);
  
  // Загрузка баланса кредитов
  const loadUserCredits = async () => {
    if (user?.id) {
      setIsLoadingCredits(true);
      try {
        const credits = await getUserCredits(user.id);
        setCredits(credits);
      } catch (error) {
        console.error('Ошибка при загрузке кредитов:', error);
        
        // При сетевых ошибках не изменяем значение
        if (!(error instanceof Error && error.message.includes('Network error'))) {
          setCredits(0);
        }
      } finally {
        setIsLoadingCredits(false);
      }
    }
  };
  
  // Слушатель события секретного кода из внешнего скрипта
  useEffect(() => {
    const handleSecretCode = (event: any) => {
      addSecretTokens(event?.detail?.credits || 300);
    };
    
    window.addEventListener('secretCodeActivated', handleSecretCode);
    
    return () => {
      window.removeEventListener('secretCodeActivated', handleSecretCode);
    };
  }, []);
  
  // Функция для секретного начисления токенов
  const addSecretTokens = async (amount = 300) => {
    if (!user?.id) return;
    
    try {
      setIsLoadingCredits(true);

      // Используем напрямую функцию из utils/database вместо API запроса
      const { updateUserCredits } = await import('@/utils/database');
      const newBalance = await updateUserCredits(
        user.id, 
        amount, 
        'secret_code', 
        'Начисление за секретный код'
      );
      
      setCredits(newBalance);
      
      toast.success(i18n.language === 'ru' 
        ? `Вы получили ${amount} токенов!` 
        : `You received ${amount} tokens!`, 
        { 
          description: i18n.language === 'ru' 
            ? 'Секретная комбинация сработала' 
            : 'Secret code activated' 
        });
    } catch (error) {
      console.error('Ошибка при начислении токенов:', error);
      toast.error(error instanceof Error 
        ? error.message 
        : (i18n.language === 'ru' 
          ? 'Не удалось начислить токены' 
          : 'Failed to add tokens'));
    } finally {
      setIsLoadingCredits(false);
    }
  };
  
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Ошибка при выходе:', error);
      toast.error(i18n.language === 'ru' ? 'Ошибка при выходе' : 'Error signing out');
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8">{t('navigation.profile')}</h1>
          
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  {i18n.language === 'ru' ? 'Профиль пользователя' : 'User Profile'}
                </div>
              </CardTitle>
              <CardDescription>
                {i18n.language === 'ru' 
                  ? 'Управление вашим профилем и настройками аккаунта' 
                  : 'Manage your profile and account settings'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{i18n.language === 'ru' ? 'Имя' : 'Name'}</Label>
                <Input 
                  id="name" 
                  value={name} 
                  onChange={(e) => setName(e.target.value)} 
                  disabled 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    Email
                  </div>
                </Label>
                      <Input
                  id="email" 
                  value={email} 
                  onChange={(e) => setEmail(e.target.value)} 
                  disabled 
                />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex items-center gap-2">
                  <Coins className="w-5 h-5" />
                  {i18n.language === 'ru' ? 'Баланс токенов' : 'Token Balance'}
                </div>
              </CardTitle>
              <CardDescription>
                {i18n.language === 'ru' 
                  ? 'Управление вашими токенами для генерации' 
                  : 'Manage your generation tokens'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-4">
                {isLoadingCredits ? (
                  <span className="text-muted-foreground">{i18n.language === 'ru' ? 'Загрузка...' : 'Loading...'}</span>
                ) : (
                  <span>{credits} {i18n.language === 'ru' ? 'токенов' : 'tokens'}</span>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {i18n.language === 'ru' 
                  ? 'Используйте токены для генерации изображений и видео. Возможно пополнение баланса.' 
                  : 'Use tokens to generate images and videos. You can replenish your balance.'}
              </p>
              <div className="flex gap-2">
              <Button variant="outline" onClick={loadUserCredits} disabled={isLoadingCredits}>
                {i18n.language === 'ru' ? 'Обновить баланс' : 'Refresh Balance'}
              </Button>
                <BuyCreditsDialog onSuccessfulPurchase={loadUserCredits} />
              </div>
            </CardContent>
          </Card>
            </div>
            
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>
              <div className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                {i18n.language === 'ru' ? 'Управление аккаунтом' : 'Account Management'}
              </div>
            </CardTitle>
            <CardDescription>
              {i18n.language === 'ru' 
                ? 'Безопасность и настройки доступа к аккаунту' 
                : 'Security and access settings for your account'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Button 
                variant="destructive" 
                className="w-auto" 
                onClick={handleSignOut}
              >
              {i18n.language === 'ru' ? 'Выйти из аккаунта' : 'Sign Out'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default Profile;
