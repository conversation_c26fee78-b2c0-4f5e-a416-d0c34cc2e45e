
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Home } from 'lucide-react';
import { Link } from 'react-router-dom';
import AnimatedGradient from '@/components/ui/animated-gradient';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-uma-black text-white p-6 relative">
      <AnimatedGradient />
      
      <div className="text-center z-10">
        <h1 className="text-7xl font-bold mb-6 text-gradient-purple">404</h1>
        <h2 className="text-3xl font-bold mb-4">Page Not Found</h2>
        <p className="text-white/70 mb-8 max-w-md mx-auto">
          The page you are looking for doesn't exist or has been moved.
        </p>
        
        <Link to="/">
          <Button className="bg-uma-purple hover:bg-uma-purple-light text-white">
            <Home className="mr-2" size={18} />
            Return to Home
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
