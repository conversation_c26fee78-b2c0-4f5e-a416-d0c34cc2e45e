import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface MasonryGridProps {
  children: React.ReactNode[];
  className?: string;
  columnCount?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
}

const MasonryGrid: React.FC<MasonryGridProps> = ({
  children,
  className,
  columnCount = { default: 1, sm: 2, md: 3, lg: 4, xl: 5 },
  gap = 16
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [columns, setColumns] = useState<React.ReactNode[][]>([]);
  const [currentColumnCount, setCurrentColumnCount] = useState(columnCount.default);

  // Функция для определения количества колонок на основе ширины экрана
  const getColumnCount = () => {
    if (typeof window === 'undefined') return columnCount.default;
    
    const width = window.innerWidth;
    if (width >= 1280 && columnCount.xl) return columnCount.xl; // xl
    if (width >= 1024 && columnCount.lg) return columnCount.lg; // lg
    if (width >= 768 && columnCount.md) return columnCount.md; // md
    if (width >= 640 && columnCount.sm) return columnCount.sm; // sm
    return columnCount.default;
  };

  // Функция для распределения элементов по колонкам
  const distributeItems = (items: React.ReactNode[], colCount: number) => {
    const cols: React.ReactNode[][] = Array.from({ length: colCount }, () => []);
    
    // Распределяем элементы по порядку: первые colCount элементов идут в первую строку,
    // следующие colCount - во вторую строку и так далее
    items.forEach((item, index) => {
      const columnIndex = index % colCount;
      cols[columnIndex].push(item);
    });
    
    return cols;
  };

  // Обновляем колонки при изменении детей или количества колонок
  useEffect(() => {
    const colCount = getColumnCount();
    setCurrentColumnCount(colCount);
    
    if (children && children.length > 0) {
      const newColumns = distributeItems(children, colCount);
      setColumns(newColumns);
    }
  }, [children, columnCount]);

  // Обработчик изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      const newColCount = getColumnCount();
      if (newColCount !== currentColumnCount) {
        setCurrentColumnCount(newColCount);
        if (children && children.length > 0) {
          const newColumns = distributeItems(children, newColCount);
          setColumns(newColumns);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [children, currentColumnCount, columnCount]);

  return (
    <div
      ref={containerRef}
      className={cn('flex', className)}
      style={{ gap: `${gap}px` }}
    >
      {columns.map((column, columnIndex) => (
        <div
          key={columnIndex}
          className="flex-1 flex flex-col"
          style={{ gap: `${gap}px` }}
        >
          {column.map((item, itemIndex) => (
            <div key={`${columnIndex}-${itemIndex}`}>
              {item}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default MasonryGrid;
