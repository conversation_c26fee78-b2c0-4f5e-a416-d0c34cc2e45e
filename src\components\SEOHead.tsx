import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
}

const SEOHead = ({
  title,
  description,
  keywords,
  image = 'https://umaai.site/images/og-image.jpg',
  url,
  type = 'website',
  noIndex = false
}: SEOHeadProps) => {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Обновляем title
    if (title) {
      document.title = title;
    }

    // Обновляем или создаем meta теги
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Основные meta теги
    if (description) {
      updateMetaTag('description', description);
    }
    
    if (keywords) {
      updateMetaTag('keywords', keywords);
    }

    if (noIndex) {
      updateMetaTag('robots', 'noindex, nofollow');
    } else {
      updateMetaTag('robots', 'index, follow');
    }

    // Open Graph теги
    if (title) {
      updateMetaTag('og:title', title, true);
    }
    
    if (description) {
      updateMetaTag('og:description', description, true);
    }
    
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:site_name', 'UMA.AI', true);
    updateMetaTag('og:locale', i18n.language === 'ru' ? 'ru_RU' : 'en_US', true);
    
    if (url) {
      updateMetaTag('og:url', url, true);
    }

    // Twitter Card теги
    if (title) {
      updateMetaTag('twitter:title', title);
    }
    
    if (description) {
      updateMetaTag('twitter:description', description);
    }
    
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:image', image);
    updateMetaTag('twitter:site', '@umaai_official');

    // Canonical URL
    if (url) {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      canonical.setAttribute('href', url);
    }

    // Обновляем lang атрибут
    document.documentElement.lang = i18n.language === 'ru' ? 'ru' : 'en';

  }, [title, description, keywords, image, url, type, noIndex, i18n.language]);

  return null; // Этот компонент не рендерит ничего видимого
};

export default SEOHead;
