import { toast } from 'sonner';

export interface StabilityImageGenerationOptions {
  prompt: string;
  negativePrompt?: string;
  width?: number;
  height?: number;
  stylePreset?: string;
  cfgScale?: number;
  steps?: number;
  numberOfImages?: number;
  engine?: string;
  userId?: string;  // Добавляем userId для отслеживания кредитов
}

export interface GeneratedImage {
  url: string;
  width: number;
  height: number;
  prompt: string;
  negativePrompt?: string;
  stylePreset?: string;
  model?: string;
  aspectRatio?: string; // Добавлено для поддержки соотношения сторон
}

const DEFAULT_ENGINE_ID = "stable-diffusion-xl-1024-v1-0";

// Базовый URL API
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

/**
 * Generates images using the Stability AI API through backend proxy
 */
export const generateImageWithStability = async (
  options: StabilityImageGenerationOptions
): Promise<GeneratedImage[]> => {
  try {
    const {
      prompt,
      negativePrompt = "",
      width = 1024,
      height = 1024,
      stylePreset = "photographic",
      cfgScale = 7,
      steps = 30,
      numberOfImages = 1,
      engine = DEFAULT_ENGINE_ID,
      userId
    } = options;

    console.log('Starting image generation with Stability AI:', { prompt, width, height, stylePreset, numberOfImages });

    // Ограничиваем количество изображений до допустимого диапазона
    const targetCount = Math.max(1, Math.min(4, numberOfImages));
    const results: GeneratedImage[] = [];

    // Создаем запрос к бэкенд-прокси
    const response = await fetch(`${API_URL}/api/stability/image-generation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt,
        negative_prompt: negativePrompt,
        engine,
        width,
        height,
        steps,
        cfg_scale: cfgScale,
        sampler: 'K_EULER',
        style_preset: stylePreset,
        userId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error response from Stability API proxy:', errorData);
      throw new Error(errorData.error || errorData.message || 'Failed to generate image');
    }

    const data = await response.json();
    
    if (!data.artifacts || data.artifacts.length === 0) {
      throw new Error('No images were generated');
    }

    // Преобразуем base64 в URL и добавляем в результаты
    for (const artifact of data.artifacts) {
      const base64Data = artifact.base64;
      const imageUrl = `data:image/png;base64,${base64Data}`;
      
      results.push({
        url: imageUrl,
        width,
        height,
        prompt,
        negativePrompt,
        stylePreset,
        model: engine
      });
    }

    console.log(`Successfully generated ${data.artifacts.length} images with Stability AI`);
    
    return results;
  } catch (error) {
    console.error('Error in generateImageWithStability:', error);
    toast.error(`Ошибка генерации изображения: ${error.message || 'Неизвестная ошибка'}`);
    throw error;
  }
};

export default generateImageWithStability;