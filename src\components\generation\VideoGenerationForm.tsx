import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Select, SelectItem } from "../ui/select";

const ASPECT_RATIOS = [
  "1:1", "3:4", "4:3", "9:16", "16:9", "9:21", "21:9"
];


const DURATIONS = [5, 9];

const CAMERA_CONCEPTS = [
  "truck_left", "pan_right", "pedestal_down", "low_angle", "pedestal_up", "selfie", "pan_left", "roll_right", "zoom_in", "over_the_shoulder", "orbit_right", "orbit_left", "static", "tiny_planet", "high_angle", "bolt_cam", "dolly_zoom", "overhead", "zoom_out", "handheld", "roll_left", "pov", "aerial_drone", "push_in", "crane_down", "truck_right", "tilt_down", "elevator_doors", "tilt_up", "ground_level", "pull_out", "aerial", "crane_up", "eye_level"
];

export interface VideoGenerationFormValues {
  prompt: string;
  start_image_url?: string;
  end_image_url?: string;
  duration: number;
  aspect_ratio: string;
  loop: boolean;
  concepts: string[];
}

const initialState: VideoGenerationFormValues = {
  prompt: "",
  start_image_url: "",
  end_image_url: "",
  duration: 5,
  aspect_ratio: "16:9",
  loop: false,
  concepts: [],
};

export const VideoGenerationForm: React.FC<{ onSubmit: (values: VideoGenerationFormValues) => void }> = ({ onSubmit }) => {
  const [values, setValues] = useState<VideoGenerationFormValues>(initialState);

  const handleChange = (field: keyof VideoGenerationFormValues, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleConceptChange = (concept: string) => {
    setValues((prev) => ({
      ...prev,
      concepts: prev.concepts.includes(concept)
        ? prev.concepts.filter((c) => c !== concept)
        : [...prev.concepts, concept],
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(values);
  };

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: 480, margin: "0 auto", display: "flex", flexDirection: "column", gap: 16 }}>
      <label>
        Prompt
        <Textarea
          value={values.prompt}
          onChange={(e) => handleChange("prompt", e.target.value)}
          required
          placeholder="Text prompt for video generation"
        />
      </label>
      <label>
        Start Image Url
        <Input
          value={values.start_image_url}
          onChange={(e) => handleChange("start_image_url", e.target.value)}
          placeholder="URL of an image to use as the starting frame"
        />
      </label>
      <label>
        End Image Url
        <Input
          value={values.end_image_url}
          onChange={(e) => handleChange("end_image_url", e.target.value)}
          placeholder="URL of an image to use as the ending frame"
        />
      </label>
      <label>
        Duration
        <Select
          value={String(values.duration)}
          onValueChange={(v) => handleChange("duration", Number(v))}
        >
          {DURATIONS.map((d) => (
            <SelectItem key={d} value={String(d)}>
              {d} сек
            </SelectItem>
          ))}
        </Select>
      </label>
      <label>
        Aspect Ratio
        <Select
          value={values.aspect_ratio}
          onValueChange={(v) => handleChange("aspect_ratio", v)}
        >
          {ASPECT_RATIOS.map((ar) => (
            <SelectItem key={ar} value={ar}>
              {ar}
            </SelectItem>
          ))}
        </Select>
      </label>
      <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
        <Checkbox
          checked={values.loop}
          onCheckedChange={(v) => handleChange("loop", !!v)}
        />
        Loop (smooth playback)
      </label>
      <label>
        Concepts (камера)
        <div style={{ display: "flex", flexWrap: "wrap", gap: 8, maxHeight: 120, overflowY: "auto" }}>
          {CAMERA_CONCEPTS.map((concept) => (
            <label key={concept} style={{ display: "flex", alignItems: "center", gap: 4 }}>
              <input
                type="checkbox"
                checked={values.concepts.includes(concept)}
                onChange={() => handleConceptChange(concept)}
              />
              {concept}
            </label>
          ))}
        </div>
      </label>
      <Button type="submit">Сгенерировать видео</Button>
    </form>
  );
};

export default VideoGenerationForm;