import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import AppLayout from '@/components/layout/AppLayout';
import { getUserHistoryImageGenerations, PAGE_SIZE_HISTORY } from '@/utils/database';
import { getVideoGenerations } from '@/utils/database';
import { useAuth } from '@/context/AuthContext';
import { LazyImage } from '@/components/ui/LazyImage';
import ImageModal from '@/components/ui/ImageModal';
import { cn } from '@/lib/utils';
import { ImageIcon, Download, Calendar } from 'lucide-react';
import DottedBackground from '@/components/ui/dotted-background';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ru, enUS } from 'date-fns/locale'; // Added enUS
import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge'; // Badge is not used in GenerationGridItem
import { Skeleton } from '@/components/ui/skeleton'; // Added Skeleton

interface GenerationGroup {
  id: string;
  prompt: string;
  images: string[];
  timestamp: Date;
  style?: string;
  aspect_ratio?: string;
  model?: string;
  user_id?: string;
  type?: 'image' | 'video';
  thumbnail_url?: string;
}

// Adapted from Dashboard.tsx
const HistoryGenerationGridItem = ({ item, onPreview }: { item: GenerationGroup, onPreview: (id: string) => void }) => {
  const { t, i18n } = useTranslation();
  const displayLocale = i18n.language === 'ru' ? ru : enUS;
  const firstMedia = item.images && item.images.length > 0 ? item.images[0] : '/placeholder.svg';
  const isVideo = item.type === 'video' || (firstMedia && firstMedia.endsWith('.mp4'));

  const handleDownloadMedia = useCallback(async (event: React.MouseEvent) => {
    event.stopPropagation();
    if (!item.images || item.images.length === 0) return;
    const mediaUrl = firstMedia;
    const promptPart = item.prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = new Date().toISOString().slice(0, 10);
    const fileName = `uma_ai_history_${promptPart}_${timestamp}.${isVideo ? 'mp4' : 'png'}`;

    try {
      const response = await fetch(mediaUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch media: ${response.statusText}`);
      }
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
      toast.success(t('dashboard.modal.downloadStarted', isVideo ? 'Video download started!' : 'Image download started!'));
    } catch (error) {
      console.error('Error downloading media:', error);
      toast.error(t('dashboard.modal.downloadError', isVideo ? 'Failed to download video.' : 'Failed to download image.'));
    }
  }, [item, t, firstMedia, isVideo]);

  return (
    <div
      className="relative group neo-brutalist-white dark:neo-brutalist-black rounded-xl overflow-hidden border border-black/10 dark:border-white/10 shadow-sm cursor-pointer break-inside-avoid mb-3 sm:mb-4"
      style={{ pageBreakInside: 'avoid', breakInside: 'avoid-column' }}
      onClick={() => {
        console.log('[HistoryGenerationGridItem] Clicked. item.id:', item.id); // Логирование item.id
        // Вызываем onPreview с ID элемента
        onPreview(item.id);
      }}
    >
      <div className="relative w-full h-auto">
        {isVideo ? (
          item.thumbnail_url && item.thumbnail_url.endsWith('.gif') ? (
            <img
              src={item.thumbnail_url}
              alt={item.prompt.substring(0, 50) || `Generated video preview for ${item.id}`}
              className="w-full h-auto object-cover rounded-xl bg-black"
              style={{ maxHeight: 320 }}
            />
          ) : (
            <video
              src={firstMedia}
              controls
              className="w-full h-auto object-cover rounded-xl bg-black"
              poster={item.thumbnail_url || undefined}
            />
          )
        ) : (
          <LazyImage
            src={firstMedia}
            alt={item.prompt.substring(0, 50) || `Generated image for ${item.id}`}
            className="w-full h-auto object-cover"
            placeholderClassName="w-full aspect-video bg-gray-200 dark:bg-gray-800"
          />
        )}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <Button
            variant="secondary"
            size="icon"
            className="h-9 w-9 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30"
            onClick={handleDownloadMedia}
            title={t('history.downloadImage', isVideo ? "Download Video" : "Download Image")}
          >
            <Download size={16} />
          </Button>
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-2.5 bg-gradient-to-t from-black/70 via-black/50 to-transparent">
          <h3 className="font-semibold text-sm text-white line-clamp-2 leading-tight">
            {item.prompt.length > 60 ? item.prompt.substring(0, 60) + '...' : item.prompt}
          </h3>
          <div className="flex items-center justify-between text-xs text-white/80 mt-1">
            <div className="flex items-center">
              <ImageIcon size={12} className="mr-1" />
              <span>{item.images.length} {item.images.length === 1 ? t('imageGen.image_one', isVideo ? 'video' : 'image') : t('imageGen.image_other', isVideo ? 'videos' : 'images')}</span>
            </div>
            <div className="flex items-center">
              <Calendar size={12} className="mr-1" />
              {format(item.timestamp, 'dd MMM', { locale: displayLocale })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Adapted from Dashboard.tsx
const HistoryGenerationGridItemSkeleton = () => (
  <div className="neo-brutalist-white dark:neo-brutalist-black rounded-xl overflow-hidden border border-black/10 dark:border-white/10 shadow-sm break-inside-avoid mb-3 sm:mb-4">
    <Skeleton className="w-full aspect-video" /> {/* Placeholder aspect ratio */}
    <div className="p-3">
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-3 w-full mb-1" />
    </div>
  </div>
);


const History = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [generations, setGenerations] = useState<GenerationGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Для предпросмотра
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMedia, setModalMedia] = useState<{ url: string, type: 'image' | 'video', prompt: string }[]>([]);
  const [modalAspectRatio, setModalAspectRatio] = useState<string | undefined>(undefined);

  const handlePreview = (id: string) => {
    console.log('[History] handlePreview called with id:', id); // Логирование id
    const generation = generations.find(gen => gen.id === id);

    if (!generation || !generation.images || generation.images.length === 0) {
      console.error('[History] Generation not found or has no images:', id);
      return;
    }

    const media = generation.images.map(url => ({
      url,
      type: generation.type || (typeof url === 'string' && url.endsWith('.mp4') ? 'video' : 'image'),
      prompt: generation.prompt
    }));

    console.log('[History] Formatted media for modal:', media); // Логирование сформированного media

    setModalMedia(media);
    setModalAspectRatio(generation.aspect_ratio);
    setModalOpen(true);
  };

  // Универсальный форматтер для изображений и видео
  const formatApiGenerations = (apiGens: any[], type: 'image' | 'video'): GenerationGroup[] => {
    return (apiGens || []).map((gen: any) => {
      const genTimestamp = gen.created_at || gen.timestamp;
      let media: string[] = [];
      if (type === 'image') {
        if (Array.isArray(gen.image_urls) && gen.image_urls.length > 0) {
          media = gen.image_urls;
        } else if (typeof gen.image_url === 'string' && gen.image_url) {
          media = [gen.image_url];
        } else if (Array.isArray(gen.images) && gen.images.length > 0) {
          media = gen.images;
        }
      } else if (type === 'video') {
        media = gen.video_url ? [gen.video_url] : [];
      }
      console.log('[formatApiGenerations]', { id: gen.id, type, media, raw: gen });
      return {
        id: gen.id,
        prompt: gen.prompt || '',
        images: media,
        timestamp: new Date(genTimestamp || Date.now()),
        style: gen.style,
        aspect_ratio: gen.aspect_ratio,
        model: gen.model,
        user_id: gen.user_id,
        type
      };
    }).filter((gen: GenerationGroup | null) => gen && gen.id && gen.images && gen.images.length > 0);
  };

  const loadGenerations = useCallback(async (page: number, loadMore = false) => {
    if (!user) return;

    if (loadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoading(true);
      setGenerations([]);
    }

    try {
      const [imageGens, videoGens] = await Promise.all([
        getUserHistoryImageGenerations(user.id, page, PAGE_SIZE_HISTORY),
        getVideoGenerations(user.id)
      ]);

      const formattedImages = formatApiGenerations(imageGens, 'image');
      const formattedVideos = formatApiGenerations(videoGens, 'video');

      // Объединяем и сортируем по времени
      const allGenerations = [...formattedImages, ...formattedVideos].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Всегда объединяем, убираем дубликаты и перемешиваем
      const merged = loadMore ? [...generations, ...allGenerations] : allGenerations;
      const unique = Array.from(new Map(merged.map(g => [g.id, g])).values());
      // Перемешиваем генерации для случайного порядка
      // Сортировка по времени создания: новые сверху
      unique.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      if (loadMore) {
        setGenerations(prev => {
          const merged = [...prev, ...allGenerations];
          const unique = Array.from(new Map(merged.map(g => [g.id, g])).values());
          return unique;
        });
        // После объединения и удаления дубликатов, повторно сортируем по времени
        unique.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      } else {
        setGenerations(unique);
        // Сортировка при первой загрузке уже есть на строке 223
      }

      setHasMore(allGenerations.length >= PAGE_SIZE_HISTORY);
      setCurrentPage(page);

    } catch (err) {
      console.error('Error in loadGenerations:', err);
      toast.error(t('general.error'));
      setHasMore(false);
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [user, t]);

  useEffect(() => {
    if (user) {
      loadGenerations(1, false); // Загрузка первой страницы при монтировании или смене пользователя
    }
  }, [user, loadGenerations]); // loadGenerations добавлен в зависимости

  useEffect(() => {
    const handleGenerationsUpdated = (event: any) => {
      if (event.detail && event.detail.userId === user?.id) {
        console.log('Обнаружено обновление генераций в History, обновляем список с первой страницы...');
        loadGenerations(1, false); // Перезагрузка с первой страницы
      }
    };

    window.addEventListener('generationsUpdated', handleGenerationsUpdated);
    return () => {
      window.removeEventListener('generationsUpdated', handleGenerationsUpdated);
    };
  }, [user, loadGenerations]); // loadGenerations добавлен в зависимости

  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadGenerations(currentPage + 1, true);
    }
  };

  return (
    <AppLayout>
      <div className="py-6 sm:py-12 px-4 sm:px-8 relative min-h-screen sm:-ml-[170px]">
        <DottedBackground type="dots" className="opacity-30" />
        
        <div className="w-full relative z-10"> {/* Changed: Removed max-w-4xl and mx-auto, added w-full for clarity */}
          <div className="mb-6 sm:mb-10 text-center">
            <h1 className="text-2xl sm:text-3xl font-bold mb-2 sm:mb-3 text-black dark:text-white">{t('dashboard.history')}</h1>
            <p className="text-black/70 dark:text-white/70 max-w-2xl mx-auto">{t('dashboard.historyDescription')}</p>
          </div>

          <div className={cn(
            "gap-3 sm:gap-4", // Matches Dashboard
            "columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5" // Matches Dashboard
          )}>
            {/* Первоначальная загрузка */}
            {isLoading && generations.length === 0 && (
               Array(PAGE_SIZE_HISTORY).fill(0).map((_, i) => (
                <HistoryGenerationGridItemSkeleton key={`skel-hist-${i}`} />
              ))
            )}

            {/* Отображение генераций */}
            {!isLoading && generations.length > 0 && generations.map((generation) => (
              <HistoryGenerationGridItem key={generation.id} item={generation} onPreview={(id) => {
                // Вызываем handlePreview с ID элемента
                handlePreview(id);
              }} />
            ))}
            <ImageModal
              isOpen={modalOpen}
              onClose={() => setModalOpen(false)}
              images={modalMedia.map(m => ({
                url: m.url,
                aspectRatio: modalAspectRatio,
                type: m.type,
                prompt: m.prompt
              }))}
            />
            {modalOpen && modalMedia.length > 0 && (
              <div className="flex items-center justify-between mt-4 px-2 max-w-2xl mx-auto">
                <span className="text-sm text-muted-foreground break-all">
                  Prompt: {modalMedia[0].prompt}
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    navigator.clipboard.writeText(modalMedia[0].prompt || '');
                    toast.success('Промт скопирован!');
                  }}
                >
                  Copy
                </Button>
              </div>
            )}
            
            {/* Нет генераций после загрузки */}
            {!isLoading && !isLoadingMore && generations.length === 0 && (
              <div className="col-span-full text-center py-16 bg-card dark:bg-black/40 rounded-xl border border-black/10 dark:border-white/10 shadow-sm">
                <div className="flex flex-col items-center justify-center gap-4">
                  <ImageIcon className="w-12 h-12 text-black/20 dark:text-white/20" />
                  <p className="text-xl font-medium">{t('history.noGenerations', 'У вас пока нет генераций.')}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('history.tryCreating', 'Попробуйте создать что-нибудь!')}</p>
                </div>
              </div>
            )}
          </div>

          {/* Кнопка "Загрузить еще" и связанные состояния */}
          <div className="mt-10 text-center">
            {isLoadingMore && (
              <div className="flex items-center justify-center">
                <div className="w-6 h-6 border-t-2 border-b-2 border-primary rounded-full animate-spin mr-3"></div>
                <p className="text-gray-500 dark:text-gray-400">{t('history.loadingMore', 'Загрузка...')}</p>
              </div>
            )}
            {!isLoadingMore && hasMore && generations.length > 0 && !isLoading && (
              <Button
                onClick={handleLoadMore}
                variant="outline"
                className="px-8 py-3"
              >
                {t('history.loadMore', 'Загрузить еще')}
              </Button>
            )}
            {!isLoading && !isLoadingMore && !hasMore && generations.length > 0 && (
               <p className="text-center text-gray-500 dark:text-gray-400 py-4">{t('history.noMoreGenerations', 'Больше генераций нет.')}</p>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default History;
