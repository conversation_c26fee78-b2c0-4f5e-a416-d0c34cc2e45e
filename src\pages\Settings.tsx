import React, { useState } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Lock, Bell, Brush, HardDrive, Trash2, LogOut, ChevronRight } from 'lucide-react';
import MovingImagesBackground from '@/components/ui/moving-images-background';
import { toast } from 'sonner';

const Settings = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [saveHistory, setSaveHistory] = useState(true);
  const [theme, setTheme] = useState('dark');
  const [language, setLanguage] = useState('english');
  const [apiLimits, setApiLimits] = useState('moderate');

  const handleSaveSettings = () => {
    toast.success('Settings saved successfully!');
  };

  return (
    <AppLayout>
      <div className="py-8 px-8 relative min-h-screen">
        <MovingImagesBackground />
        
        <div className="max-w-5xl mx-auto relative z-10">
          <h1 className="text-3xl font-bold mb-8">Settings</h1>
          
          <div className="neo-glass rounded-xl overflow-hidden">
            <Tabs defaultValue="appearance" className="w-full">
              <div className="flex border-b border-white/10">
                <TabsList className="bg-transparent p-0 h-auto border-r border-white/10">
                  <div className="flex flex-col items-stretch w-64 py-6">
                    <TabsTrigger 
                      value="appearance" 
                      className="justify-start px-6 py-3 h-auto rounded-none data-[state=active]:bg-white/5"
                    >
                      <Brush size={18} className="mr-3" />
                      Appearance
                    </TabsTrigger>
                    <TabsTrigger 
                      value="notifications" 
                      className="justify-start px-6 py-3 h-auto rounded-none data-[state=active]:bg-white/5"
                    >
                      <Bell size={18} className="mr-3" />
                      Notifications
                    </TabsTrigger>
                    <TabsTrigger 
                      value="privacy" 
                      className="justify-start px-6 py-3 h-auto rounded-none data-[state=active]:bg-white/5"
                    >
                      <Lock size={18} className="mr-3" />
                      Privacy & Data
                    </TabsTrigger>
                    <TabsTrigger 
                      value="api" 
                      className="justify-start px-6 py-3 h-auto rounded-none data-[state=active]:bg-white/5"
                    >
                      <HardDrive size={18} className="mr-3" />
                      API Usage
                    </TabsTrigger>
                  </div>
                </TabsList>
                
                <div className="flex-1 p-6">
                  <TabsContent value="appearance" className="mt-0">
                    <h2 className="text-xl font-semibold mb-6">Appearance</h2>
                    
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-md font-medium mb-4">Theme</h3>
                        <div className="grid grid-cols-3 gap-4">
                          <div 
                            className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'dark' ? 'border-uma-purple' : 'border-white/10 hover:border-white/30'}`}
                            onClick={() => setTheme('dark')}
                          >
                            <div className="h-20 bg-uma-black rounded-md mb-3"></div>
                            <p className="text-sm font-medium text-white dark:text-white">Dark (Default)</p>
                          </div>
                          <div 
                            className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'light' ? 'border-uma-purple' : 'border-white/10 hover:border-white/30'}`}
                            onClick={() => setTheme('light')}
                          >
                            <div className="h-20 bg-gray-200 rounded-md mb-3"></div>
                            <p className="text-sm font-medium text-white dark:text-white">Light</p>
                          </div>
                          <div 
                            className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'system' ? 'border-uma-purple' : 'border-white/10 hover:border-white/30'}`}
                            onClick={() => setTheme('system')}
                          >
                            <div className="h-20 bg-gradient-to-r from-uma-black to-gray-200 rounded-md mb-3"></div>
                            <p className="text-sm font-medium text-white dark:text-white">System</p>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-md font-medium mb-3">Language</h3>
                        <Select value={language} onValueChange={setLanguage}>
                          <SelectTrigger className="w-64 bg-transparent border-white/10">
                            <SelectValue placeholder="Select language" className="text-white dark:text-white" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="english" className="text-black dark:text-white">English</SelectItem>
                            <SelectItem value="spanish" className="text-black dark:text-white">Spanish</SelectItem>
                            <SelectItem value="french" className="text-black dark:text-white">French</SelectItem>
                            <SelectItem value="german" className="text-black dark:text-white">German</SelectItem>
                            <SelectItem value="japanese" className="text-black dark:text-white">Japanese</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="notifications" className="mt-0">
                    <h2 className="text-xl font-semibold mb-6">Notifications</h2>
                    
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="notifications" className="text-md font-medium">Enable Notifications</Label>
                          <p className="text-sm text-white/60 mt-1">Receive notifications about generations and updates</p>
                        </div>
                        <Switch 
                          id="notifications" 
                          checked={notificationsEnabled} 
                          onCheckedChange={setNotificationsEnabled} 
                        />
                      </div>
                      
                      <div className="flex items-center justify-between opacity-50">
                        <div>
                          <Label htmlFor="email-notifications" className="text-md font-medium">Email Notifications</Label>
                          <p className="text-sm text-white/60 mt-1">Receive email notifications about important updates</p>
                        </div>
                        <Switch id="email-notifications" disabled />
                      </div>
                      
                      <p className="text-sm text-white/60 italic">More notification settings coming soon</p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="privacy" className="mt-0">
                    <h2 className="text-xl font-semibold mb-6">Privacy & Data</h2>
                    
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="save-history" className="text-md font-medium">Save Generation History</Label>
                          <p className="text-sm text-white/60 mt-1">Save your generations to access them later</p>
                        </div>
                        <Switch 
                          id="save-history" 
                          checked={saveHistory} 
                          onCheckedChange={setSaveHistory} 
                        />
                      </div>
                      
                      <div>
                        <Button 
                          variant="destructive" 
                          className="bg-red-900/40 hover:bg-red-900/60"
                        >
                          <Trash2 size={16} className="mr-2" />
                          Clear All Data
                        </Button>
                        <p className="text-sm text-white/60 mt-2">
                          This will permanently delete all your saved generations and preferences.
                        </p>
                      </div>
                      
                      <div>
                        <Label className="text-md font-medium block mb-2">Account Actions</Label>
                        <Button 
                          variant="outline" 
                          className="w-full justify-between border-white/10 mb-2"
                        >
                          <span>Export Your Data</span>
                          <ChevronRight size={16} />
                        </Button>
                        <Button 
                          variant="outline" 
                          className="w-full justify-between text-red-400 border-white/10"
                        >
                          <span className="flex items-center">
                            <LogOut size={16} className="mr-2" />
                            Sign Out
                          </span>
                          <ChevronRight size={16} />
                        </Button>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="api" className="mt-0">
                    <h2 className="text-xl font-semibold mb-6">API Usage</h2>
                    
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-md font-medium mb-3">API Request Limits</h3>
                        <Select value={apiLimits} onValueChange={setApiLimits}>
                          <SelectTrigger className="w-64 bg-transparent border-white/10">
                            <SelectValue placeholder="Select limit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="conservative">Conservative (saves tokens)</SelectItem>
                            <SelectItem value="moderate">Moderate (balanced)</SelectItem>
                            <SelectItem value="generous">Generous (best quality)</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-white/60 mt-2">
                          Controls how many tokens are used in API requests. Higher values give better results but use more of your quota.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="text-md font-medium mb-3">Current Usage</h3>
                        <div className="neo-glass rounded-lg p-4 border border-white/10">
                          <div>
                            <p className="text-sm text-white/70 mb-1">Text Generation</p>
                            <div className="w-full bg-uma-gray-dark h-2 rounded-full">
                              <div className="bg-blue-500 h-2 rounded-full w-[45%]"></div>
                            </div>
                            <p className="text-xs text-white/50 mt-1">45% of monthly quota used</p>
                          </div>
                          
                          <div className="mt-4">
                            <p className="text-sm text-white/70 mb-1">Image Generation</p>
                            <div className="w-full bg-uma-gray-dark h-2 rounded-full">
                              <div className="bg-green-500 h-2 rounded-full w-[20%]"></div>
                            </div>
                            <p className="text-xs text-white/50 mt-1">20% of monthly quota used</p>
                          </div>
                          
                          <div className="mt-4">
                            <p className="text-sm text-white/70 mb-1">Video Generation</p>
                            <div className="w-full bg-uma-gray-dark h-2 rounded-full">
                              <div className="bg-purple-500 h-2 rounded-full w-[70%]"></div>
                            </div>
                            <p className="text-xs text-white/50 mt-1">70% of monthly quota used</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </div>
              </div>
            </Tabs>
          </div>
          
          <div className="flex justify-end mt-6">
            <Button 
              onClick={handleSaveSettings}
              className="bg-uma-purple hover:bg-uma-purple-light"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Settings;
