import React from 'react';
import { motion } from 'framer-motion';

const GridBackground = () => (
  <div 
    className="absolute inset-0 opacity-10"
    style={{
      backgroundImage: 'radial-gradient(circle at 1px 1px, black 1px, transparent 0)',
      backgroundSize: '30px 30px',
    }}
  />
);

const CometEffect = () => {
  const variants = {
    initial: { x: -100, y: -100, opacity: 0 },
    animate: { 
      x: [null, 400],
      y: [null, 400],
      opacity: [0, 1, 0],
      transition: {
        duration: 3,
        ease: "linear",
        repeat: Infinity,
        repeatDelay: Math.random() * 5 + 2
      }
    }
  };

  return (
    <motion.div
      variants={variants}
      initial="initial"
      animate="animate"
      className="absolute w-1 h-1 rounded-full"
      style={{
        background: 'linear-gradient(to right, rgba(74, 222, 128, 0.6), rgba(74, 222, 128, 0))',
        boxShadow: '0 0 4px rgba(74, 222, 128, 0.4)',
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
      }}
    />
  );
};

const GridCometBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <GridBackground />
      {[...Array(3)].map((_, i) => (
        <CometEffect key={i} />
      ))}
    </div>
  );
};

export default GridCometBackground;