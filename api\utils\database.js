// Серверные утилиты для работы с базой данных
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Проверяет существование видео генерации по replicate_prediction_id
 */
export const checkVideoGenerationExists = async (replicatePredictionId) => {
  try {
    if (!replicatePredictionId) return false;

    const { data, error } = await supabase
      .from('video_generations')
      .select('id')
      .eq('replicate_prediction_id', replicatePredictionId)
      .limit(1);

    if (error) {
      console.error('Ошибка при проверке существования видео генерации:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Ошибка при проверке существования видео генерации:', error);
    return false;
  }
};

/**
 * Обновляет кредиты пользователя
 */
export const updateUserCredits = async (userId, amount, transactionType = 'manual', description = '') => {
  try {
    // Получаем текущий баланс
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      throw new Error(`Ошибка получения профиля: ${profileError.message}`);
    }

    const currentCredits = profile?.credits || 0;
    const newCredits = currentCredits + amount;

    if (newCredits < 0) {
      throw new Error('Недостаточно кредитов');
    }

    // Обновляем баланс
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ credits: newCredits })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Ошибка обновления кредитов: ${updateError.message}`);
    }

    // Записываем транзакцию
    const { error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: amount,
        type: transactionType,
        transaction_type: transactionType,
        description: description,
        balance_after: newCredits
      });

    if (transactionError) {
      console.error('Ошибка записи транзакции:', transactionError);
    }

    return newCredits;
  } catch (error) {
    console.error('Ошибка обновления кредитов:', error);
    throw error;
  }
};

/**
 * Загружает аудио в Supabase Storage
 */
export const uploadAudioToSupabase = async (buffer, fileName, bucketName = 'generations') => {
  try {
    console.log(`[uploadAudioToSupabase] Starting upload: bucket=${bucketName}, fileName=${fileName}, bufferSize=${buffer.length}`);

    // Определяем правильный путь в зависимости от bucket
    let uploadPath = fileName;
    if (bucketName === 'generations' && !fileName.startsWith('audio/')) {
      uploadPath = `audio/${fileName}`;
    }

    console.log(`[uploadAudioToSupabase] Upload path: ${uploadPath}`);

    // Пробуем загрузить в указанный bucket
    let { error } = await supabase.storage
      .from(bucketName)
      .upload(uploadPath, buffer, {
        contentType: 'audio/mpeg',
        upsert: true
      });

    // Если bucket не найден, пробуем альтернативные buckets
    if (error && error.message.includes('Bucket not found')) {
      console.log(`[uploadAudioToSupabase] Bucket '${bucketName}' не найден, пробуем 'generations'`);

      const result = await supabase.storage
        .from('generations')
        .upload(`audio/${fileName}`, buffer, {
          contentType: 'audio/mpeg',
          upsert: true
        });

      if (result.error) {
        console.error(`[uploadAudioToSupabase] Bucket 'generations' тоже недоступен:`, {
          originalError: error.message,
          generationsError: result.error.message
        });
        throw new Error(`Ошибка загрузки в Storage: все buckets недоступны`);
      }

      // Получаем публичный URL из generations bucket
      const { data: urlData } = supabase.storage
        .from('generations')
        .getPublicUrl(`audio/${fileName}`);

      return urlData.publicUrl;
    }

    if (error) {
      console.error(`[uploadAudioToSupabase] Upload error:`, error);
      throw new Error(`Ошибка загрузки в Storage: ${error.message}`);
    }

    // Получаем публичный URL из оригинального bucket
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(uploadPath);

    console.log(`[uploadAudioToSupabase] Successfully uploaded to: ${urlData.publicUrl}`);
    return urlData.publicUrl;
  } catch (error) {
    console.error('Ошибка загрузки аудио в Supabase:', error);
    throw error;
  }
};

/**
 * Загружает изображение в Supabase Storage
 */
export const uploadImageToSupabase = async (buffer, fileName, bucketName = 'generations') => {
  try {
    console.log(`[uploadImageToSupabase] Starting upload: bucket=${bucketName}, fileName=${fileName}, bufferSize=${buffer.length}`);

    // Определяем правильный путь в зависимости от bucket
    let uploadPath = fileName;
    if (bucketName === 'generations' && !fileName.startsWith('images/')) {
      uploadPath = `images/${fileName}`;
    }

    console.log(`[uploadImageToSupabase] Upload path: ${uploadPath}`);

    // Пробуем загрузить в указанный bucket
    let { error } = await supabase.storage
      .from(bucketName)
      .upload(uploadPath, buffer, {
        contentType: 'image/jpeg',
        upsert: true
      });

    // Если bucket не найден, пробуем альтернативные buckets
    if (error && error.message.includes('Bucket not found')) {
      console.log(`[uploadImageToSupabase] Bucket '${bucketName}' не найден, пробуем 'generations'`);

      const result = await supabase.storage
        .from('generations')
        .upload(`images/${fileName}`, buffer, {
          contentType: 'image/jpeg',
          upsert: true
        });

      if (result.error) {
        console.error(`[uploadImageToSupabase] Bucket 'generations' тоже недоступен:`, {
          originalError: error.message,
          generationsError: result.error.message
        });
        throw new Error(`Ошибка загрузки в Storage: все buckets недоступны`);
      }

      // Получаем публичный URL из generations bucket
      const { data: urlData } = supabase.storage
        .from('generations')
        .getPublicUrl(`images/${fileName}`);

      return urlData.publicUrl;
    }

    if (error) {
      console.error(`[uploadImageToSupabase] Upload error:`, error);
      throw new Error(`Ошибка загрузки в Storage: ${error.message}`);
    }

    // Получаем публичный URL из оригинального bucket
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(uploadPath);

    console.log(`[uploadImageToSupabase] Successfully uploaded to: ${urlData.publicUrl}`);
    return urlData.publicUrl;
  } catch (error) {
    console.error('Ошибка загрузки изображения в Supabase:', error);
    throw error;
  }
};

/**
 * Загружает видео в Supabase Storage
 */
export const uploadVideoToSupabase = async (buffer, fileName, bucketName = 'generations') => {
  try {
    // Определяем правильный путь в зависимости от bucket
    let uploadPath = `videos/${fileName}`;

    // Пробуем загрузить в указанный bucket
    let { error } = await supabase.storage
      .from(bucketName)
      .upload(uploadPath, buffer, {
        contentType: 'video/mp4',
        upsert: true
      });

    // Если bucket не найден, пробуем альтернативные buckets
    if (error && error.message.includes('Bucket not found')) {
      console.log(`[uploadVideoToSupabase] Bucket '${bucketName}' не найден, пробуем 'replicate-assets'`);

      const result = await supabase.storage
        .from('replicate-assets')
        .upload(fileName, buffer, {
          contentType: 'video/mp4',
          upsert: true
        });

      if (result.error) {
        throw new Error(`Ошибка загрузки видео в Storage: ${error.message}`);
      }

      // Получаем публичный URL из replicate-assets bucket
      const { data: urlData } = supabase.storage
        .from('replicate-assets')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    }

    if (error) {
      throw new Error(`Ошибка загрузки видео в Storage: ${error.message}`);
    }

    // Получаем публичный URL из оригинального bucket
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(uploadPath);

    return urlData.publicUrl;
  } catch (error) {
    console.error('Ошибка загрузки видео в Supabase:', error);
    throw error;
  }
};

/**
 * Загружает thumbnail в Supabase Storage
 */
export const uploadThumbnailToSupabase = async (buffer, fileName, bucketName = 'generations') => {
  try {
    // Определяем правильный путь в зависимости от bucket
    let uploadPath = `thumbnails/${fileName}`;

    // Пробуем загрузить в указанный bucket
    let { error } = await supabase.storage
      .from(bucketName)
      .upload(uploadPath, buffer, {
        contentType: 'image/jpeg',
        upsert: true
      });

    // Если bucket не найден, пробуем альтернативные buckets
    if (error && error.message.includes('Bucket not found')) {
      console.log(`[uploadThumbnailToSupabase] Bucket '${bucketName}' не найден, пробуем 'generation_thumbnails'`);

      const result = await supabase.storage
        .from('generation_thumbnails')
        .upload(fileName, buffer, {
          contentType: 'image/jpeg',
          upsert: true
        });

      if (result.error) {
        throw new Error(`Ошибка загрузки thumbnail в Storage: ${error.message}`);
      }

      // Получаем публичный URL из generation_thumbnails bucket
      const { data: urlData } = supabase.storage
        .from('generation_thumbnails')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    }

    if (error) {
      throw new Error(`Ошибка загрузки thumbnail в Storage: ${error.message}`);
    }

    // Получаем публичный URL из оригинального bucket
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(uploadPath);

    return urlData.publicUrl;
  } catch (error) {
    console.error('Ошибка загрузки thumbnail в Supabase:', error);
    throw error;
  }
};

/**
 * Генерирует thumbnail из видео используя FFmpeg
 */
export const generateThumbnailFromVideo = async (videoBuffer) => {
  try {
    // Импортируем ffmpeg динамически
    const ffmpeg = await import('fluent-ffmpeg');
    const fs = await import('fs');
    const path = await import('path');
    const os = await import('os');

    // Создаем временные файлы
    const tempDir = os.tmpdir();
    const videoPath = path.join(tempDir, `temp_video_${Date.now()}.mp4`);
    const thumbnailPath = path.join(tempDir, `temp_thumbnail_${Date.now()}.jpg`);

    // Записываем видео во временный файл
    fs.writeFileSync(videoPath, videoBuffer);

    return new Promise((resolve, reject) => {
      ffmpeg.default(videoPath)
        .screenshots({
          timestamps: ['00:00:01'], // Берем кадр на 1 секунде
          filename: path.basename(thumbnailPath),
          folder: path.dirname(thumbnailPath),
          size: '320x240'
        })
        .on('end', () => {
          try {
            // Читаем сгенерированный thumbnail
            const thumbnailBuffer = fs.readFileSync(thumbnailPath);

            // Удаляем временные файлы
            fs.unlinkSync(videoPath);
            fs.unlinkSync(thumbnailPath);

            resolve(thumbnailBuffer);
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          // Удаляем временные файлы в случае ошибки
          try {
            if (fs.existsSync(videoPath)) fs.unlinkSync(videoPath);
            if (fs.existsSync(thumbnailPath)) fs.unlinkSync(thumbnailPath);
          } catch (cleanupError) {
            console.error('Ошибка очистки временных файлов:', cleanupError);
          }
          reject(error);
        });
    });
  } catch (error) {
    console.error('Ошибка генерации thumbnail:', error);
    // Возвращаем null если не удалось сгенерировать thumbnail
    return null;
  }
};

/**
 * Скачивает файл по URL и возвращает buffer
 */
export const downloadFile = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('Ошибка скачивания файла:', error);
    throw error;
  }
};

/**
 * Сохраняет генерацию речи в базу данных
 */
export const saveSpeechGeneration = async ({
  user_id,
  text,
  audio_url,
  voice_id,
  voice_name,
  model,
  parameters,
  cost,
  public: isPublic,
  replicate_prediction_id
}) => {
  try {
    // Создаем объект для вставки
    const insertData = {
      user_id,
      text,
      audio_url,
      voice_id,
      voice_name: voice_name || voice_id,
      model,
      parameters: parameters || {},
      cost: cost || 0,
      public: isPublic !== undefined ? isPublic : true,
      created_at: new Date().toISOString()
    };

    // Добавляем replicate_prediction_id только если он передан
    if (replicate_prediction_id) {
      insertData.replicate_prediction_id = replicate_prediction_id;
    }

    const { data, error } = await supabase
      .from('speech_generations')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(`Ошибка сохранения речи: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Ошибка сохранения генерации речи:', error);
    throw error;
  }
};

/**
 * Сохраняет генерацию изображения в базу данных
 */
export const saveImageGeneration = async ({ user_id, prompt, image_url, model, replicate_prediction_id, cost, style, aspect_ratio, visibility }) => {
  try {
    // Проверяем, существует ли уже запись с таким replicate_prediction_id
    if (replicate_prediction_id) {
      const { data: existingRecord } = await supabase
        .from('image_generations')
        .select('id')
        .eq('replicate_prediction_id', replicate_prediction_id)
        .single();

      if (existingRecord) {
        console.log(`[saveImageGeneration] Запись с prediction_id ${replicate_prediction_id} уже существует, пропускаем`);
        return existingRecord;
      }
    }

    // Скачиваем изображение по временной ссылке
    const { v4: uuidv4 } = await import('uuid');
    const imageBuffer = await downloadFile(image_url);
    const imageId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${imageId}/${timestamp}-image.jpg`;

    // Загружаем изображение в Supabase Storage (используем generations bucket)
    const supabaseImageUrl = await uploadImageToSupabase(imageBuffer, fileName, 'generations');

    // Создаем объект для вставки
    const insertData = {
      id: imageId,
      user_id,
      prompt,
      image_url: supabaseImageUrl,
      image_urls: [supabaseImageUrl], // Добавляем массив с одним изображением для совместимости
      model,
      cost: cost || 0,
      style: style || null,
      aspect_ratio: aspect_ratio || null,
      public: visibility === 'public', // Добавляем поддержку приватности
      created_at: new Date().toISOString()
    };

    // Добавляем replicate_prediction_id только если он передан
    if (replicate_prediction_id) {
      insertData.replicate_prediction_id = replicate_prediction_id;
    }

    const { data, error } = await supabase
      .from('image_generations')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(`Ошибка сохранения изображения: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Ошибка сохранения генерации изображения:', error);
    throw error;
  }
};

/**
 * Сохраняет генерацию видео в базу данных
 */
export const saveVideoGeneration = async ({
  user_id,
  prompt,
  video_url,
  thumbnail_url,
  model,
  replicate_prediction_id,
  duration,
  quality,
  aspect_ratio,
  motion_mode,
  cost,
  visibility
}) => {
  try {
    // Проверяем, существует ли уже запись с таким replicate_prediction_id
    if (replicate_prediction_id) {
      const { data: existingRecord } = await supabase
        .from('video_generations')
        .select('id')
        .eq('replicate_prediction_id', replicate_prediction_id)
        .single();

      if (existingRecord) {
        console.log(`[saveVideoGeneration] Запись с prediction_id ${replicate_prediction_id} уже существует, пропускаем`);
        return existingRecord;
      }
    }

    // Импортируем uuid для генерации id
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();

    console.log(`[saveVideoGeneration] Сохраняем видео с URL: ${video_url}`);

    // Создаем объект для вставки
    const insertData = {
      id: videoId,
      user_id,
      prompt,
      video_url: video_url, // Используем переданный URL
      thumbnail_url: thumbnail_url || null, // Используем переданный thumbnail URL
      model,
      duration: duration || null,
      quality: quality || null,
      aspect_ratio: aspect_ratio || null,
      motion_mode: motion_mode || null,
      cost: cost || 0,
      public: visibility === 'public', // Добавляем поддержку приватности
      created_at: new Date().toISOString()
    };

    // Добавляем replicate_prediction_id только если он передан
    if (replicate_prediction_id) {
      insertData.replicate_prediction_id = replicate_prediction_id;
    }

    // Убираем fal_request_id так как такой колонки нет в БД

    console.log(`[saveVideoGeneration] Сохраняем в БД:`, insertData);

    const { error } = await supabase
      .from('video_generations')
      .insert(insertData);

    if (error) {
      throw new Error(`Ошибка сохранения видео: ${error.message}`);
    }

    console.log(`[saveVideoGeneration] Видео успешно сохранено в БД с ID: ${videoId}`);
    return { success: true, id: videoId, video_url: video_url };
  } catch (error) {
    console.error('Ошибка сохранения генерации видео:', error);
    throw error;
  }
};
