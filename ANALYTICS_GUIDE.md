# Руководство по аналитике UMA.AI

## Доступ к аналитике

Секретная страница аналитики доступна только администраторам через специальный код.

### Активация секретного доступа

**Способ 1: Konami Code**
1. Откройте сайт UMA.AI
2. Введите секретную комбинацию клавиш (Konami Code):
   ```
   ↑ ↑ ↓ ↓ ← → ← → B A
   ```
3. После успешного ввода появится белая вспышка
4. В сайдбаре появится пункт "Аналитика"

**Способ 2: Прямой переход (для тестирования)**
1. Перейдите напрямую по адресу `/analytics`
2. Секретный доступ активируется автоматически

**Способ 3: Консоль браузера**
1. Откройте консоль браузера (F12)
2. Выполните команду: `sessionStorage.setItem('secretAccess', 'true')`
3. Обновите страницу

## Функциональность аналитики

### Основные метрики

1. **Общие пополнения** - сумма всех платежей в рублях и кредитах
2. **Себестоимость** - реальные затраты на API провайдеров в рублях и долларах
3. **Прибыль** - разница между пополнениями и себестоимостью
4. **Пользователи** - общее количество и конверсия в платящих

### Разделы аналитики

#### 1. Обзор
- График пополнений по дням (последние 30 дней)
- Общая динамика доходов

#### 2. Модели
- Топ моделей по использованию
- Прибыльность каждой модели
- Детальная таблица с метриками:
  - Количество использований
  - Доход в кредитах
  - Себестоимость в кредитах
  - Прибыль
  - Маржинальность

#### 3. Пользователи
- Общее количество пользователей
- Количество платящих пользователей
- Активные пользователи (создававшие контент)
- Конверсия в платящих

#### 4. Транзакции
- Последние пополнения
- Последние траты
- Детальная информация по каждой транзакции

### Цены моделей (себестоимость)

#### Изображения:
- flux-kontext-pro: $0.04 = 4 кредита
- flux-kontext-max: $0.08 = 8 кредитов  
- minimax/image-01: $0.01 = 1 кредит
- ideogram-v3-turbo: $0.03 = 3 кредита
- ideogram-v2a-turbo: $0.025 = 2.5 кредита
- google/imagen-4: $0.05 = 5 кредитов
- bytedance/bagel: $0.10 = 10 кредитов
- flux-canny-pro: $0.05 = 5 кредитов

#### Видео (за секунду):
- kling-v2.0: $0.28 = 28 кредитов/сек
- kling-v1.6-standard: $0.05 = 5 кредитов/сек
- kling-v1.6-pro: $0.095 = 9.5 кредитов/сек
- ray-flash-2-540p: $0.033 = 3.3 кредита/сек
- veo3 (audio off): $0.50 = 50 кредитов/сек
- veo3 (audio on): $0.75 = 75 кредитов/сек

#### Аудио:
- elevenlabs/turbo-v2.5: $0.05/1000 chars = 5 кредитов/1000 chars
- elevenlabs/multilingual-v2: $0.1/1000 chars = 10 кредитов/1000 chars
- minimax/speech-02-hd: $0.05/1000 tokens = 5 кредитов/1000 tokens
- minimax/speech-02-turbo: $0.03/1000 tokens = 3 кредита/1000 tokens
- minimax/voice-cloning: $3.00 = 300 кредитов

### Курсы валют
- 1$ = 100 кредитов
- 1₽ = 2 кредита

### Функции безопасности

1. **Скрытие данных** - кнопка "Показать/Скрыть суммы" для защиты конфиденциальной информации
2. **Ограниченный доступ** - только через секретный код
3. **Маскировка пользователей** - ID пользователей показываются частично

### API эндпоинт

```
GET /api/analytics
Authorization: Bearer <token>
```

Возвращает JSON с полной аналитикой:
- summary: основные метрики
- model_stats: статистика по моделям
- user_stats: статистика пользователей
- payments_by_day: платежи по дням
- recent_transactions: последние транзакции

### Технические детали

**Автоматическое определение данных**:
- Система автоматически пытается получить реальные данные из Supabase
- При отсутствии подключения к БД использует mock данные
- Индикатор "DEMO данные" показывает, когда используются тестовые данные

**Реальные данные (после деплоя)**:
- Все исторические транзакции будут автоматически подтянуты
- Генерации изображений, видео и речи из всех таблиц
- Точные расчеты прибыли на основе реальных цен API

**Продакшн версия**:
1. **База данных**: Supabase PostgreSQL
2. **Таблицы**:
   - `credit_transactions` - все транзакции
   - `image_generations` - генерации изображений
   - `video_generations` - генерации видео
   - `speech_generations` - генерации речи
   - `profiles` - профили пользователей

3. **Безопасность**: RLS (Row Level Security) политики
4. **Кэширование**: Данные обновляются при каждом запросе
5. **Обработка ошибок**: Fallback на тестовые данные при проблемах с БД

### Мониторинг

Рекомендуется регулярно отслеживать:
1. Маржинальность моделей
2. Конверсию пользователей
3. Динамику пополнений
4. Убыточные модели

### Оптимизация

На основе данных аналитики можно:
1. Корректировать цены на модели
2. Отключать убыточные модели
3. Продвигать прибыльные модели
4. Улучшать конверсию пользователей
