// Serverless function for Replicate API (image/video generation)
// Supports: ideogram-ai/ideogram-v2a-turbo, prunaai/hidream-l1-fast:..., luma/ray-flash-2-540p, kwaivgi/kling-v1.6-pro, kwaivgi/kling-v1.6-standard, kwaivgi/kling-v2.0

import Replicate from "replicate";
import sharp from "sharp";
import fetch from "node-fetch";
import fs from "fs";
import path from "path";
import { updateUserCredits, uploadImageToSupabase } from "../utils/database.js";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Функция расчёта стоимости генерации видео
function getVideoGenerationCost(model, duration, quality = '540p', motionMode = 'normal') {
  // duration в секундах, model - строка
  if (!model || !duration) return 0;

  // Pixverse модель
  if (model.includes("pixverse-v4.5")) {
    const costTable = {
      5: {
        '360p': { normal: 90, smooth: 180 },
        '540p': { normal: 90, smooth: 180 },
        '720p': { normal: 120, smooth: 240 },
        '1080p': { normal: 240 }
      },
      8: {
        '360p': { normal: 180 },
        '540p': { normal: 180 },
        '720p': { normal: 240 }
      }
    };
    return costTable[duration]?.[quality]?.[motionMode] || 0;
  }

  if (model.includes("kling-v2.0")) {
    if (duration >= 10) return 800;
    return 400;
  }
  if (model.includes("kling-v1.6-pro")) {
    if (duration >= 10) return 600;
    return 300;
  }
  if (model.includes("kling-v1.6")) {
    if (duration >= 10) return 300;
    return 150;
  }
  if (model.includes("ray-flash-2")) {
    if (duration >= 9) return 160;
    return 100;
  }
  return 0;
}

// Функция расчёта стоимости генерации речи
function getSpeechGenerationCost(model, text) {
  if (!model || !text) return 0;

  // Модели речи minimax
  if (model.includes("speech-02-turbo")) {
    const textLength = text.length;
    const cost = Math.ceil(textLength * 0.1); // 0.1 кредита за символ, округление вверх
    return Math.max(5, cost); // Минимум 5 кредитов
  } else if (model.includes("speech-02-hd")) {
    const textLength = text.length;
    const cost = Math.ceil(textLength * 0.2); // 0.2 кредита за символ, округление вверх
    return Math.max(5, cost); // Минимум 5 кредитов
  }

  return 0;
}
import { v4 as uuidv4 } from "uuid";

// Скачивание и обрезка изображения под нужные размеры
async function downloadAndCropImage(url, width, height) {
  const response = await fetch(url);
  if (!response.ok) throw new Error("Failed to download image: " + url);
  const buffer = await response.buffer();
  // Приводим к JPG и нужным размерам
  const cropped = await sharp(buffer)
    .resize(width, height, { fit: "cover" })
    .jpeg({ quality: 95 })
    .toBuffer();
  const meta = await sharp(cropped).metadata();
  console.log(`[IMAGE] Cropped image for ${url}: ${meta.width}x${meta.height}, format: ${meta.format}`);
  return { buffer: cropped, width: meta.width, height: meta.height, format: meta.format };
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN
});

export default async function handler(req, res) {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  try {
    // Universal body parser for Vercel serverless (Node.js/ESM)
    let body = {};
// --- СПИСАНИЕ КРЕДИТОВ ---
/* Блок списания кредитов и обработки input перемещён ниже, после парсинга body */
    try {
      if (req.body && typeof req.body === "object" && Object.keys(req.body).length > 0) {
        body = req.body;
      } else {
        const raw = await new Promise((resolve, reject) => {
          let data = "";
          req.on("data", chunk => { data += chunk; });
          req.on("end", () => resolve(data));
          req.on("error", err => reject(err));
        });
        body = JSON.parse(raw || "{}");
      }
    } catch (e) {
      console.error("Failed to parse request body:", e);
      body = {};
    }
    console.log("Replicate API received body:", body);

    // Ожидаем формат { version, input }
    const { version, input } = body;

    // --- ПРОВЕРКА КРЕДИТОВ (без списания) ---
    if (input) {
      const userId = input?.userId;
      const model = body?.model || version || "";
      const text = input?.text || "";
      const duration = Number(input?.duration) || 5;
      const quality = input?.quality || '540p';
      const motionMode = input?.motion_mode || 'normal';

      // Определяем тип генерации и рассчитываем стоимость
      let cost = 0;
      let generationType = "unknown";

      if (model.includes("speech-02")) {
        cost = getSpeechGenerationCost(model, text);
        generationType = "speech";
      } else {
        cost = getVideoGenerationCost(model, duration, quality, motionMode);
        generationType = "video";
      }

      if (!userId) {
        res.status(400).json({ error: "userId обязателен" });
        return;
      }

      // Проверяем баланс, но не списываем кредиты
      if (cost > 0) {
        try {
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('credits')
            .eq('id', userId)
            .single();

          if (profileError) {
            throw new Error(`Ошибка получения профиля: ${profileError.message}`);
          }

          const currentCredits = profile?.credits || 0;
          if (currentCredits < cost) {
            res.status(402).json({ error: "Недостаточно кредитов", required: cost, available: currentCredits });
            return;
          }

          console.log(`[CREDITS] Проверка пройдена: у пользователя ${userId} достаточно кредитов (${currentCredits}) для ${generationType} (стоимость: ${cost})`);
        } catch (e) {
          console.error("[CREDITS] Ошибка проверки кредитов:", e);
          res.status(500).json({ error: "Ошибка проверки баланса", details: e.message });
          return;
        }
      }
    } else {
      console.warn("[CREDITS] Input или userId отсутствует, проверка кредитов пропущена.");
    }

    // --- МАППИНГ МОДЕЛЕЙ НА ВЕРСИИ ---
    let finalVersion = version;
    const model = body?.model || "";

    // Маппинг speech моделей
    if (model.includes("minimax/speech-02-turbo")) {
      finalVersion = "minimax/speech-02-turbo"; // Используем имя модели напрямую
    } else if (model.includes("minimax/speech-02-hd")) {
      finalVersion = "minimax/speech-02-hd"; // Используем имя модели напрямую
    }

    console.log(`[MODEL MAPPING] Original model: ${model}, Final version: ${finalVersion}`);

// --- ОБРАБОТКА start_image и end_image под нужный формат ---
const modelId = (version || model || "").toLowerCase();
// Маппинг aspect_ratio → width/height
function getSizeByAspect(aspect) {
  switch (aspect) {
    case "16:9": return { width: 1280, height: 720 };
    case "9:16": return { width: 720, height: 1280 };
    case "1:1": return { width: 1024, height: 1024 };
    case "4:3": return { width: 960, height: 720 };
    case "3:4": return { width: 720, height: 960 };
    case "21:9": return { width: 1680, height: 720 };
    case "9:21": return { width: 720, height: 1680 };
    default: return { width: 1280, height: 720 };
  }
}
if (input) {
  // Kling: start_image/end_image, aspect_ratio, duration, prompt
  if (modelId.includes("kling")) {
    const allowed = ["1:1","3:4","4:3","9:16","16:9","9:21","21:9"];
    const aspect = allowed.includes(input.aspect_ratio) ? input.aspect_ratio : "16:9";
    const { width, height } = getSizeByAspect(aspect);
    if (input.start_image) {
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(input.start_image, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed start_image dimensions: ${processedWidth}x${processedHeight}`);
        // Генерируем уникальное имя файла для Supabase Storage
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets"); // Указываем новый бакет
        input.start_image = url;
        console.log(`[INPUT] start_image set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки start_image:", e);
        // В случае ошибки обработки изображения, удаляем его из input, чтобы не отправлять невалидную ссылку
        delete input.start_image;
      }
    }
    if (input.end_image) {
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(input.end_image, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed end_image dimensions: ${processedWidth}x${processedHeight}`);
        // Генерируем уникальное имя файла для Supabase Storage
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets"); // Указываем новый бакет
        input.end_image = url;
        console.log(`[INPUT] end_image set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки end_image:", e);
        // В случае ошибки обработки изображения, удаляем его из input, чтобы не отправлять невалидную ссылку
        delete input.end_image;
      }
    }
    console.log(`[ASPECT_RATIO] Determined aspect for Kling: ${aspect}`);
    input.aspect_ratio = aspect;
    console.log(`[ASPECT_RATIO] input.aspect_ratio before Replicate call: ${input.aspect_ratio}`);
    if (![5, 10].includes(Number(input.duration))) {
      input.duration = 5;
    }

    // Специальные параметры для Kling v2.0
    if (modelId.includes("kling-v2.0")) {
      if (input.cfg_scale !== undefined) {
        input.cfg_scale = Number(input.cfg_scale);
        console.log(`[KLING-V2.0] cfg_scale set to: ${input.cfg_scale}`);
      }
      if (input.negative_prompt) {
        console.log(`[KLING-V2.0] negative_prompt set to: ${input.negative_prompt}`);
      }
    }

    delete input.width;
    delete input.height;
  }
  // Ray Flash 2: start_image_url/end_image_url, aspect_ratio, duration, prompt
  if (modelId.includes("ray-flash-2")) {
    const allowed = ["1:1","3:4","4:3","9:16","16:9","9:21","21:9"];
    const aspect = allowed.includes(input.aspect_ratio) ? input.aspect_ratio : "16:9";
    const { width, height } = getSizeByAspect(aspect);
    if (input.start_image) {
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(input.start_image, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed start_image dimensions: ${processedWidth}x${processedHeight}`);
        // Генерируем уникальное имя файла для Supabase Storage
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets"); // Указываем новый бакет
        input.start_image_url = url;
        console.log(`[INPUT] start_image_url set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки start_image_url:", e);
        // В случае ошибки обработки изображения, удаляем его из input, чтобы не отправлять невалидную ссылку
        delete input.start_image_url;
      }
    }
    if (input.end_image) {
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(input.end_image, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed end_image dimensions: ${processedWidth}x${processedHeight}`);
        // Генерируем уникальное имя файла для Supabase Storage
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets"); // Указываем новый бакет
        input.end_image_url = url;
        console.log(`[INPUT] end_image_url set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки end_image_url:", e);
        // В случае ошибки обработки изображения, удаляем его из input, чтобы не отправлять невалидную ссылку
        delete input.end_image_url;
      }
    }
    input.aspect_ratio = aspect;
    if (![5, 9].includes(Number(input.duration))) {
      input.duration = 5;
    }
    delete input.width;
    delete input.height;
    delete input.start_image;
    delete input.end_image;
  }
  // Pixverse: image, last_frame_image, style, effect, quality, duration, motion_mode, aspect_ratio, negative_prompt, seed
  if (modelId.includes("pixverse-v4.5")) {
    const allowed = ["16:9", "9:16", "1:1"];
    const aspect = allowed.includes(input.aspect_ratio) ? input.aspect_ratio : "16:9";
    const { width, height } = getSizeByAspect(aspect);

    // Обработка начального изображения
    if (input.start_image || input.image) {
      const imageUrl = input.start_image || input.image;
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(imageUrl, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed image dimensions: ${processedWidth}x${processedHeight}`);
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets");
        input.image = url;
        console.log(`[INPUT] image set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки image:", e);
        delete input.image;
      }
      delete input.start_image;
    }

    // Обработка конечного изображения
    if (input.end_image || input.last_frame_image) {
      const imageUrl = input.end_image || input.last_frame_image;
      try {
        const { buffer, format, width: processedWidth, height: processedHeight } = await downloadAndCropImage(imageUrl, width, height);
        console.log(`[IMAGE_DIMENSIONS] Processed last_frame_image dimensions: ${processedWidth}x${processedHeight}`);
        const uniqueFileName = `replicate-inputs/${uuidv4()}.${format || 'jpg'}`;
        const url = await uploadImageToSupabase(buffer, uniqueFileName, "replicate-assets");
        input.last_frame_image = url;
        console.log(`[INPUT] last_frame_image set to ${url}, format: ${format}, size: ${width}x${height}`);
      } catch (e) {
        console.error("Ошибка обработки last_frame_image:", e);
        delete input.last_frame_image;
      }
      delete input.end_image;
    }

    // Валидация параметров pixverse
    input.quality = ['360p', '540p', '720p', '1080p'].includes(input.quality) ? input.quality : '540p';
    input.duration = [5, 8].includes(Number(input.duration)) ? Number(input.duration) : 5;
    input.motion_mode = ['normal', 'smooth'].includes(input.motion_mode) ? input.motion_mode : 'normal';
    input.aspect_ratio = aspect;

    // Проверка совместимости параметров
    if (input.duration === 8 && input.motion_mode === 'smooth') {
      input.motion_mode = 'normal'; // 8s не поддерживает smooth
    }
    if (input.quality === '1080p' && (input.duration === 8 || input.motion_mode === 'smooth')) {
      input.quality = '720p'; // 1080p ограничения
    }

    // Очистка неиспользуемых параметров
    delete input.width;
    delete input.height;
    delete input.start_image_url;
    delete input.end_image_url;

    console.log(`[PIXVERSE] Final input parameters:`, {
      quality: input.quality,
      duration: input.duration,
      motion_mode: input.motion_mode,
      aspect_ratio: input.aspect_ratio,
      style: input.style,
      effect: input.effect,
      seed: input.seed,
      negative_prompt: input.negative_prompt
    });
  }
}
    // Для speech моделей prompt не обязателен, проверяем text
    const isTextToSpeech = model.includes("speech-02");
    const requiredTextParam = isTextToSpeech ? input?.text : input?.prompt;

    if (!finalVersion || !input || !requiredTextParam) {
      const missingParam = isTextToSpeech ? "input.text" : "input.prompt";
      res.status(400).json({ error: `Missing required parameters: version, ${missingParam}` });
      return;
    }

    // Логируем токен (замаскированно)
    const token = process.env.REPLICATE_API_TOKEN;
    console.log(
      "BACKEND: Read REPLICATE_API_TOKEN:",
      token ? `Token found (starts with ${token.substring(0, 5)}, ends with ${token.slice(-5)})` : "TOKEN NOT FOUND!"
    );

    // Логируем исходящий запрос
    // Добавляем webhook для асинхронного пуша результата
    const webhookUrl = "https://umaai.site/api/replicate/webhook";
    const replicateHeaders = {
      "Authorization": `Token ${token}`,
      "Content-Type": "application/json"
    };
    const replicateBody = JSON.stringify({
      version: finalVersion,
      input,
      webhook: webhookUrl,
      webhook_events_filter: ["completed"]
    });
    console.log("BACKEND: Replicate fetch headers:", {
      ...replicateHeaders,
      Authorization: `Token ${token ? token.substring(0, 5) + "..." + token.slice(-5) : "MISSING"}`
    });
    console.log("BACKEND: Replicate fetch body:", replicateBody);
    console.log("BACKEND: Sending request to Replicate API...");

    // Run model через прямой fetch с токеном
    const replicateRes = await fetch("https://api.replicate.com/v1/predictions", {
      method: "POST",
      headers: replicateHeaders,
      body: replicateBody
    });

    // Логируем ответ Replicate
    console.log("BACKEND: Replicate response status:", replicateRes.status);
    const replicateData = await replicateRes.json();
    console.log("BACKEND: Replicate response body:", replicateData);

    if (!replicateRes.ok) {
      // Проверяем на специфические ошибки
      const errorMessage = replicateData.detail || replicateData.error || '';
      console.log("BACKEND: Replicate error details:", errorMessage);

      // Проверяем на NSFW ошибки
      if (errorMessage.toLowerCase().includes('nsfw') ||
          errorMessage.toLowerCase().includes('safety') ||
          errorMessage.toLowerCase().includes('inappropriate') ||
          errorMessage.toLowerCase().includes('content policy') ||
          errorMessage.toLowerCase().includes('violates') ||
          errorMessage.toLowerCase().includes('blocked')) {
        res.status(400).json({
          error: "NSFW_BLOCKED",
          message: "Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.",
          details: replicateData
        });
        return;
      }

      res.status(replicateRes.status).json({
        error: "Replicate API error",
        details: replicateData
      });
      return;
    }

    res.status(200).json(replicateData);
  } catch (error) {
    // Replicate SDK throws with error.message and sometimes error.response
    let details = error.message;
    if (error.response && error.response.data) {
      details = error.response.data;
    }
    res.status(500).json({
      error: "Replicate API error",
      details
    });
  }
}
