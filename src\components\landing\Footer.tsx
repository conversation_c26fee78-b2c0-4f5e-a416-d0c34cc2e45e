import { Mail, Phone, MapPin } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { i18n } = useTranslation();
  
  return (
    <footer className="py-12 px-6 border-t border-black/10 dark:border-white/10">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="text-gradient text-2xl font-bold mb-4">Uma AI</h3>
            <p className="text-foreground/70 mb-4">
              {i18n.language === 'ru'
                ? 'Платформа для генерации изображений и видео с использованием искусственного интеллекта.'
                : 'AI-powered platform for image and video generation.'}
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold text-lg mb-4 text-foreground">
              {i18n.language === 'ru' ? 'Контакты' : 'Contact Us'}
            </h4>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-2 text-foreground/60" />
                <a href="mailto:<EMAIL>" className="text-foreground/70 hover:text-foreground transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Phone className="w-4 h-4 mr-2 text-foreground/60" />
                <a href="tel:+79509540097" className="text-foreground/70 hover:text-foreground transition-colors">
                  +7 (950) 954-00-97
                </a>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-foreground/60" />
                <span className="text-foreground/70">
                  {i18n.language === 'ru'
                    ? 'Самозанятый Богдан Евгеньевич Друковский'
                    : 'Self-employed Bogdan Drukovsky'}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-lg mb-4 text-foreground">
              {i18n.language === 'ru' ? 'Навигация' : 'Navigation'}
            </h4>
            <div className="space-y-2">
              <div><a href="/" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Главная' : 'Home'}</a></div>
              <div><a href="/dashboard" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Панель управления' : 'Dashboard'}</a></div>
              <div><a href="/text" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Генерация текста' : 'Text Generation'}</a></div>
              <div><a href="/image" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Генерация изображений' : 'Image Generation'}</a></div>
              <div><a href="/video" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Генерация видео' : 'Video Generation'}</a></div>
              <div><a href="/blog" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Блог' : 'Blog'}</a></div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-lg mb-4 text-foreground">
              {i18n.language === 'ru' ? 'Документы' : 'Legal Documents'}
            </h4>
            <div className="space-y-2">
              <div><a href="/terms" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Условия использования' : 'Terms of Service'}</a></div>
              <div><a href="/privacy" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Политика конфиденциальности' : 'Privacy Policy'}</a></div>
              <div><a href="/cookie" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Политика использования файлов cookie' : 'Cookie Policy'}</a></div>
              <div><a href="/delivery" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Условия доставки' : 'Delivery Terms'}</a></div>
              <div><a href="/return" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Условия возврата' : 'Return Policy'}</a></div>
              <div><a href="/license" className="text-foreground/70 hover:text-foreground transition-colors">{i18n.language === 'ru' ? 'Лицензия' : 'License'}</a></div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-black/10 dark:border-white/10 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-foreground/60 text-sm mb-4 md:mb-0">
            © {currentYear} Uma AI. Самозанятый Богдан Евгеньевич Друковский. ИНН: 550147543516. {i18n.language === 'ru' ? 'Все права защищены.' : 'All rights reserved.'}
          </div>
          
          <div className="flex gap-6">
            <a href="/terms" className="text-black/60 hover:text-black transition-colors text-sm">{i18n.language === 'ru' ? 'Условия использования' : 'Terms of Service'}</a>
            <a href="/privacy" className="text-black/60 hover:text-black transition-colors text-sm">{i18n.language === 'ru' ? 'Политика конфиденциальности' : 'Privacy Policy'}</a>
            <a href="/cookie" className="text-black/60 hover:text-black transition-colors text-sm">{i18n.language === 'ru' ? 'Политика использования файлов cookie' : 'Cookie Policy'}</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
