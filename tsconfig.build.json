{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "dist", "rootDir": "src", "declaration": false, "esModuleInterop": true, "moduleResolution": "node", "allowJs": false, "skipLibCheck": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist"]}