import { MessageSquare, Image, Video, LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

const getFeatures = (t: (key: string) => string) => [
  {
    icon: MessageSquare,
    title: t('features.textGenTitle'),
    description: t('features.textGenDescription'),
    color: "from-blue-500/20 to-blue-600/5",
    delay: "animation-delay-200"
  },
  {
    icon: Image,
    title: t('features.imageGenTitle'),
    description: t('features.imageGenDescription'),
    color: "from-slate-500/20 to-slate-600/5",
    delay: "animation-delay-400"
  },
  {
    icon: Video,
    title: t('features.videoGenTitle'),
    description: t('features.videoGenDescription'),
    color: "from-emerald-500/20 to-emerald-600/5",
    delay: "animation-delay-600"
  }
];

interface FeatureType {
  icon: LucideIcon;
  title: string;
  description: string;
  color: string;
  delay: string;
}

import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

const FeatureCard = ({ feature, index }: { feature: FeatureType; index: number }) => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const getPath = (idx: number) => {
    if (idx === 0) return '/text';
    if (idx === 1) return '/image';
    if (idx === 2) return '/video';
    return '/';
  };

  const handleClick = () => {
    if (user) {
      navigate(getPath(index));
    } else {
      navigate('/login');
    }
  };

  return (
    <div
      className={cn(
        "bg-background dark:bg-card rounded-xl p-6 animate-scale-in shadow-sm border border-black/10 dark:border-white/10 cursor-pointer transition-transform hover:-translate-y-1",
        feature.delay
      )}
      onClick={handleClick}
      tabIndex={0}
      role="button"
      onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') handleClick(); }}
    >
      <div className={cn(
        "w-14 h-14 rounded-lg mb-5 flex items-center justify-center bg-gradient-to-br",
        feature.color
      )}>
        <feature.icon className="text-foreground" size={24} />
      </div>
      <h3 className="text-xl font-bold mb-3 text-foreground">{feature.title}</h3>
      <p className="text-foreground/70">{feature.description}</p>
    </div>
  );
};

const FeaturesSection = () => {
  const { t } = useTranslation();
  const features = getFeatures(t);

  return (
    <section className="py-20 px-6 relative">
      <div className="absolute inset-0 w-full h-full z-0 pointer-events-none overflow-hidden">
        <div 
          className="absolute top-[15%] left-0 w-[40%] h-[50vh]"
          style={{
            background: "radial-gradient(ellipse at left center, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.07) 30%, rgba(99, 102, 241, 0.02) 60%, transparent 80%)",
            filter: "blur(70px)",
            opacity: 0.7
          }}
        />
        
        <div 
          className="absolute top-[15%] right-0 w-[40%] h-[50vh]"
          style={{
            background: "radial-gradient(ellipse at right center, rgba(168, 85, 247, 0.15) 0%, rgba(168, 85, 247, 0.07) 30%, rgba(168, 85, 247, 0.02) 60%, transparent 80%)",
            filter: "blur(70px)",
            opacity: 0.7
          }}
        />
      </div>
      
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 relative">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-foreground relative z-10 tracking-tight">
            {t('features.sectionTitle')}
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto relative z-10">
            {t('features.sectionSubtitle')}
          </p>
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-[150%] opacity-5 bg-gradient-radial from-blue-500 via-purple-500 to-transparent rounded-full blur-3xl -z-10"></div>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          {Array.isArray(features) &&
            features.map((feature, index) => (
              <FeatureCard key={index} feature={feature} index={index} />
            ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
