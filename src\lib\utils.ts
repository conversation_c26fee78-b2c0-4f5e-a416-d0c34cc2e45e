import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Склеивание классов tailwind
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Обрезает base64-изображение до нужного aspect ratio (например, "16:9").
 * Возвращает новый base64.
 * Если исходное изображение уже с нужным соотношением — возвращает исходное.
 */
export async function cropBase64ToAspectRatio(
  base64: string,
  aspectRatio: string
): Promise<{ cropped: string; wasCropped: boolean }> {
  return new Promise((resolve) => {
    const img = new window.Image();
    img.onload = () => {
      const [w, h] = [img.width, img.height];
      const [arW, arH] = aspectRatio.split(":").map(Number);
      if (!arW || !arH) return resolve({ cropped: base64, wasCropped: false });

      const targetRatio = arW / arH;
      const imgRatio = w / h;

      let sx = 0, sy = 0, sw = w, sh = h;
      let wasCropped = false;

      if (Math.abs(imgRatio - targetRatio) < 0.01) {
        // Почти совпадает, не обрезаем
        resolve({ cropped: base64, wasCropped: false });
        return;
      }

      if (imgRatio > targetRatio) {
        // Изображение слишком широкое — crop по ширине
        sw = Math.round(h * targetRatio);
        sx = Math.round((w - sw) / 2);
        wasCropped = true;
      } else {
        // Изображение слишком высокое — crop по высоте
        sh = Math.round(w / targetRatio);
        sy = Math.round((h - sh) / 2);
        wasCropped = true;
      }

      const canvas = document.createElement("canvas");
      canvas.width = sw;
      canvas.height = sh;
      const ctx = canvas.getContext("2d");
      if (!ctx) return resolve({ cropped: base64, wasCropped: false });

      ctx.drawImage(img, sx, sy, sw, sh, 0, 0, sw, sh);
      const croppedBase64 = canvas.toDataURL();
      resolve({ cropped: croppedBase64, wasCropped });
    };
    img.onerror = () => resolve({ cropped: base64, wasCropped: false });
    img.src = base64;
  });
}
/**
 * Преобразует File в base64 (data URL)
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}
