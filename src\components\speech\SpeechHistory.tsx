import React, { useState, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Play, Pause, Download, Mic } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

interface SpeechHistoryItem {
  id: string;
  text: string;
  audio_url: string;
  voice_name: string;
  model: string;
  created_at: string;
  cost: number;
}

interface SpeechHistoryProps {
  generations: SpeechHistoryItem[];
  className?: string;
}

const SpeechHistory: React.FC<SpeechHistoryProps> = ({
  generations,
  className
}) => {
  const { t } = useTranslation();
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());

  // Очистка аудио элементов при размонтировании
  useEffect(() => {
    return () => {
      audioElements.forEach(audio => {
        audio.pause();
        audio.src = '';
      });
    };
  }, []);

  // Воспроизведение/пауза аудио
  const togglePlayback = async (item: SpeechHistoryItem) => {
    try {
      // Останавливаем все другие аудио
      audioElements.forEach((audio, id) => {
        if (id !== item.id) {
          audio.pause();
        }
      });

      let audio = audioElements.get(item.id);
      
      if (!audio) {
        // Создаем новый аудио элемент
        audio = new Audio(item.audio_url);
        
        audio.addEventListener('ended', () => {
          setPlayingId(null);
        });

        audio.addEventListener('error', () => {
          toast.error('Ошибка воспроизведения аудио');
          setPlayingId(null);
        });

        setAudioElements(prev => new Map(prev).set(item.id, audio!));
      }

      if (playingId === item.id) {
        // Пауза
        audio.pause();
        setPlayingId(null);
      } else {
        // Воспроизведение
        await audio.play();
        setPlayingId(item.id);
      }
    } catch (error) {
      console.error('Playback error:', error);
      toast.error('Ошибка воспроизведения');
      setPlayingId(null);
    }
  };

  // Скачивание аудио
  const downloadAudio = async (item: SpeechHistoryItem) => {
    try {
      const response = await fetch(item.audio_url);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `speech-${item.id}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
      toast.success('Файл загружен');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Ошибка загрузки файла');
    }
  };

  // Форматирование даты
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString('ru-RU', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 1) {
      return 'Вчера';
    } else if (diffDays < 7) {
      return `${diffDays} дн. назад`;
    } else {
      return date.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  };

  // Форматирование названия модели
  const formatModelName = (model: string) => {
    if (model.includes('speech-02-turbo')) {
      return 'Speech 02 Turbo';
    } else if (model.includes('speech-02-hd')) {
      return 'Speech 02 HD';
    }
    return model; // Возвращаем оригинальное название, если не распознали
  };

  if (generations.length === 0) {
    return (
      <div className={cn("bg-background rounded-xl p-4 border w-full", className)}>
        <h3 className="text-lg font-bold mb-3">{t('speechGen.history.title')}</h3>
        <div className="text-center text-foreground/60 py-8">
          <Mic size={48} className="mx-auto mb-4 opacity-50" />
          <p>{t('speechGen.history.empty')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-background rounded-xl p-4 border w-full", className)}>
      <h3 className="text-lg font-bold mb-3">{t('speechGen.history.title')}</h3>
      
      <ScrollArea className="h-[400px]">
        <div className="space-y-3">
          {generations.map((item) => (
            <div
              key={item.id}
              className="bg-card p-3 rounded-lg border hover:border-primary/50 transition-colors"
            >
              {/* Заголовок с датой */}
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-foreground/80">
                  {formatDate(item.created_at)}
                </div>
                <div className="text-xs text-foreground/60">
                  {item.voice_name} • {formatModelName(item.model)}
                </div>
              </div>

              {/* Текст */}
              <div className="text-sm text-foreground/70 mb-3 line-clamp-2">
                {item.text}
              </div>

              {/* Элементы управления */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {/* Кнопка воспроизведения */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => togglePlayback(item)}
                    className="h-8 w-8 p-0"
                    title={playingId === item.id ? 'Пауза' : t('speechGen.history.play')}
                  >
                    {playingId === item.id ? (
                      <Pause size={14} />
                    ) : (
                      <Play size={14} />
                    )}
                  </Button>

                  {/* Кнопка скачивания */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => downloadAudio(item)}
                    className="h-8 w-8 p-0"
                    title={t('speechGen.history.download')}
                  >
                    <Download size={14} />
                  </Button>
                </div>

                {/* Стоимость */}
                <div className="text-xs text-foreground/60">
                  {item.cost} токенов
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default SpeechHistory;