import React from 'react'
import { cn } from '@/lib/utils'
import { ImageIcon, SlidersHorizontal, ArrowUp } from 'lucide-react'

interface InvertedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onSubmit?: () => void;
  className?: string;
}

const InvertedInput = React.forwardRef<HTMLInputElement, InvertedInputProps>(
  ({ className, onSubmit, ...props }, ref) => {
    return (
      <div className={cn(
        "relative flex items-center w-full",
        "bg-white rounded-full border border-gray-200/30",
        "shadow-[0_2px_6px_rgba(0,0,0,0.05)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.1)]",
        "transition-shadow duration-200",
        className
      )}>
        {/* Left icons container */}
        <div className="flex items-center gap-3 px-4">
          <ImageIcon className="w-5 h-5 text-gray-400" strokeWidth={1.5} />
          <SlidersHorizontal className="w-5 h-5 text-gray-400" strokeWidth={1.5} />
        </div>

        {/* Input field */}
        <input
          ref={ref}
          className={cn(
            "flex-1 h-12 bg-transparent",
            "text-gray-800 placeholder:text-gray-400",
            "focus:outline-none"
          )}
          defaultValue="треугольник"
          {...props}
        />

        {/* Submit button */}
        <button
          onClick={onSubmit}
          className={cn(
            "flex items-center justify-center",
            "w-10 h-10 rounded-full mr-1",
            "bg-gray-50 hover:bg-gray-100",
            "transition-colors duration-200",
            "mr-0.5"
          )}
        >
          <ArrowUp className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    )
  }
)

InvertedInput.displayName = 'InvertedInput'

export default InvertedInput