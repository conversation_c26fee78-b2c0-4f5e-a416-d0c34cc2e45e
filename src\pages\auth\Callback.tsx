import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase, exchangeCodeForSession, createUserProfile } from '@/utils/supabase';
import { toast } from 'sonner';

const Callback = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Функция для обработки OAuth редиректа
    const handleOAuthRedirect = async () => {
      try {
        console.log('Обработка OAuth редиректа');
        
        // Получаем URL hash фрагмент для OAuth
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        
        // Если есть hash параметры - это OAuth flow
        if (hashParams.get('access_token')) {
          console.log('Найден access_token, завершаем OAuth процесс');
          
          // Завершаем OAuth процесс
          const { data, error } = await supabase.auth.getUser();
          
          if (error) {
            console.error('Ошибка при получении пользователя:', error);
            setError(error.message);
            toast.error('Ошибка при входе: ' + error.message);
            return;
          }
          
          if (data.user) {
            console.log('Пользователь авторизован:', data.user.email);
            
            // Создаем профиль пользователя, если он еще не существует
            try {
              console.log('Проверяем/создаем профиль пользователя');
              await createUserProfile(data.user);
            } catch (profileError) {
              console.error('Ошибка при создании профиля:', profileError);
              // Продолжаем работу даже при ошибке создания профиля
            }
            
            // Перенаправляем на дашборд
            navigate('/dashboard');
            return;
          }
        }
        
        // Проверяем наличие кода авторизации в URL
        const params = new URLSearchParams(window.location.search);
        const code = params.get('code');
        
        if (!code) {
          console.error('Код авторизации не найден в URL');
          setError('Код авторизации не найден');
          toast.error('Ошибка при входе: Код авторизации не найден');
          return;
        }
        
        console.log('Найден код авторизации, обмениваем на сессию');
        
        // Обмениваем код на сессию
        const { session, user } = await exchangeCodeForSession(code);
          
        if (user) {
          console.log('Пользователь авторизован:', user.email);
          
          // Создаем профиль пользователя, если он еще не существует
          try {
            console.log('Проверяем/создаем профиль пользователя');
            await createUserProfile(user);
          } catch (profileError) {
            console.error('Ошибка при создании профиля:', profileError);
            // Продолжаем работу даже при ошибке создания профиля
          }
          
          // Перенаправляем на дашборд
          navigate('/dashboard');
        } else {
          console.error('Пользователь не найден после обмена кода на сессию');
          setError('Ошибка при входе');
          toast.error('Ошибка при входе: Пользователь не найден');
        }
      } catch (error: any) {
        console.error('Ошибка при обработке редиректа:', error);
        setError(error.message || 'Произошла ошибка при входе');
        toast.error('Ошибка при входе: ' + (error.message || 'Произошла ошибка'));
      }
    };
    
    handleOAuthRedirect();
  }, [navigate]);

  // Отображаем загрузку или ошибку
  return (
    <div className="flex items-center justify-center h-screen">
      {error ? (
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Ошибка при входе</h2>
          <p className="text-gray-700">{error}</p>
          <button 
            onClick={() => navigate('/auth')} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Вернуться на страницу входа
          </button>
        </div>
      ) : (
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-t-blue-500 border-b-blue-500 border-gray-200 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-700">Завершаем вход в систему...</p>
        </div>
      )}
    </div>
  );
};

export default Callback; 