// FAL Result API - Simple name to avoid routing issues
// GET /api/falresult?requestId=<request_id>&model=<model_id>&userId=<user_id>&prompt=<prompt>&visibility=<visibility>

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL RESULT SIMPLE] ${req.method} request received`);
  
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    console.log(`[FAL RESULT SIMPLE] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility } = req.query;
    
    console.log('[FAL RESULT SIMPLE] Processing:', { requestId, model, userId });
    
    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL RESULT SIMPLE] FAL_KEY not found');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Get status first
    const statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    console.log('[FAL RESULT SIMPLE] Checking status:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL RESULT SIMPLE] Status error:', errorText);
      return res.status(statusResponse.status).json({ 
        error: 'Status check failed', 
        details: errorText 
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL RESULT SIMPLE] Status:', statusData.status);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({ 
        error: 'Generation not completed', 
        status: statusData.status 
      });
    }

    // Use response_url from status
    const resultUrl = statusData.response_url;
    console.log('[FAL RESULT SIMPLE] Getting result from:', resultUrl);

    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL RESULT SIMPLE] FAL error:', errorText);
      return res.status(falResponse.status).json({ 
        error: 'Failed to get result', 
        details: errorText 
      });
    }

    const result = await falResponse.json();
    console.log('[FAL RESULT SIMPLE] Got result, has video:', !!result.video);

    // Save to database
    if (result.video && result.video.url && userId && prompt) {
      try {
        console.log('[FAL RESULT SIMPLE] Saving to database...');
        await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility || 'public', requestId);
        console.log('[FAL RESULT SIMPLE] Saved successfully');
      } catch (saveError) {
        console.error('[FAL RESULT SIMPLE] Save error:', saveError);
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL RESULT SIMPLE] Handler error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}

async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    const videoBuffer = await downloadFile(videoUrl);
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);

    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
      }
    } catch (thumbnailError) {
      console.error(`[FAL RESULT SIMPLE] Thumbnail error:`, thumbnailError);
    }

    let cost = 100;
    if (model.includes('standard')) cost = 75;
    else if (model.includes('pro')) cost = 120;
    else if (model.includes('master')) cost = 320;

    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId,
      duration: 5,
      cost: cost,
      visibility: visibility
    });

    // Start GIF creation
    createGifAsync(supabaseVideoUrl, savedGeneration.id, userId).catch(console.error);

  } catch (error) {
    console.error(`[FAL RESULT SIMPLE] Database error:`, error);
    throw error;
  }
}

async function createGifAsync(videoUrl, generationId, userId) {
  try {
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000';
    await fetch(`${baseUrl}/api/video/create-gif`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ videoUrl, generationId, userId })
    });
  } catch (error) {
    console.error(`[FAL RESULT SIMPLE] GIF error:`, error);
  }
}
