# Исправления дублирования изображений в FLUX (Together)

## Проблема
При генерации изображений через FLUX (Together) изображения сохранялись дважды из-за:

1. **Дублирование логики в `togetherImageGeneration.ts`**:
   - Проверка `model !== 'together'` выполнялась дважды (строки 51 и 89)
   - Это могло приводить к двойному вызову Replicate API

2. **Двойное сохранение в базу данных**:
   - В `togetherImageGeneration.ts` изображения загружались в Supabase Storage и сохранялись в БД
   - В `ImageGeneration.tsx` происходил дополнительный вызов `saveImageGeneration`

3. **Двойная загрузка в Supabase Storage**:
   - В `togetherImageGeneration.ts` изображения загружались в Storage
   - В `saveImageGeneration` происходила повторная загрузка тех же изображений

## Исправления

### 1. Убрано дублирование логики в `togetherImageGeneration.ts`
- Удален дублирующий блок проверки `model !== 'together'`
- Оставлена только одна проверка в начале функции

### 2. Исправлено двойное сохранение в `ImageGeneration.tsx`
- Добавлено условие `selectedService !== 'together'` для вызова `saveImageGeneration`
- Для Together API сохранение происходит только внутри `generateImageWithTogether`

### 3. Правильная обработка base64 данных от Together API
- Together API возвращает base64 данные, которые нужно сохранить в постоянное хранилище
- В `togetherImageGeneration.ts` base64 конвертируется в blob и загружается в Supabase Storage
- Полученные Supabase URL передаются в `saveImageGeneration` для сохранения в БД

### 4. Оптимизирована логика в `saveImageGeneration`
- Добавлена проверка на уже загруженные в Supabase Storage URL
- Избегается повторная загрузка изображений, которые уже находятся в Storage
- Сохранена совместимость с другими источниками изображений

## Результат
- Изображения теперь сохраняются только один раз
- Устранено дублирование записей в базе данных
- Base64 данные от Together API корректно конвертируются и сохраняются
- Избегается повторная загрузка уже сохраненных изображений
- Сохранена совместимость с другими сервисами генерации