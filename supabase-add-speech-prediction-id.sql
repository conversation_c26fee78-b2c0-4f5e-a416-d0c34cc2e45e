-- До<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> колонку replicate_prediction_id в таблицу speech_generations
-- Эт<PERSON> колонка нужна для связи с Replicate API

ALTER TABLE speech_generations 
ADD COLUMN IF NOT EXISTS replicate_prediction_id TEXT;

-- Добавляем индекс для быстрого поиска по prediction_id
CREATE INDEX IF NOT EXISTS idx_speech_generations_prediction_id 
ON speech_generations(replicate_prediction_id);

-- Проверяем структуру таблицы
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'speech_generations' 
ORDER BY ordinal_position;