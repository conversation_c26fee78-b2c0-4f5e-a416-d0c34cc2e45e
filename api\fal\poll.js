// Serverless function for background polling of Fal API status
// This runs independently of client connection and saves results to database

import fetch from "node-fetch";
import { saveVideoGeneration, updateUserCredits, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "../utils/database.js";
import { createClient } from '@supabase/supabase-js';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import os from 'os';
import fs from 'fs/promises';

const execAsync = promisify(exec);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const falKey = process.env.FAL_KEY;

if (!supabaseUrl || !supabaseServiceKey || !falKey) {
  throw new Error('Missing environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility = 'public', cost } = req.body;

    if (!requestId || !model || !userId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    console.log(`[FAL POLL] Starting background polling for request ${requestId}`);

    // Запускаем фоновый процесс polling (не ждем его завершения)
    pollFalRequest(requestId, model, userId, prompt, visibility, cost)
      .catch(error => {
        console.error(`[FAL POLL] Background polling failed for ${requestId}:`, error);
      });

    // Сразу возвращаем ответ клиенту
    return res.status(200).json({
      success: true,
      message: 'Background polling started',
      requestId
    });

  } catch (error) {
    console.error('[FAL POLL] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Фоновый polling статуса Fal API с сохранением в БД
 */
export async function pollFalRequest(requestId, model, userId, prompt, visibility, cost) {
  const maxAttempts = 120; // 20 минут максимум (120 * 10 секунд)
  let attempt = 0;
  const retryDelay = 10000; // 10 секунд между проверками

  console.log(`[FAL POLL] Background polling started for ${requestId}, max attempts: ${maxAttempts}`);

  while (attempt < maxAttempts) {
    try {
      console.log(`[FAL POLL] Checking status (attempt ${attempt + 1}/${maxAttempts}) for ${requestId}`);

      // Проверяем статус через Fal API (поддержка разных моделей)
      let statusUrl;
      if (model === 'fal-ai/veo3') {
        statusUrl = `https://queue.fal.run/fal-ai/veo3/requests/${requestId}/status`;
      } else {
        statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
      }

      const statusResponse = await fetch(statusUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Key ${falKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!statusResponse.ok) {
        throw new Error(`Status check failed: ${statusResponse.status}`);
      }

      const statusData = await statusResponse.json();
      console.log(`[FAL POLL] Status for ${requestId}:`, statusData.status);

      if (statusData.status === 'COMPLETED') {
        console.log(`[FAL POLL] Generation completed for ${requestId}, fetching result`);

        // Получаем результат через response_url из статуса
        const resultResponse = await fetch(statusData.response_url, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!resultResponse.ok) {
          throw new Error(`Result fetch failed: ${resultResponse.status}`);
        }

        const resultData = await resultResponse.json();
        console.log(`[FAL POLL] Result for ${requestId}:`, resultData);

        if (resultData.video && resultData.video.url) {
          // Сохраняем видео в БД
          await saveVideoToDatabase(resultData.video.url, model, userId, prompt, visibility, requestId);
          console.log(`[FAL POLL] Video saved to database for ${requestId}`);
          return;
        } else {
          throw new Error('No video URL in result');
        }
      }

      if (statusData.status === 'FAILED') {
        console.error(`[FAL POLL] Generation failed for ${requestId}`);

        // Возвращаем кредиты пользователю
        if (cost && cost > 0) {
          try {
            await updateUserCredits(userId, cost, 'refund', `Fal API Generation Failed (${model})`);
            console.log(`[FAL POLL] Refunded ${cost} credits to user ${userId}`);
          } catch (refundError) {
            console.error(`[FAL POLL] Error refunding credits:`, refundError);
          }
        }

        throw new Error('Video generation failed');
      }

      // Статус все еще "IN_QUEUE" или "IN_PROGRESS", ждем и опрашиваем снова
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;

    } catch (error) {
      console.error(`[FAL POLL] Error during polling (attempt ${attempt + 1}):`, error);

      // Если это последняя попытка, возвращаем кредиты
      if (attempt >= maxAttempts - 1) {
        if (cost && cost > 0) {
          try {
            await updateUserCredits(userId, cost, 'refund', `Fal API Timeout (${model})`);
            console.log(`[FAL POLL] Refunded ${cost} credits due to timeout for user ${userId}`);
          } catch (refundError) {
            console.error(`[FAL POLL] Error refunding credits:`, refundError);
          }
        }
        throw error;
      }

      // Ждем перед следующей попыткой
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;
    }
  }

  console.error(`[FAL POLL] Timeout after ${maxAttempts} attempts for ${requestId}`);

  // Возвращаем кредиты при таймауте
  if (cost && cost > 0) {
    try {
      await updateUserCredits(userId, cost, 'refund', `Fal API Timeout (${model})`);
      console.log(`[FAL POLL] Refunded ${cost} credits due to timeout for user ${userId}`);
    } catch (refundError) {
      console.error(`[FAL POLL] Error refunding credits:`, refundError);
    }
  }

  throw new Error('Timeout waiting for video generation');
}

/**
 * Сохраняет видео в базу данных
 */
async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[FAL POLL] Saving video to database: ${videoUrl}`);

    // Скачиваем видео
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[FAL POLL] Downloaded video, size: ${videoBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    // Загружаем видео в Supabase Storage
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[FAL POLL] Video uploaded to Storage: ${supabaseVideoUrl}`);

    // Генерируем thumbnail
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[FAL POLL] Thumbnail uploaded: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[FAL POLL] Error generating thumbnail:`, thumbnailError);
    }

    // Определяем стоимость
    let cost = 100; // По умолчанию
    if (model === 'fal-ai/veo3') {
      cost = 1280; // Veo3 стоит 1280 кредитов за 8 секунд
    } else if (model.includes('standard')) {
      cost = 75; // 5s = 75, 10s = 150
    } else if (model.includes('pro')) {
      cost = 120; // 5s = 120, 10s = 240
    } else if (model.includes('master')) {
      cost = 320; // 5s = 320, 10s = 640
    }

    // Сохраняем в БД
    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId, // Используем fal_request_id вместо replicate_prediction_id
      duration: model === 'fal-ai/veo3' ? 8 : 5, // Veo3 = 8 секунд, остальные = 5
      cost: cost,
      visibility: visibility
    });

    console.log(`[FAL POLL] Video generation saved to database with ID: ${videoId}`);

    // Автоматически создаем GIF превью для dashboard
    try {
      console.log(`[FAL POLL] Starting GIF creation for generation ${savedGeneration.id}`);
      await createGifThumbnail(supabaseVideoUrl, savedGeneration.id, userId);
      console.log(`[FAL POLL] GIF thumbnail created successfully for generation ${savedGeneration.id}`);
    } catch (gifError) {
      console.error(`[FAL POLL] Error creating GIF thumbnail:`, gifError);
      // Не прерываем процесс, так как видео уже сохранено
    }

  } catch (error) {
    console.error(`[FAL POLL] Error saving video to database:`, error);
    throw error;
  }
}

/**
 * Создает GIF превью для видео
 */
async function createGifThumbnail(videoUrl, generationId, userId) {
  let tempVideoPath = null;
  let tempGifPath = null;

  try {
    console.log(`[FAL POLL GIF] Creating GIF for generation ${generationId}, videoUrl: ${videoUrl}`);

    // 1. Скачиваем видео
    const response = await fetch(videoUrl);
    if (!response.ok) {
      throw new Error(`Failed to download video: ${response.statusText}`);
    }

    // Создаем временную директорию
    const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'fal-gif-'));
    tempVideoPath = path.join(tempDir, `video_${generationId}.mp4`);
    tempGifPath = path.join(tempDir, `preview_${generationId}.gif`);

    // Сохраняем видео во временный файл
    const buffer = await response.buffer();
    await fs.writeFile(tempVideoPath, buffer);
    console.log(`[FAL POLL GIF] Video downloaded to ${tempVideoPath}`);

    // 2. Проверяем наличие FFmpeg
    try {
      await execAsync('ffmpeg -version');
      console.log('[FAL POLL GIF] FFmpeg found.');
    } catch (ffmpegCheckError) {
      console.error('[FAL POLL GIF] FFmpeg not found. Skipping GIF creation.');
      return; // Выходим без ошибки
    }

    // 3. Конвертируем в GIF
    const ffmpegCommand = `ffmpeg -i "${tempVideoPath}" -vf "fps=10,scale=320:-1:flags=lanczos" -c:v gif -f gif "${tempGifPath}"`;
    console.log(`[FAL POLL GIF] Executing FFmpeg command: ${ffmpegCommand}`);

    await execAsync(ffmpegCommand);
    console.log(`[FAL POLL GIF] Video converted to GIF: ${tempGifPath}`);

    // 4. Загружаем GIF в Supabase
    const gifBuffer = await fs.readFile(tempGifPath);
    const { error: uploadError } = await supabase.storage
      .from('generations') // Используем основной bucket
      .upload(`gifs/${userId}/${generationId}.gif`, gifBuffer, {
        contentType: 'image/gif',
        upsert: true
      });

    if (uploadError) {
      throw new Error(`Failed to upload GIF: ${uploadError.message}`);
    }

    // 5. Получаем публичный URL
    const { data: publicUrlData } = supabase.storage
      .from('generations')
      .getPublicUrl(`gifs/${userId}/${generationId}.gif`);

    const publicGifUrl = publicUrlData.publicUrl;
    console.log(`[FAL POLL GIF] GIF uploaded: ${publicGifUrl}`);

    // 6. Обновляем запись в БД
    const { error: dbUpdateError } = await supabase
      .from('video_generations')
      .update({ thumbnail_url: publicGifUrl })
      .eq('id', generationId);

    if (dbUpdateError) {
      throw new Error(`Failed to update database: ${dbUpdateError.message}`);
    }

    console.log(`[FAL POLL GIF] Database updated with GIF URL for generation ${generationId}`);

  } catch (error) {
    console.error('[FAL POLL GIF] Error creating GIF:', error);
    throw error;
  } finally {
    // Очистка временных файлов
    if (tempVideoPath) {
      try {
        await fs.unlink(tempVideoPath);
      } catch (e) { console.error('[FAL POLL GIF] Failed to clean up video file:', e); }
    }
    if (tempGifPath) {
      try {
        await fs.unlink(tempGifPath);
      } catch (e) { console.error('[FAL POLL GIF] Failed to clean up GIF file:', e); }
    }
  }
}
