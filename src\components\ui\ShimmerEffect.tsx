import React from 'react';
import { cn } from '@/lib/utils';

interface ShimmerEffectProps {
  children: React.ReactNode;
  isActive: boolean;
  className?: string;
}

/**
 * Компонент для создания эффекта "блестящего" свечения во время генерации
 * Добавляет движущуюся полосу света, которая проходит сверху вниз по контейнеру
 */
const ShimmerEffect: React.FC<ShimmerEffectProps> = ({ 
  children, 
  isActive, 
  className 
}) => {
  return (
    <div 
      className={cn(
        "relative",
        isActive && "shimmer-container",
        className
      )}
    >
      {children}
    </div>
  );
};

export default ShimmerEffect;
