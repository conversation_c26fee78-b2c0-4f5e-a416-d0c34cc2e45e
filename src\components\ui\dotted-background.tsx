
import React from 'react';

interface DottedBackgroundProps {
  className?: string;
  type?: 'dots' | 'grid';
}

const DottedBackground: React.FC<DottedBackgroundProps> = ({ className, type = 'dots' }) => {
  return (
    <div 
      className={`absolute inset-0 ${type === 'dots' ? 'dotted-bg' : 'grid-bg'} z-0 ${className || ''}`}
      aria-hidden="true"
    />
  );
};

export default DottedBackground;
