// Serverless function for Fal AI API (Veo3 model)
// Supports: fal-ai/veo3

import fetch from "node-fetch";
import { updateUserCredits } from "../../utils/database.js";

// Функция расчёта стоимости генерации видео для Veo3
function getVeo3GenerationCost(duration, generateAudio = true) {
  // Veo3 поддерживает только 8 секунд
  // 1400 кредитов с аудио, 1000 кредитов без аудио
  return generateAudio ? 1400 : 1000;
}

export default async function handler(req, res) {
  // Разрешаем только POST запросы
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { model, userId, inputKeys, ...input } = req.body;

    console.log('[VEO3] Received request:', { model, userId, inputKeys, input });

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[VEO3] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Проверяем обязательные параметры
    if (!input.prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Подготавливаем input для Veo3 API
    const veo3Input = {
      prompt: input.prompt,
      aspect_ratio: input.aspect_ratio || "16:9",
      duration: "8s", // Veo3 поддерживает только 8 секунд
      enhance_prompt: input.enhancePrompt !== undefined ? input.enhancePrompt : true,
      generate_audio: input.generateAudio !== undefined ? input.generateAudio : true
    };

    console.log('[VEO3] Prepared input:', veo3Input);

    // Рассчитываем стоимость с учетом параметра generateAudio
    const cost = getVeo3GenerationCost(8, veo3Input.generate_audio);
    console.log(`[VEO3] Generation cost: ${cost} credits (audio: ${veo3Input.generate_audio})`);

    // Проверяем баланс пользователя (если указан userId)
    if (userId && userId !== 'anonymous') {
      try {
        const balanceResponse = await fetch(`${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site'}/api/user/credits?userId=${userId}`);
        if (balanceResponse.ok) {
          const balanceData = await balanceResponse.json();
          const currentCredits = Number(balanceData.credits);
          
          if (currentCredits < cost) {
            console.log(`[VEO3] Insufficient credits: ${currentCredits} < ${cost}`);
            return res.status(400).json({ 
              error: 'Insufficient credits',
              required: cost,
              available: currentCredits
            });
          }
        }
      } catch (balanceError) {
        console.error('[VEO3] Error checking balance:', balanceError);
        // Продолжаем выполнение, если проверка баланса не удалась
      }
    }

    // Отправляем запрос к Fal API (как в Kling 2.1)
    console.log('[VEO3] Sending request to Fal API...');
    const falResponse = await fetch('https://queue.fal.run/fal-ai/veo3', {
      method: 'POST',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(veo3Input)
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[VEO3] Fal API error:', falResponse.status, errorText);
      return res.status(falResponse.status).json({
        error: 'Fal API error',
        details: errorText,
        status: falResponse.status
      });
    }

    const result = await falResponse.json();

    console.log('[VEO3] Fal API response:', result);

    // Списываем кредиты сразу после успешного запроса
    if (userId && userId !== 'anonymous') {
      try {
        await updateUserCredits(userId, -cost);
        console.log(`[VEO3] Deducted ${cost} credits from user ${userId}`);
      } catch (creditError) {
        console.error('[VEO3] Error deducting credits:', creditError);
        // Не прерываем выполнение, так как запрос уже отправлен
      }
    }



    // Запускаем фоновый polling для обработки результата
    if (result.request_id) {
      try {
        console.log(`[VEO3] Starting background polling for request ${result.request_id}`);

        // Запускаем фоновый polling напрямую (как в Kling 2.1)
        console.log('[VEO3] Starting direct polling function...');

        // Импортируем и вызываем функцию polling напрямую
        const { pollFalRequest } = await import('../poll.js');

        // Запускаем polling в фоне (не ждем завершения)
        pollFalRequest(result.request_id, model, userId, input.prompt, input.metadata_visibility || 'public', cost)
          .catch(error => {
            console.error('[VEO3] Background polling failed:', error);
          });

        console.log('[VEO3] Direct polling started successfully');

      } catch (pollError) {
        console.error('[VEO3] Error starting polling:', pollError);
      }
    }

    // Возвращаем результат клиенту
    return res.status(200).json({
      request_id: result.request_id,
      status: 'submitted',
      message: 'Video generation started'
    });

  } catch (error) {
    console.error('[VEO3] Unexpected error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}
