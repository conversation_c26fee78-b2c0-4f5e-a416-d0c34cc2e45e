import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { CreditPlan, creditPlans, createPayment } from '@/utils/yukassa';
import { Coins } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BuyCreditsDialogProps {
  onSuccessfulPurchase?: () => void;
  triggerButtonClassName?: string;
  variant?: "default" | "outline" | "sidebar";
}

export function BuyCreditsDialog({
  onSuccessfulPurchase,
  triggerButtonClassName,
  variant = "default"
}: BuyCreditsDialogProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<CreditPlan | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [customAmount, setCustomAmount] = useState<string>('');
  const [customCredits, setCustomCredits] = useState<number>(0);

  // Сбрасываем выбранный план при закрытии диалога
  useEffect(() => {
    if (!isOpen) {
      setSelectedPlan(null);
      setIsProcessing(false);
      setCustomAmount('');
      setCustomCredits(0);
    }
  }, [isOpen]);

  // Обработка изменения произвольной суммы
  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    const amount = parseFloat(value);
    if (!isNaN(amount) && amount >= 2000 && amount <= 100000) {
      setCustomCredits(amount * 2); // 1 рубль = 2 кредита
    } else {
      setCustomCredits(0);
    }
  };

  // Обработка выбора плана
  const handleSelectPlan = (plan: CreditPlan) => {
    console.log('Выбран план:', plan.id, plan.name);
    setSelectedPlan(plan);
  };

  // Функция для создания платежа и перехода на страницу оплаты
  const handleBuyCredits = async () => {
    if (!selectedPlan || !user) {
      toast.error(t('buyCredits.pleaseSelectPlan'));
      return;
    }
    if (!user.email) {
      toast.error(t('buyCredits.errors.noEmail'));
      return;
    }

    // Для произвольного плана проверяем введенную сумму
    if (selectedPlan.isCustomPlan) {
      const amount = parseFloat(customAmount);
      if (isNaN(amount) || amount < 2000 || amount > 100000) {
        toast.error(t('buyCredits.invalidAmount'));
        return;
      }
    }

    console.log('Нажата кнопка оплаты, план:', selectedPlan.id, selectedPlan.name);
    setIsProcessing(true);

    try {
      // Получаем текущий URL для возврата после оплаты
      const returnUrl = `${window.location.origin}/payment-callback`;

      // Определяем сумму и количество кредитов
      const amount = selectedPlan.isCustomPlan ? parseFloat(customAmount) : selectedPlan.amount;
      const credits = selectedPlan.isCustomPlan ? customCredits : selectedPlan.credits;

      console.log('Создаем платеж:', {
        amount,
        description: `${selectedPlan.name}: ${credits} ${t('buyCredits.credits')}`,
        userId: user.id,
        creditsAmount: credits,
        returnUrl
      });

      // Индикатор загрузки
      toast.loading(t('buyCredits.creatingPayment'));

      // Создаем платеж в ЮKassa
      const payment = await createPayment({
        amount,
        description: `${selectedPlan.name}: ${credits} ${t('buyCredits.credits')}`,
        userId: user.id,
        creditsAmount: credits,
        returnUrl,
        email: user.email!
      });

      console.log('Ответ API платежа:', payment);
      toast.dismiss();

      // Проверяем наличие необходимых данных в ответе
      if (!payment || !payment.id) {
        throw new Error(t('buyCredits.errors.invalidPaymentResponse'));
      }

      // Если платеж успешно создан и есть URL для подтверждения
      if (payment.confirmation && payment.confirmation.confirmation_url) {
        // Сохраняем идентификатор платежа в localStorage для последующей проверки
        localStorage.setItem('pendingPaymentId', payment.id);
        
        console.log('Перенаправление на URL платежа:', payment.confirmation.confirmation_url);
        toast.success(t('buyCredits.redirecting'), { duration: 2000 });
        
        // Небольшая задержка перед перенаправлением для показа сообщения
        setTimeout(() => {
          // Перенаправляем пользователя на страницу оплаты
          if (typeof payment.confirmation.confirmation_url === 'string') {
            window.location.href = payment.confirmation.confirmation_url;
          } else {
            toast.error(t('buyCredits.errors.noConfirmationUrl'));
          }
        }, 1000);

        // Если определен колбэк успешной покупки, вызываем его
        if (onSuccessfulPurchase) {
          onSuccessfulPurchase();
        }
      } else {
        throw new Error(t('buyCredits.errors.noConfirmationUrl'));
      }
    } catch (error) {
      console.error('Ошибка при создании платежа:', error);
      toast.dismiss();
      toast.error(
        `${t('buyCredits.errors.paymentCreation')}: ${
          error instanceof Error ? error.message : t('buyCredits.errors.unknown')
        }`
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const getButtonVariant = () => {
    switch(variant) {
      case "outline":
        return "outline";
      case "sidebar":
        return "ghost";
      default:
        return "default";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={getButtonVariant()} 
          className={cn("gap-2", triggerButtonClassName)}
        >
          <Coins className="h-4 w-4" />
          {t('buyCredits.buttonText')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {t('buyCredits.title')}
          </DialogTitle>
          <DialogDescription>
            {t('buyCredits.description')}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {creditPlans.map((plan) => (
            <div key={plan.id}>
              <div
                className={cn(
                  "flex items-start p-4 rounded-lg border-2 cursor-pointer transition-colors",
                  "hover:bg-gray-50 dark:hover:bg-zinc-800/50",
                  selectedPlan?.id === plan.id
                    ? "border-primary bg-primary/5 dark:bg-primary/10"
                    : "border-gray-200 dark:border-zinc-800"
                )}
                onClick={() => handleSelectPlan(plan)}
              >
                <div className="flex-1">
                  <h3 className="font-semibold flex items-center gap-2">
                    {plan.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {plan.description}
                  </p>
                  <div className="mt-2 flex justify-between">
                    <span className="font-semibold">
                      {plan.isCustomPlan
                        ? (customCredits > 0 ? `${customCredits} ${t('buyCredits.credits')}` : t('buyCredits.creditsCalculated', { credits: 0 }))
                        : `${plan.credits} ${t('buyCredits.credits')}`
                      }
                    </span>
                    <span className="font-bold">
                      {plan.isCustomPlan ? t('buyCredits.customAmount') : `${plan.amount} ₽`}
                    </span>
                  </div>
                </div>
              </div>

              {/* Поле ввода произвольной суммы */}
              {selectedPlan?.id === plan.id && plan.isCustomPlan && (
                <div className="mt-3 p-4 bg-gray-50 dark:bg-zinc-800/50 rounded-lg border">
                  <label className="block text-sm font-medium text-foreground mb-2">
                    {t('buyCredits.enterAmount')}
                  </label>
                  <Input
                    type="number"
                    min="2000"
                    max="100000"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    placeholder="2000"
                    className="w-full"
                  />
                  <div className="mt-2 text-xs text-muted-foreground">
                    <p>{t('buyCredits.minAmount')}</p>
                    <p>{t('buyCredits.maxAmount')}</p>
                    {customCredits > 0 && (
                      <p className="text-primary font-medium mt-1">
                        {t('buyCredits.creditsCalculated', { credits: customCredits })}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
        <DialogFooter className="flex flex-col gap-2 sm:gap-0">
          <Button
            type="submit"
            disabled={
              !selectedPlan ||
              isProcessing ||
              (selectedPlan?.isCustomPlan && (customCredits === 0 || parseFloat(customAmount) < 2000 || parseFloat(customAmount) > 100000))
            }
            onClick={handleBuyCredits}
            className="w-full bg-primary hover:bg-white hover:text-black text-primary-foreground font-semibold transition-colors duration-200"
          >
            {isProcessing
              ? t('buyCredits.loading')
              : t('buyCredits.proceedToPayment')
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 