import React, { useState, useEffect } from 'react';
import { Sheet, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet'; // SheetClose убран, т.к. стандартный крестик уже есть
import { ScrollArea } from '@/components/ui/scroll-area';
import { LazyImage } from '@/components/ui/LazyImage';
import { Video, Bookmark } from 'lucide-react'; // X убран, т.к. не используется явно и стандартный крестик должен работать
import { cn } from '@/lib/utils';

const placeholderSvg = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E";

// Вспомога<PERSON>ельная функция для безопасного получения времени из timestamp
const getSafeTime = (timestamp: any, itemId: string, itemPrompt?: string): number => {
  // Явная проверка на null или undefined в самом начале
  if (timestamp == null) { // Ловит и null, и undefined
    console.warn(`RecentGenerations: Timestamp is null or undefined (ID: ${itemId}, Prompt: "${itemPrompt?.substring(0,30)}..."). Using default for sorting.`);
    return 0;
  }

  // Сначала проверяем, является ли это уже объектом Date
  if (timestamp instanceof Date) {
    const time = timestamp.getTime();
    if (!isNaN(time)) {
      return time;
    } else {
      console.warn(`RecentGenerations: Invalid Date object provided for timestamp (ID: ${itemId}, Prompt: "${itemPrompt?.substring(0,30)}..."). Using default for sorting.`);
      return 0; // Дата-объект есть, но он Invalid Date
    }
  }

  // Если это строка или число, пытаемся создать Date
  if (typeof timestamp === 'string' || typeof timestamp === 'number') {
    // Дополнительная проверка для пустых строк, так как new Date("") это Invalid Date, но лучше залогировать явно
    if (typeof timestamp === 'string' && timestamp.trim() === "") {
        console.warn(`RecentGenerations: Empty string provided for timestamp (ID: ${itemId}, Prompt: "${itemPrompt?.substring(0,30)}..."). Using default for sorting.`);
        return 0;
    }
    const date = new Date(timestamp);
    const time = date.getTime();
    if (!isNaN(time)) {
      return time;
    } else {
      console.warn(`RecentGenerations: Could not parse date string/number for timestamp (ID: ${itemId}, Value: "${timestamp}", Prompt: "${itemPrompt?.substring(0,30)}..."). Using default for sorting.`);
      return 0;
    }
  }
  
  let valueDetail = "";
  if (timestamp !== null && typeof timestamp !== "undefined") {
    try {
      valueDetail = String(timestamp);
      if (valueDetail.length > 50) valueDetail = valueDetail.substring(0, 50) + "...";
    } catch (e) {
      valueDetail = "[unstringifiable value]";
    }
    valueDetail = `, Value: "${valueDetail}"`;
  }
  
  console.warn(`RecentGenerations: Missing or invalid type for timestamp (ID: ${itemId}, Type: ${typeof timestamp}${valueDetail}, Prompt: "${itemPrompt?.substring(0,30)}..."). Using default for sorting.`);
  return 0;
};

/**
 * Универсальная функция для получения валидного URL изображения.
 */
const getValidImageUrl = (url: string | string[] | undefined): string => {
  if (!url) return placeholderSvg; // Возвращаем placeholder если URL не предоставлен
  const realUrl = Array.isArray(url) ? url[0] : url;
  if (!realUrl || typeof realUrl !== 'string') return placeholderSvg; // Возвращаем placeholder если URL некорректен

  if (realUrl.startsWith('data:')) {
    return realUrl;
  }
  if (realUrl.startsWith('http')) {
    return realUrl;
  }
  // Если URL не data и не http, считаем его потенциально некорректным или локальным путем, который может не работать.
  // Для безопасности можно возвращать placeholder или обрабатывать иначе. Пока возвращаем как есть.
  console.warn('Потенциально некорректный URL изображения (не data:, не http(s)):', String(realUrl).substring(0, 50) + '...');
  return realUrl; // или placeholderSvg для большей безопасности
};

interface RecentGenerationItem {
  id: string;
  type: "image" | "video";
  prompt: string;
  result?: string | string[]; // Может быть одним URL, массивом URL или отсутствовать
  timestamp: Date;
  local?: boolean;
  aspectRatioString?: string; // e.g., "1024:1024" - для превью карточки
  aspect_ratio?: string;      // e.g., "1:1" - для деталей, может приходить от API
  image_url?: string;         // Альтернативное поле для URL изображения
  imageUrl?: string;          // Еще одно альтернативное поле
  image_urls?: string[];      // Поле для массива URL изображений
  thumbnail_url?: string;     // Поле для URL миниатюры видео
}

interface RecentGenerationsProps {
  generations: Array<RecentGenerationItem>;
  type: "image" | "video";
  className?: string;
}

const RecentGenerations = ({ generations, type, className }: RecentGenerationsProps) => {
  const [localGenerations, setLocalGenerations] = useState<RecentGenerationItem[]>([]);

  // Обновляем локальное состояние, когда изменяются props
  useEffect(() => {
    // Используем Map для более эффективной дедупликации по id
    const uniqueMap = new Map();
    
    // Добавляем все генерации в Map с id в качестве ключа - это автоматически уберет дубликаты
    generations.forEach(gen => {
      // Перезаписываем значение в Map, чтобы взять самую последнюю версию записи с этим id
      uniqueMap.set(gen.id, gen);
    });
    
    // Преобразуем Map в массив
    const uniqueGenerations = Array.from(uniqueMap.values());

    // Фильтруем, чтобы оставить только "завершенные" генерации (с изображениями/видео)
    const completeGenerations = uniqueGenerations.filter(gen => {
      const hasResultsArray = Array.isArray(gen.result) && gen.result.length > 0 && gen.result.some((url: string) => url && url !== placeholderSvg);
      const hasResultString = typeof gen.result === 'string' && gen.result && gen.result !== placeholderSvg;
      const hasImageUrlsArray = Array.isArray(gen.image_urls) && gen.image_urls.length > 0 && gen.image_urls.some((url: string) => url && url !== placeholderSvg);
      const hasImageUrlField = typeof gen.image_url === 'string' && gen.image_url && gen.image_url !== placeholderSvg;
      const hasImageUrlProp = typeof gen.imageUrl === 'string' && gen.imageUrl && gen.imageUrl !== placeholderSvg;
      return hasResultsArray || hasResultString || hasImageUrlsArray || hasImageUrlField || hasImageUrlProp;
    }).sort((a, b) => { // Сортируем завершенные генерации по timestamp (новые сверху)
      const timeA = getSafeTime(a.timestamp, a.id, a.prompt);
      const timeB = getSafeTime(b.timestamp, b.id, b.prompt);
      return timeB - timeA;
    });
    
    // Ограничиваем количество элементов для производительности
    // Ограничиваем количество элементов до 5 для "Recent Images"
    const limitedGenerations = completeGenerations.slice(0, 5);
    
    setLocalGenerations(limitedGenerations);
  }, [generations, type]);

  // Добавляем обработчик события обновления генераций
  useEffect(() => {
    const handleGenerationsUpdated = (_event: Event) => {
      // Ничего не делаем, так как родительский компонент должен обновить props
    };

    window.addEventListener('generationsUpdated', handleGenerationsUpdated);

    return () => {
      window.removeEventListener('generationsUpdated', handleGenerationsUpdated);
    };
  }, []);
  
  // Если нет генераций, показываем сообщение
  // const displayableGenerations = localGenerations.filter(gen => !erroredImageIds.has(gen.id)); // Logic moved to LazyImage
  const displayableGenerations = localGenerations; // LazyImage will return null for errored images

  if (displayableGenerations.length === 0) {
    return (
      <div className={cn("bg-background rounded-xl p-4 border w-full", className)}>
        <h3 className="text-lg font-bold mb-3">Recent {type === "image" ? "Images" : "Videos"}</h3>
        <p className="text-sm text-foreground/60 text-center py-4">
          No recent {type === "image" ? "images" : "videos"} found.
        </p>
      </div>
    );
  }

  return (
    <div className={cn("bg-background rounded-xl p-4 border w-full", className)}>
      <h3 className="text-lg font-bold mb-3">Recent {type === "image" ? "Images" : "Videos"}</h3>
      <div className="space-y-3">
        {displayableGenerations.map((generation) => (
          <Sheet key={generation.id}>
            <SheetTrigger asChild>
              <div 
                className="bg-card p-2 rounded-lg border cursor-pointer hover:border-primary transition-colors group"
              >
                {type === "image" && (
                  <div
                    className="relative rounded-2xl overflow-hidden mb-2 max-h-60 md:max-h-48 aspect-[auto]" // Используем aspect-auto для LazyImage ниже
                  >
                    <LazyImage
                      src={getValidImageUrl(generation.image_url || generation.imageUrl || (generation.image_urls && generation.image_urls[0]) || (Array.isArray(generation.result) ? generation.result[0] : generation.result))}
                      alt={generation.prompt || "Generated image preview"}
                      className={cn(
                        "w-full h-full object-cover rounded-2xl",
                        `aspect-[${(generation.aspectRatioString || generation.aspect_ratio || "1/1").replace(":", "/")}]`
                      )}
                      placeholderClassName="w-full h-full object-cover bg-gray-200 dark:bg-gray-700 rounded-2xl"
                      // generationId={generation.id} // Убрано, т.к. LazyImageProps его не ожидает
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition flex items-center justify-center">
                      <Bookmark className="w-5 h-5 text-white" />
                    </div>
                    {generation.local && (
                      <div className="absolute top-1 right-1 bg-yellow-600 text-white text-xs px-1 rounded">
                        Local
                      </div>
                    )}
                  </div>
                )}

                {type === "video" && (
                  <div className="relative aspect-[16/9] rounded-2xl overflow-hidden mb-2">
                    {generation.thumbnail_url && generation.thumbnail_url.endsWith('.gif') ? (
                      <img
                        src={generation.thumbnail_url}
                        alt={generation.prompt ? `${generation.prompt} - GIF preview` : "Video GIF preview"}
                        className="w-full h-full object-cover rounded-2xl bg-black"
                        style={{ maxHeight: 240 }}
                      />
                    ) : (
                      <video
                        src={Array.isArray(generation.result) ? generation.result[0] : generation.result}
                        controls
                        className="w-full h-full object-cover rounded-2xl bg-black"
                        poster={generation.thumbnail_url}
                        preload="metadata"
                        crossOrigin="anonymous"
                        onError={(e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
                          console.error('Ошибка при загрузке/воспроизведении видео:', generation.id, 'URL:', e.currentTarget.src, 'Error:', e.currentTarget.error);
                          const videoElement = e.currentTarget;
                          if (videoElement.poster !== placeholderSvg) {
                            videoElement.poster = placeholderSvg;
                          }
                          if (e.currentTarget.error) {
                            console.error('Детали ошибки медиа:', {
                              code: e.currentTarget.error.code,
                              message: e.currentTarget.error.message,
                              MEDIA_ERR_ABORTED: MediaError.MEDIA_ERR_ABORTED,
                              MEDIA_ERR_NETWORK: MediaError.MEDIA_ERR_NETWORK,
                              MEDIA_ERR_DECODE: MediaError.MEDIA_ERR_DECODE,
                              MEDIA_ERR_SRC_NOT_SUPPORTED: MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED,
                              MEDIA_ERR_ENCRYPTED: (MediaError as any).MEDIA_ERR_ENCRYPTED
                            });
                          }
                        }}
                      />
                    )}
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center pointer-events-none">
                      <Video className="w-5 h-5 text-white" />
                    </div>
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition flex items-center justify-center pointer-events-none">
                      <Bookmark className="w-5 h-5 text-white" />
                    </div>
                    {generation.local && (
                      <div className="absolute top-1 right-1 bg-yellow-600 text-white text-xs px-1 rounded">
                        Local
                      </div>
                    )}
                  </div>
                )}
                <p className="text-sm text-foreground/70 truncate">
                  {generation.prompt}
                </p>
              </div>
            </SheetTrigger>
            {/*
              SheetContent:
              - Проблема 1 (Лишний крестик): Стандартный крестик от Sheet/SheetHeader используется.
                                        Убрал явный SheetClose, чтобы избежать дублирования.
              - Проблема 4 (Фон модального окна): bg-background/90 backdrop-blur-sm применен.
            */}
            <SheetContent className="w-[95%] sm:max-w-lg bg-background/90 backdrop-blur-sm flex flex-col p-0">
              <SheetHeader className="p-6 pb-2 border-b"> {/* Уменьшил pb и добавил border-b для отделения заголовка */}
                <SheetTitle>Generation Details</SheetTitle>
              </SheetHeader>
              {/*
                Контейнер для контента с прокруткой:
                - flex-grow и min-h-0 важны для правильной работы ScrollArea или div c overflow внутри flex-контейнера.
              */}
              <ScrollArea className="flex-grow min-h-0"> {/* Обернул весь контент в ScrollArea */}
                <div className="p-6 space-y-4">
                  {/*
                    Проблема 3 (Отображение нескольких изображений) и Проблема 5 (Рамка изображений):
                    - Используем Carousel для нескольких изображений.
                    - LazyImage с object-contain и rounded-lg. Контейнер с overflow-hidden.
                  */}
                  {type === "image" && (() => {
                    let imageUrls: string[] = [];
                    if (Array.isArray(generation.image_urls) && generation.image_urls.length > 0) {
                      imageUrls = generation.image_urls.map(url => getValidImageUrl(url));
                    } else if (Array.isArray(generation.result) && generation.result.length > 0) {
                      imageUrls = generation.result.map(url => getValidImageUrl(url));
                    } else if (typeof generation.image_url === 'string') {
                      imageUrls = [getValidImageUrl(generation.image_url)];
                    } else if (typeof generation.imageUrl === 'string') {
                      imageUrls = [getValidImageUrl(generation.imageUrl)];
                    } else if (typeof generation.result === 'string') {
                      imageUrls = [getValidImageUrl(generation.result)];
                    }
                    imageUrls = imageUrls.filter(url => url !== placeholderSvg); // Убираем пустые/невалидные после getValidImageUrl

                    if (imageUrls.length === 0) {
                      return (
                        <div className="mb-4 p-4 text-center text-sm text-muted-foreground bg-muted rounded-md">
                          No image found or all image URLs are invalid.
                        </div>
                      );
                    }

                    if (imageUrls.length > 1) {
                      // Используем сетку для нескольких изображений
                      return (
                        <div
                          className={
                            imageUrls.length === 4
                              ? "mb-4 grid grid-cols-2 grid-rows-2 gap-2"
                              : "mb-4 grid grid-cols-2 gap-2"
                          }
                        >
                          {imageUrls.map((imgUrl: string, index: number) => (
                            <div
                              key={index}
                              className="aspect-square rounded-2xl overflow-hidden border border-border bg-background"
                              // style={{ borderRadius: "1rem" }} // Заменено на rounded-2xl
                            >
                              <LazyImage
                                src={imgUrl}
                                alt={generation.prompt ? `${generation.prompt} - Image ${index + 1}` : `Generated image ${index + 1}`}
                                className="w-full h-full object-contain"
                                placeholderClassName="w-full h-full object-contain bg-gray-200 dark:bg-gray-700"
                              />
                            </div>
                          ))}
                        </div>
                      );
                    } else if (imageUrls.length === 1) {
                      // Одиночное изображение
                      return (
                        // Внешний div для центрирования и отступов
                        <div className="mb-4 flex justify-center items-center">
                          {/* Контейнер, который задает форму (скругление) и обрезает контент */}
                          <div className="w-full max-w-lg max-h-[60vh] rounded-2xl overflow-hidden bg-muted/30 dark:bg-muted/10 flex justify-center items-center"> {/* Уменьшен max-w и max-h */}
                            <LazyImage
                              src={imageUrls[0]}
                              alt={generation.prompt || "Generated image"}
                              className="object-contain w-auto h-auto max-w-full max-h-full" // Заполняет контейнер, rounded-lg убрано. max-h-full чтобы не превышать контейнер.
                              placeholderClassName="object-contain w-auto h-auto max-w-full max-h-full bg-gray-200 dark:bg-gray-700" // rounded-lg убрано
                            />
                          </div>
                        </div>
                      );
                    }
                    // Если нет изображений (хотя мы фильтровали), ничего не возвращаем или можно вернуть сообщение.
                    // Но getValidImageUrl и filter(url => url !== placeholderSvg) должны были это обработать.
                    return null;
                  })()}

                  {type === "video" && (
                    <div className="mb-4 rounded-2xl overflow-hidden bg-black/10 dark:bg-white/5">
                      {generation.thumbnail_url && generation.thumbnail_url.endsWith('.gif') ? (
                        <img
                          src={generation.thumbnail_url}
                          alt={generation.prompt ? `${generation.prompt} - GIF preview` : "Video GIF preview"}
                          className="w-full max-h-[70vh] rounded-2xl object-contain bg-black"
                        />
                      ) : (
                        <video
                          src={Array.isArray(generation.result) ? generation.result[0] : generation.result}
                          controls
                          className="w-full max-h-[70vh] rounded-2xl"
                          poster={getValidImageUrl(Array.isArray(generation.result) ? generation.result[0] : generation.result)}
                          crossOrigin="anonymous"
                          onError={(e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
                            console.error('Ошибка при загрузке видео:', generation.id);
                            const videoElement = e.currentTarget;
                            if (videoElement.poster !== placeholderSvg) {
                              videoElement.poster = placeholderSvg;
                            }
                          }}
                        />
                      )}
                    </div>
                  )}

                  {/* Кнопка скачивания оригинального видео */}
                  {type === "video" && (
                    <div className="flex justify-end mt-2">
                      <button
                        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/80 transition"
                        onClick={async () => {
                          const videoUrl = Array.isArray(generation.result) ? generation.result[0] : generation.result;
                          if (!videoUrl) return;
                          try {
                            const response = await fetch(videoUrl);
                            if (!response.ok) throw new Error('Ошибка загрузки видео');
                            const blob = await response.blob();
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = `video-${generation.id}.mp4`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(link.href);
                          } catch (e) {
                            alert('Ошибка скачивания видео');
                          }
                        }}
                      >
                        Скачать видео
                      </button>
                    </div>
                  )}

                  {/*
                    Проблема 2 (Обрезание промта):
                    - ScrollArea для промта теперь не имеет max-h, т.к. внешняя ScrollArea обработает весь контент.
                  */}
                  <div className="mt-2">
                    <h4 className="text-sm font-semibold mb-1">Prompt</h4>
                    <div className="text-sm text-foreground/80 whitespace-pre-wrap break-words p-3 border rounded-md bg-background/60">
                      {generation.prompt || "No prompt provided."}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-semibold mb-1">Generated on</h4>
                    <p className="text-sm text-foreground/80">
                      {new Date(generation.timestamp).toLocaleString()}
                    </p>
                  </div>

                  {generation.local && (
                    <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-md border border-yellow-300 dark:border-yellow-700">
                      <p className="text-xs text-yellow-800 dark:text-yellow-500">
                        This generation is stored locally in your browser and not in the cloud.
                      </p>
                    </div>
                  )}
                </div> {/* Закрытие div.p-6.space-y-4 */}
              </ScrollArea> {/* Закрытие ScrollArea обертки */}
            </SheetContent>
          </Sheet>
        ))}
      </div>
    </div>
  );
};

export default RecentGenerations;
