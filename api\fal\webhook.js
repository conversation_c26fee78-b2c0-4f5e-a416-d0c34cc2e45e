// FAL API webhook endpoint: POST /api/fal/webhook
// FAL будет отправлять сюда POST-запросы при завершении генерации

import { saveSpeechGeneration, downloadFile, uploadAudioToSupabase } from "../utils/database.js";

export default async function handler(req, res) {
  console.log("[FAL WEBHOOK] Received request:", req.method, req.url);
  console.log("[FAL WEBHOOK] Headers:", req.headers);
  console.log("[FAL WEBHOOK] Raw body:", req.body);

  if (req.method !== "POST") {
    console.log("[FAL WEBHOOK] Method not allowed:", req.method);
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  try {
    console.log("[FAL WEBHOOK] Processing webhook request");

    // FAL отправляет данные в теле запроса
    const body = req.body && typeof req.body === "object" ? req.body : (() => {
      try {
        return JSON.parse(req.body || "{}");
      } catch (parseError) {
        console.error("[FAL WEBHOOK] JSON parse error:", parseError);
        return {};
      }
    })();

    // Логируем входящие данные
    console.log("[FAL WEBHOOK] Parsed body:", JSON.stringify(body, null, 2));

    // Проверяем, что это завершенная генерация
    if (body.status === "COMPLETED" && body.data) {
      try {
        console.log("[FAL WEBHOOK] Обрабатываем завершенную генерацию");
        
        const requestId = body.request_id;
        const resultData = body.data;
        
        console.log("[FAL WEBHOOK] Request ID:", requestId);
        console.log("[FAL WEBHOOK] Result data:", resultData);

        // Проверяем, что это речевая генерация
        if (resultData.audio && resultData.audio.url) {
          console.log("[FAL WEBHOOK] Найдена речевая генерация");
          
          // Получаем метаданные из webhook'а или из базы данных
          const userId = body.metadata?.userId || body.metadata?.user_id;
          const text = body.metadata?.text || "";
          const voice = body.metadata?.voice || "Rachel";
          const model = body.metadata?.model || "fal-ai/elevenlabs/tts/turbo-v2.5";
          const visibility = body.metadata?.visibility || "public";

          console.log("[FAL WEBHOOK] Метаданные:", { userId, text, voice, model, visibility });

          if (userId && text) {
            try {
              console.log("[FAL WEBHOOK] Сохраняем аудио в базу данных...");
              
              // Скачиваем аудио
              const audioBuffer = await downloadFile(resultData.audio.url);
              console.log(`[FAL WEBHOOK] Downloaded audio, size: ${audioBuffer.length} bytes`);

              // Генерируем уникальное имя файла
              const { v4: uuidv4 } = await import('uuid');
              const audioId = uuidv4();
              const timestamp = Date.now();
              const fileName = `${audioId}/${timestamp}-audio.mp3`;

              // Загружаем аудио в Supabase Storage
              const supabaseAudioUrl = await uploadAudioToSupabase(audioBuffer, fileName);
              console.log(`[FAL WEBHOOK] Audio uploaded to Storage: ${supabaseAudioUrl}`);

              // Рассчитываем стоимость
              let cost = 5; // Минимум
              if (model.includes('turbo-v2.5')) {
                cost = Math.max(5, Math.ceil((text.length / 1000) * 20));
              } else if (model.includes('multilingual-v2')) {
                cost = Math.max(5, Math.ceil((text.length / 1000) * 40));
              }

              // Сохраняем в БД
              const savedGeneration = await saveSpeechGeneration({
                user_id: userId,
                text: text,
                audio_url: supabaseAudioUrl,
                voice_id: voice,
                voice_name: voice,
                model: model,
                parameters: {
                  voice: voice,
                  fal_request_id: requestId
                },
                cost: cost,
                public: visibility === 'public'
              });

              console.log(`[FAL WEBHOOK] Speech generation saved to database with ID: ${savedGeneration.id}`);

              // Списываем кредиты
              const { updateUserCredits } = await import("../utils/database.js");
              await updateUserCredits(userId, -cost, 'generation', `ElevenLabs TTS (${model})`);
              console.log(`[FAL WEBHOOK] Списано ${cost} кредитов за речевую генерацию для user: ${userId}`);

            } catch (saveError) {
              console.error("[FAL WEBHOOK] Ошибка при сохранении аудио:", saveError);
            }
          } else {
            console.log("[FAL WEBHOOK] Недостаточно метаданных для сохранения:", { userId, text });
          }
        } else {
          console.log("[FAL WEBHOOK] Не найдено аудио в результате");
        }

      } catch (err) {
        console.error("[FAL WEBHOOK] Ошибка при обработке результата:", err);
      }
    } else {
      console.log("[FAL WEBHOOK] Получено уведомление, но генерация не завершена:", body.status);
    }

    // Возвращаем OK
    res.status(200).json({ received: true, request_id: body.request_id, status: body.status });
    
  } catch (error) {
    console.error("[FAL WEBHOOK] Ошибка обработки:", error);
    res.status(500).json({ error: "Webhook error", details: error.message });
  }
}
