import { API_CONFIG, createApiUrl } from '@/config/api';
import { toast } from 'sonner';

// Interface for text generation request
interface Message {
  role: "user" | "assistant";
  content: string;
  images?: string[];
}

interface TextGenerationRequest {
  model:
    | 'geminiPro'
    | 'geminiFlash'
    | 'geminiImage'
    | 'mistralM'    // mistral-medium-2505
    | 'mistralL'    // mistral-large-2411
    | 'pixtralL';   // pixtral-large-2411
  prompt: string;
  messages?: Message[];
  images?: string[];
  maxTokens?: number;
  temperature?: number;
}


// Interface for image generation request
interface ImageGenerationRequest {
  prompt: string;
  numImages?: number;
  style?: string;
}

// Text generation service
export const generateText = async ({
  model,
  prompt,
  messages,
  images,
  maxTokens = 1024,
  temperature = 0.7,
}: TextGenerationRequest) => {
  try {
    // Ограничение истории сообщений и изображений для предотвращения 413
    let limitedMessages = messages;
    if (messages && messages.length > 0) {
      // Оставляем только последние 10 сообщений
      limitedMessages = messages.slice(-10).map(msg => ({
        ...msg,
        // Оставляем только первые 2 изображения на сообщение
        images: msg.images ? msg.images.slice(0, 2) : undefined,
        // Ограничиваем длину текста сообщения
        content: msg.content.length > 2000 ? msg.content.slice(0, 2000) : msg.content,
      }));
    }

    // Gemini
    if (model === 'geminiPro' || model === 'geminiFlash' || model === 'geminiImage') {
      let requestBody = {
        prompt: prompt.length > 2000 ? prompt.slice(0, 2000) : prompt,
        model: 'gemini-2.5-pro-exp-03-25', // дефолтная модель (будет заменена на актуальную в api/google/generate-text)
        temperature,
        maxOutputTokens: maxTokens,
      };
      // Можно добавить обработку messages/изображений для Gemini при необходимости

      const response = await fetch('/api/google/generate-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorText = await response.text();
        try {
          const errorData = JSON.parse(errorText);
          console.error('API error:', errorData);
          throw new Error(errorData.error || 'Error generating text');
        } catch {
          // Не JSON — выводим текст ошибки
          console.error('API error:', errorText);
          throw new Error(errorText || 'Error generating text');
        }
      }

      const data = await response.json();
      return data;
    }

    // Mistral
    // Алиасы: mistralM → mistral-medium-2505, mistralL → mistral-large-2411, pixtralL → pixtral-large-2411
    let mistralModel = '';
    if (model === 'mistralM') mistralModel = 'mistral-medium-2505';
    if (model === 'mistralL') mistralModel = 'mistral-large-2411';
    if (model === 'pixtralL') mistralModel = 'pixtral-large-2411';

    // Формируем сообщения для Mistral API
    const mistralMessages: any[] = [];
    if (limitedMessages && limitedMessages.length > 0) {
      for (const msg of limitedMessages) {
        mistralMessages.push({
          role: msg.role,
          content: msg.content,
        });
        // Если есть изображения — добавляем их как отдельные части (Mistral API поддерживает image_url)
        if (msg.images && msg.images.length > 0) {
          msg.images.forEach((img) => {
            mistralMessages.push({
              role: msg.role,
              content: [
                {
                  type: "image_url",
                  image_url: { url: img },
                },
              ],
            });
          });
        }
      }
    } else {
      mistralMessages.push({
        role: "user",
        content: prompt.length > 2000 ? prompt.slice(0, 2000) : prompt,
      });
      if (images && images.length > 0) {
        images.slice(0, 2).forEach((img) => {
          mistralMessages.push({
            role: "user",
            content: [
              {
                type: "image_url",
                image_url: { url: img },
              },
            ],
          });
        });
      }
    }

    const mistralRequestBody = {
      model: mistralModel,
      messages: mistralMessages,
      temperature,
      max_tokens: maxTokens,
    };

    // Retry логика для Mistral API
    let response: Response | undefined;
    let retryCount = 0;
    const maxRetries = 3;
    const baseDelay = 1000; // 1 секунда

    while (retryCount <= maxRetries) {
      try {
        response = await fetch('/api/mistral/generate-text', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mistralRequestBody),
        });

        // Если получили 429, ждем и повторяем
        if (response.status === 429 && retryCount < maxRetries) {
          const delay = baseDelay * Math.pow(2, retryCount); // Экспоненциальная задержка
          console.log(`Mistral API rate limit (429). Retrying in ${delay/1000} seconds... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          retryCount++;
          continue;
        }

        break; // Выходим из цикла если запрос успешен или это не 429
      } catch (fetchError) {
        if (retryCount === maxRetries) {
          throw fetchError;
        }
        retryCount++;
        const delay = baseDelay * Math.pow(2, retryCount - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!response) {
      throw new Error('Failed to get response from Mistral API after retries');
    }

    if (!response.ok) {
      let errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        console.error('Mistral API error:', errorData);

        // Специальная обработка ошибки 429 (превышение пропускной способности)
        if (response.status === 429) {
          if (errorData.originalError && errorData.originalError.includes('Service tier capacity exceeded')) {
            throw new Error('Пропускная способность модели Mistral превышена. Попробуйте другую модель.');
          } else {
            throw new Error('Модель Mistral перегружена. Попробуйте позже или используйте другую модель.');
          }
        }

        throw new Error(errorData.message || errorData.error || 'Error generating text (Mistral)');
      } catch (parseError) {
        // Не JSON — выводим текст ошибки
        console.error('Mistral API error:', errorText);

        // Специальная обработка ошибки 429 даже если не JSON
        if (response.status === 429) {
          throw new Error('Превышена квота Mistral API. Попробуйте позже или используйте другую модель (например, Gemini).');
        }

        throw new Error(errorText || 'Error generating text (Mistral)');
      }
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error generating text:', error);
    toast.error('Не удалось сгенерировать текст. Пожалуйста, попробуйте еще раз.');
    throw error;
  }
};

// Image generation service (uses the text API with a special system prompt)
export const generateImage = async ({ prompt, numImages = 1, style }: ImageGenerationRequest) => {
  try {
    const enhancedPrompt = style 
      ? `${prompt}. Style: ${style}` 
      : prompt;
    
    const imageGenerationPrompt = `Generate ${numImages} detailed image${numImages > 1 ? 's' : ''} based on this prompt. Only respond with image descriptions, no additional text: ${enhancedPrompt}`;
    
    const url = createApiUrl(`${API_CONFIG.imageGeneration.baseUrl}${API_CONFIG.imageGeneration.model}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: imageGenerationPrompt
              }
            ]
          }
        ],
        generationConfig: {
          maxOutputTokens: 1024,
          temperature: 0.9,
        }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API error:', errorData);
      throw new Error(errorData.error?.message || 'Error generating image');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error generating image:', error);
    toast.error('Failed to generate image. Please try again.');
    throw error;
  }
};

// Function to save generation to history
export const saveToHistory = (type: 'text' | 'image' | 'video', prompt: string, result: any) => {
  try {
    // Get existing history from localStorage
    const historyString = localStorage.getItem('umaai_history');
    const history = historyString ? JSON.parse(historyString) : [];
    
    // Create new history item
    const newItem = {
      id: Date.now(),
      type,
      prompt,
      result,
      timestamp: new Date().toISOString()
    };
    
    // Add to history and limit to 50 items
    const updatedHistory = [newItem, ...history].slice(0, 50);
    
    // Save back to localStorage
    localStorage.setItem('umaai_history', JSON.stringify(updatedHistory));
    
    return newItem;
  } catch (error) {
    console.error('Error saving to history:', error);
    return null;
  }
};

// Function to get user history
export const getUserHistory = () => {
  try {
    const historyString = localStorage.getItem('umaai_history');
    return historyString ? JSON.parse(historyString) : [];
  } catch (error) {
    console.error('Error getting history:', error);
    return [];
  }
};
