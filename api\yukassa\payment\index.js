// Импортируем необходимые модули
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

// Константы с данными для аутентификации в ЮKassa
const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
const API_URL = 'https://api.yookassa.ru/v3';

export default async function handler(req, res) {
  // Явно устанавливаем заголовок Content-Type для JSON
  res.setHeader('Content-Type', 'application/json');
  
  // Разрешаем только POST-запросы для создания платежа
  if (req.method === 'POST') {
    try {
      console.log('Получен запрос на создание платежа:', req.body);
      const {
        amount,
        currency = 'RUB',
        description = 'Покупка кредитов UMA.AI',
        userId,
        creditsAmount,
        returnUrl,
        email
      } = req.body;

      if (!amount || !userId || !creditsAmount || !returnUrl || !email) {
        console.error('Неверные параметры запроса:', { amount, userId, creditsAmount, returnUrl, email });
        return res.status(400).json({
          error: 'Отсутствуют обязательные параметры',
          details: 'Необходимо указать amount, userId, creditsAmount, returnUrl и email (для чека)'
        });
      }

      // Создаем уникальный ключ идемпотентности для запроса
      const idempotenceKey = uuidv4();
      
      // Формируем данные для запроса
      const paymentData = {
        amount: {
          value: typeof amount === 'number' ? amount.toFixed(2) : amount,
          currency
        },
        capture: true,
        confirmation: {
          type: 'redirect',
          return_url: returnUrl
        },
        description,
        metadata: {
          userId,
          creditsAmount
        },
        receipt: {
          items: [
            {
              description: description,
              quantity: 1,
              amount: {
                value: typeof amount === 'number' ? amount.toFixed(2) : amount,
                currency
              },
              vat_code: 1 // Без НДС
            }
          ],
          customer: {
            email: email
          }
        }
      };
      
      console.log('Отправка запроса в ЮKassa:', paymentData);
      
      // HTTP Basic Auth
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      try {
        // Отправляем запрос к API ЮKassa
        const response = await axios.post(`${API_URL}/payments`, paymentData, {
          headers: {
            'Authorization': `Basic ${authHeader}`,
            'Idempotence-Key': idempotenceKey,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('Получен ответ от ЮKassa:', response.data);
        return res.status(200).json(response.data);
      } catch (axiosError) {
        console.error('Ошибка API ЮKassa:', axiosError.response?.data || axiosError.message);
        return res.status(axiosError.response?.status || 500).json({
          error: 'Ошибка API ЮKassa',
          details: axiosError.response?.data || axiosError.message,
          code: axiosError.response?.status || 500
        });
      }
    } catch (error) {
      console.error('Общая ошибка при создании платежа:', error);
      return res.status(500).json({
        error: 'Ошибка при создании платежа',
        details: error.message || 'Неизвестная ошибка',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  } else {
    // Если метод запроса не поддерживается
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      error: 'Method Not Allowed',
      message: `Method ${req.method} is not allowed`
    });
  }
}