import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import AppLayout from '@/components/layout/AppLayout';
import { processSuccessfulPayment } from '@/utils/yukassa';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle2, XCircle } from 'lucide-react';

const PaymentCallback = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState<'success' | 'failed' | null>(null);
  const [creditsAmount, setCreditsAmount] = useState<number | null>(null);

  // При загрузке страницы, обрабатываем платеж
  // Флаг для защиты от повторных вызовов processPayment
  const isProcessingPayment = React.useRef(false);

  useEffect(() => {
    const processPayment = async () => {
      if (isProcessingPayment.current) return;
      isProcessingPayment.current = true;

      // Получаем идентификатор платежа из localStorage
      const paymentId = localStorage.getItem('pendingPaymentId');

      if (!paymentId) {
        console.error('Идентификатор платежа не найден');
        setPaymentStatus('failed');
        setIsProcessing(false);
        isProcessingPayment.current = false;
        return;
      }

      try {
        // Обрабатываем платеж
        const result = await processSuccessfulPayment(paymentId);

        // Очищаем идентификатор платежа из localStorage в любом случае
        localStorage.removeItem('pendingPaymentId');

        if (result.success && result.creditsAmount) {
          // Платеж успешен
          setPaymentStatus('success');
          setCreditsAmount(result.creditsAmount);

          // Показываем уведомление об успешном пополнении баланса
          toast.success(
            i18n.language === 'ru'
              ? `Платеж успешно обработан!`
              : `Payment processed successfully!`,
            {
              description: i18n.language === 'ru'
                ? `На ваш счет зачислено ${result.creditsAmount} кредитов`
                : `${result.creditsAmount} credits have been added to your account`,
              duration: 5000
            }
          );
        } else {
          // Платеж не прошел или уже был обработан
          setPaymentStatus('failed');

          toast.error(
            i18n.language === 'ru'
              ? 'Ошибка при обработке платежа'
              : 'Error processing payment',
            {
              description: i18n.language === 'ru'
                ? 'Пожалуйста, проверьте статус операции в личном кабинете'
                : 'Please check the status of the operation in your personal account',
              duration: 5000
            }
          );
        }
      } catch (error) {
        console.error('Ошибка при обработке платежа:', error);
        setPaymentStatus('failed');
        
        toast.error(
          i18n.language === 'ru'
            ? 'Произошла ошибка'
            : 'An error occurred',
          {
            description: i18n.language === 'ru'
              ? 'Пожалуйста, обратитесь в службу поддержки'
              : 'Please contact customer support',
            duration: 5000
          }
        );
      } finally {
        setIsProcessing(false);
        isProcessingPayment.current = false;
      }
    };

    if (user) {
      processPayment();
    } else {
      // Если пользователь не авторизован, перенаправляем на страницу входа
      navigate('/login');
    }
  }, [user, i18n.language, navigate]);

  // Функция для перехода на профиль пользователя
  const handleGoToProfile = () => {
    navigate('/profile');
  };

  // Функция для перехода на главную страницу
  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>
              {isProcessing ? (
                i18n.language === 'ru' ? 'Обработка платежа...' : 'Processing payment...'
              ) : paymentStatus === 'success' ? (
                i18n.language === 'ru' ? 'Платеж успешно обработан' : 'Payment successful'
              ) : (
                i18n.language === 'ru' ? 'Ошибка при обработке платежа' : 'Payment failed'
              )}
            </CardTitle>
            <CardDescription>
              {isProcessing ? (
                i18n.language === 'ru' ? 'Пожалуйста, подождите...' : 'Please wait...'
              ) : paymentStatus === 'success' ? (
                i18n.language === 'ru'
                  ? `Кредиты успешно зачислены на ваш счет`
                  : `Credits have been successfully added to your account`
              ) : (
                i18n.language === 'ru'
                  ? 'Возникла проблема при обработке вашего платежа'
                  : 'There was a problem processing your payment'
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center py-6">
            {isProcessing ? (
              <Loader2 className="h-16 w-16 text-primary animate-spin" />
            ) : paymentStatus === 'success' ? (
              <>
                <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
                <p className="text-lg font-semibold">
                  {i18n.language === 'ru'
                    ? `Добавлено ${creditsAmount} кредитов`
                    : `Added ${creditsAmount} credits`}
                </p>
              </>
            ) : (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </CardContent>
          <CardFooter className="flex justify-center gap-4">
            {!isProcessing && (
              <>
                {paymentStatus === 'success' ? (
                  <Button onClick={handleGoToProfile}>
                    {i18n.language === 'ru' ? 'Перейти в профиль' : 'Go to profile'}
                  </Button>
                ) : (
                  <Button variant="outline" onClick={handleGoHome}>
                    {i18n.language === 'ru' ? 'Вернуться на главную' : 'Return to home'}
                  </Button>
                )}
              </>
            )}
          </CardFooter>
        </Card>
      </div>
    </AppLayout>
  );
};

export default PaymentCallback;
