// DashboardActionButton — кнопка для Dashboard с гифкой по ховеру, переводами и поддержкой темы
import { useContext } from "react";
import ThemeContext from "@/context/ThemeContext";
import { useNavigate } from "react-router-dom";

type DashboardActionButtonProps = {
  type: "image" | "video" | "speech" | "text";
  className?: string;
};

const gifMap = {
  image: {
    light: "/222Light.gif",
    dark: "/222dark.gif",
  },
  video: {
    light: "/333Light.gif",
    dark: "/333dark.gif",
  },
  speech: {
    light: "/speech.webp",
    dark: "/speech.webp",
  },
  text: {
    light: "/text.webp",
    dark: "/text.webp",
  },
};

import { useTranslation } from "react-i18next";
export function DashboardActionButton({ type, className }: DashboardActionButtonProps) {
  const { i18n } = useTranslation();
  const { theme } = useContext(ThemeContext) || { theme: "light" };
  const navigate = useNavigate();
  // Тексты и переводы
  const titles = {
    image: {
      en: "Create Image",
      ru: "Создать изображение",
    },
    video: {
      en: "Create Video",
      ru: "Создать видео",
    },
    speech: {
      en: "Create Speech",
      ru: "Создать речь",
    },
    text: {
      en: "Chat",
      ru: "Чат",
    },
  };
  const descriptions = {
    image: {
      en: "Generate any image your imagination desires.",
      ru: "Создавайте любые изображения, какие только пожелаете.",
    },
    video: {
      en: "Create any video you want with AI.",
      ru: "Создавайте любые видео с помощью ИИ.",
    },
    speech: {
      en: "Generate speech from text with AI voices.",
      ru: "Генерируйте речь из текста с помощью ИИ голосов.",
    },
    text: {
      en: "Chat with AI and generate text content.",
      ru: "Общайтесь с ИИ и генерируйте текстовый контент.",
    },
  };

  const lang = i18n.language.startsWith("ru") ? "ru" : "en";
  const title = titles[type][lang];
  const description = descriptions[type][lang];

  // Гифка и статичный кадр по теме
  const gifSrc = gifMap[type][theme === "dark" ? "dark" : "light"];
  const staticMap = {
    image: {
      light: "/222light.png",
      dark: "/222dark.png",
    },
    video: {
      light: "/333Light.png",
      dark: "/333dark.png",
    },
    speech: {
      light: "/speech.webp",
      dark: "/speech.webp",
    },
    text: {
      light: "/text.webp",
      dark: "/text.webp",
    },
  };
  const staticSrc = staticMap[type][theme === "dark" ? "dark" : "light"];

  // Навигация
  const handleClick = () => {
    if (type === "image") navigate("/image");
    else if (type === "video") navigate("/video");
    else if (type === "speech") navigate("/speech");
    else if (type === "text") navigate("/text");
  };

  return (
    <button
      className={
        "relative flex flex-col justify-between rounded-2xl border border-black/10 dark:border-white/10 shadow-lg dark:shadow-none overflow-hidden group cursor-pointer transition-all duration-200 bg-white dark:bg-background hover:shadow-xl dark:hover:shadow-none focus:outline-none flex-shrink-0 " +
        "w-[622px] max-w-full h-[150px] px-8 py-5 text-left " +
        (className || "")
      }
      onClick={handleClick}
      style={{ minWidth: 300, minHeight: 120 }}
    >
      <div className="relative z-10">
        <div className="font-extrabold text-2xl sm:text-3xl mb-1 text-black dark:text-white">{title}</div>
        <div className="text-base sm:text-lg text-neutral-700 dark:text-neutral-300 font-normal">{description}</div>
      </div>
      {/* SVG стрелка */}
      <div className="absolute bottom-4 right-6 z-20">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
          <path d="M8 16H24" stroke={theme === "dark" ? "#fff" : "#222"} strokeWidth="2" strokeLinecap="round"/>
          <path d="M18 10L24 16L18 22" stroke={theme === "dark" ? "#fff" : "#222"} strokeWidth="2" strokeLinecap="round"/>
        </svg>
      </div>
      {/* Статичный кадр всегда, гифка только по ховеру для image/video */}
      <div className="absolute inset-0 z-0 pointer-events-none">
        <img
          src={staticSrc}
          alt=""
          className={cn(
            "w-full h-full object-cover",
            (type === "image" || type === "video") ? "group-hover:opacity-0 transition-opacity duration-300" : ""
          )}
          draggable={false}
          style={{ zIndex: 1, position: "absolute", inset: 0, display: "block" }}
        />
        {(type === "image" || type === "video") && (
          <img
            src={gifSrc}
            alt=""
            className="w-full h-full object-cover absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            draggable={false}
            style={{ zIndex: 2, position: "absolute", inset: 0, display: "block" }}
          />
        )}
      </div>
    </button>
  );
}
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { Copy, Download, X } from 'lucide-react'; // Оставляем только используемые
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose, // DialogFooter removed
} from "@/components/ui/dialog";
// Используем псевдонимы для второго модального окна, чтобы избежать конфликтов имен
// и явно указать, что это другой инстанс диалога для другой цели.
// Однако, если мы используем тот же компонент Dialog, псевдонимы не нужны,
// достаточно управлять состоянием open/onOpenChange для каждого отдельно.
// Решено не использовать псевдонимы, а просто разные состояния.
import { Button } from "@/components/ui/button";
// Импорт обеих функций получения генераций
import { getAllImageGenerations, getAllVideoGenerations, incrementGenerationLike, GenerationGroup } from '@/utils/database';
// import { useAuth } from '@/context/AuthContext'; // Not directly used in this version of Dashboard display logic
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import LazyImage from '@/components/ui/LazyImage';
import MasonryGrid from '../components/ui/MasonryGrid';
import SEOHead from '@/components/SEOHead';
import NewsCarousel from '@/components/dashboard/NewsCarousel';


import { Heart } from 'lucide-react';

const GenerationGridItem = ({
  item,
  onItemClick,
  onLike,
  liked,
}: {
  item: GenerationGroup & { thumbnail_url?: string };
  onItemClick: (item: GenerationGroup) => void;
  onLike?: (id: string) => void;
  liked?: boolean;
}) => {
  // Только промт, обычный текст, лайк и счетчик
  return (
    <div
      className="relative group neo-brutalist-white dark:neo-brutalist-black rounded-xl overflow-hidden border border-black/10 dark:border-white/10 shadow-sm cursor-pointer break-inside-avoid mb-3 sm:mb-4"
      onClick={() => onItemClick(item)}
      style={{ pageBreakInside: 'avoid', breakInside: 'avoid-column' }}
    >
      <div className="relative w-full h-auto">
        {item.model === 'video' ? (
          item.thumbnail_url && item.thumbnail_url.endsWith('.gif') ? (
            <img
              src={item.thumbnail_url}
              alt={item.prompt.substring(0, 50) || `Generated video preview for ${item.id}`}
              className="w-full h-auto object-contain bg-black"
              loading="lazy"
            />
          ) : (
            <video
              src={item.images && item.images.length > 0 ? item.images[0] : '/placeholder.svg'}
              poster={item.thumbnail_url}
              autoPlay
              loop
              muted
              playsInline
              className="w-full h-auto object-contain bg-black"
              onClick={e => { e.stopPropagation(); onItemClick(item); }}
            />
          )
        ) : (
          <LazyImage
            src={item.images && item.images.length > 0 ? item.images[0] : '/placeholder.svg'}
            alt={item.prompt.substring(0, 50) || `Generated image for ${item.id}`}
            className="w-full h-auto object-contain"
            placeholderClassName="w-full h-auto bg-gray-200 dark:bg-gray-800"
          />
        )}
        <div className="absolute bottom-0 left-0 right-0 p-2.5 bg-gradient-to-t from-black/70 via-black/50 to-transparent flex items-center justify-between">
          <span className="text-white text-[1rem] font-normal truncate max-w-[80%]">{item.prompt.length > 60 ? item.prompt.substring(0, 60) + '...' : item.prompt}</span>
          <button
            className={cn(
              "flex items-center gap-1 transition",
              "border-none bg-transparent shadow-none outline-none focus:outline-none"
            )}
            onClick={e => {
              e.stopPropagation();
              onLike?.(item.id);
            }}
            aria-label="Поставить лайк"
          >
            <span className="text-sm font-medium" style={{ color: "#fff" }}>{item.likes}</span>
            <Heart
              size={18}
              fill={liked ? "#e11d48" : "#fff"}
              stroke={liked ? "#e11d48" : "none"}
              style={{ marginLeft: 4, transition: "fill 0.2s, stroke 0.2s" }}
            />
          </button>
        </div>
      </div>
    </div>
  );
};

const GenerationGridItemSkeleton = () => (
  <div className="neo-brutalist-white dark:neo-brutalist-black rounded-xl overflow-hidden border border-black/10 dark:border-white/10 shadow-sm break-inside-avoid mb-3 sm:mb-4">
    <Skeleton className="w-full aspect-video" /> {/* Placeholder aspect ratio */}
    <div className="p-3">
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-3 w-full mb-1" />
    </div>
  </div>
);

const Dashboard = () => {
  const { t, i18n } = useTranslation();
  // const { user } = useAuth();
  const [generations, setGenerations] = useState<GenerationGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedGeneration, setSelectedGeneration] = useState<GenerationGroup | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // State for expanding prompt text within the modal
  const [isPromptExpandedInModal, setIsPromptExpandedInModal] = useState(false);
  const [promptRequiresExpansion, setPromptRequiresExpansion] = useState(false);
  const promptModalRef = useRef<HTMLParagraphElement>(null);

  // Infinite scroll state
  const [visibleCount, setVisibleCount] = useState(20);
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  // Состояния для модального окна предпросмотра изображения
  const [previewImageSrc, setPreviewImageSrc] = useState<string | null>(null);
  const [isImagePreviewModalOpen, setIsImagePreviewModalOpen] = useState(false);

  // Drag scrolling для кнопок
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Drag scrolling handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  // --- Лайки ---
  // Сохраняем id генераций, которые пользователь лайкнул (localStorage)
  const [likedIds, setLikedIds] = useState<string[]>(() => {
    try {
      return JSON.parse(localStorage.getItem('dashboard_liked_ids') || '[]');
    } catch {
      return [];
    }
  });

  const handleLike = useCallback(async (id: string) => {
    setGenerations(prev =>
      prev.map(gen =>
        gen.id === id
          ? { ...gen, likes: (gen.likes ?? 0) + (likedIds.includes(id) ? -1 : 1) }
          : gen
      )
    );
    setLikedIds(prev => {
      let updated;
      if (prev.includes(id)) {
        updated = prev.filter(x => x !== id);
      } else {
        updated = [...prev, id];
      }
      localStorage.setItem('dashboard_liked_ids', JSON.stringify(updated));
      return updated;
    });

    // Определяем тип генерации (image/video) и отправляем лайк в БД
    const gen = generations.find(g => g.id === id);
    if (!gen) return;
    const type = gen.model === 'video' ? 'video' : 'image';
    try {
      const newLikes = await incrementGenerationLike(type, id, likedIds.includes(id) ? -1 : 1);
      // Синхронизируем счетчик лайков с сервером сразу после успешного ответа
      setGenerations(prev =>
        prev.map(gen =>
          gen.id === id
            ? { ...gen, likes: newLikes }
            : gen
        )
      );
      // Обновляем кэш в localStorage
      const cachedGenerationsData = localStorage.getItem('dashboard_generations');
      if (cachedGenerationsData) {
        try {
          const parsedGenerations = JSON.parse(cachedGenerationsData);
          const updatedGenerations = parsedGenerations.map((gen: any) =>
            gen.id === id ? { ...gen, likes: newLikes } : gen
          );
          localStorage.setItem('dashboard_generations', JSON.stringify(updatedGenerations));
        } catch (e) {
          // ignore cache update error
        }
      }
    } catch (e) {
      // Можно добавить toast или откатить локальный лайк при ошибке
      // toast.error('Ошибка при синхронизации лайка');
    }
  }, [likedIds, generations]);

  useEffect(() => {
    // When modal opens or selected generation changes, check if prompt needs expansion button
    if (isModalOpen && selectedGeneration && promptModalRef.current) {
      const el = promptModalRef.current;
      // Temporarily unclamp to measure full height
      el.classList.remove('line-clamp-3');
      const isOverflowing = el.scrollHeight > el.clientHeight;
      el.classList.add('line-clamp-3'); // Re-apply clamp for initial view

      setPromptRequiresExpansion(isOverflowing);
      setIsPromptExpandedInModal(false); // Reset expansion state
    } else if (!isModalOpen) {
      setIsPromptExpandedInModal(false); // Reset when modal closes
      setPromptRequiresExpansion(false);
    }
  }, [isModalOpen, selectedGeneration]);


  const fetchGenerations = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) {
      setIsLoading(true);
    }
    try {
      const cachedGenerationsData = localStorage.getItem('dashboard_generations');
      const lastUpdated = localStorage.getItem('dashboard_generations_timestamp');
      const now = new Date().getTime();

      if (cachedGenerationsData && isInitialLoad) {
        try {
          const parsedGenerations = JSON.parse(cachedGenerationsData);
          if (Array.isArray(parsedGenerations) && parsedGenerations.length > 0) {
            const processed = parsedGenerations.map(gen => {
              const ts = gen.timestamp && !isNaN(new Date(gen.timestamp).getTime()) ? new Date(gen.timestamp) : new Date(0);
              const imgs = Array.isArray(gen.images) ? gen.images : [];
              return { ...gen, timestamp: ts, images: imgs, likes: typeof gen.likes === 'number' ? gen.likes : 0 };
            });
            setGenerations(processed);
            if (isInitialLoad) setIsLoading(false);
          }
        } catch (parseError) {
          console.error('Ошибка при разборе кэшированных данных:', parseError);
          if (isInitialLoad) setIsLoading(false); // Ensure loading stops on error
        }
      }

      const shouldRefresh = !lastUpdated || (now - parseInt(lastUpdated, 10)) > 5 * 60 * 1000; // 5 minutes

      if (!isInitialLoad || shouldRefresh || !cachedGenerationsData) {
        console.log(isInitialLoad ? 'Загрузка новых данных из базы' : 'Обновление данных из базы (фоновое/событие)');

        // Получаем генерации изображений и видео, объединяем и перемешиваем
        const [imageGens, videoGens] = await Promise.all([
          getAllImageGenerations(),
          getAllVideoGenerations()
        ]);
        const allGens = [...imageGens, ...videoGens];

        // Удаляем дубликаты по id
        const uniqueMap = new Map<string, GenerationGroup>();
        allGens.forEach(gen => {
          const currentTimestamp = gen.timestamp && !isNaN(new Date(gen.timestamp).getTime()) ? new Date(gen.timestamp) : new Date(0);
          const currentImages = Array.isArray(gen.images) ? gen.images : [];
          if (uniqueMap.has(gen.id)) {
            const existingEntry = uniqueMap.get(gen.id)!;
            if (currentImages.length > 0 && existingEntry.images.length === 0) {
              uniqueMap.set(gen.id, { ...gen, images: currentImages, timestamp: currentTimestamp, likes: typeof gen.likes === 'number' ? gen.likes : 0 });
            } else if (currentImages.length === existingEntry.images.length && currentTimestamp.getTime() > existingEntry.timestamp.getTime()){
              uniqueMap.set(gen.id, { ...gen, images: currentImages, timestamp: currentTimestamp, likes: typeof gen.likes === 'number' ? gen.likes : 0 });
            }
          } else {
            uniqueMap.set(gen.id, { ...gen, images: currentImages, timestamp: currentTimestamp, likes: typeof gen.likes === 'number' ? gen.likes : 0 });
          }
        });
        // Сортировка по популярности (likes DESC, timestamp DESC)
        const uniqueGens = Array.from(uniqueMap.values()).sort((a, b) => {
          if ((b.likes ?? 0) !== (a.likes ?? 0)) return (b.likes ?? 0) - (a.likes ?? 0);
          return b.timestamp.getTime() - a.timestamp.getTime();
        });

        console.log(`Загружено ${allGens.length} генераций (включая видео), после удаления дубликатов: ${uniqueGens.length}`);

        try {
          const dataToCache = uniqueGens.slice(0, 100);
          localStorage.setItem('dashboard_generations', JSON.stringify(dataToCache));
          localStorage.setItem('dashboard_generations_timestamp', now.toString());
        } catch (cacheError: any) {
          console.error('Ошибка при сохранении кэша:', cacheError);
          if (cacheError instanceof DOMException && (cacheError.name === 'QuotaExceededError' || cacheError.code === 22 || cacheError.code === 1014)) {
            try {
              console.warn('QuotaExceededError, очистка кэша и попытка сохранить меньший объем.');
              localStorage.removeItem('dashboard_generations');
              localStorage.removeItem('dashboard_generations_timestamp');
              const minimalCache = uniqueGens.slice(0, 20);
              localStorage.setItem('dashboard_generations', JSON.stringify(minimalCache));
              localStorage.setItem('dashboard_generations_timestamp', now.toString());
            } catch (retryError) {
              console.error('Не удалось сохранить даже минимальный кэш:', retryError);
            }
          }
        }

        setGenerations(uniqueGens); // Уже отсортировано по популярности
        if (isInitialLoad) setIsLoading(false);
      }
    } catch (error) {
      console.error('Error fetching generations:', error);
      toast.error(t('general.error'));
      setGenerations([]);
      if (isInitialLoad) setIsLoading(false);
    }
  }, [t]);

  useEffect(() => {
    console.log('Запуск эффекта загрузки генераций в Dashboard');
    fetchGenerations(true);

    const handleGenerationsUpdatedEvent = () => {
      console.log('Обнаружено обновление генераций на дашборде (событие), обновляем список...');
      fetchGenerations(false);
    };

    window.addEventListener('generationsUpdated', handleGenerationsUpdatedEvent);

    const intervalId = setInterval(() => {
      console.log('Тихое фоновое обновление данных дашборда (интервал)...');
      fetchGenerations(false);
    }, 30000);

    return () => {
      console.log('Очистка эффекта Dashboard: удаление слушателя и интервала');
      window.removeEventListener('generationsUpdated', handleGenerationsUpdatedEvent);
      clearInterval(intervalId);
    };
  }, [fetchGenerations]);

  const filteredGenerations = useMemo(() => {
    // Не сортируем по лайкам, только фильтрация по вкладке
    if (activeTab === 'all') return generations.filter(gen => !gen.local);
    if (activeTab === 'image') return generations.filter(gen => (!gen.model || gen.model !== 'video') && !gen.local);
    if (activeTab === 'video') return generations.filter(gen => gen.model === 'video' && !gen.local);
    return generations.filter(gen => !gen.local);
  }, [generations, activeTab]);

  const handleItemClick = (item: GenerationGroup) => {
    setSelectedGeneration(item);
    setIsModalOpen(true);
  };

  const handleCopyPrompt = useCallback(async () => {
    if (!selectedGeneration || !selectedGeneration.prompt) return;
    try {
      await navigator.clipboard.writeText(selectedGeneration.prompt);
      toast.success(t('dashboard.modal.promptCopied', 'Prompt copied to clipboard!'));
    } catch (err) {
      console.error('Failed to copy prompt: ', err);
      toast.error(t('dashboard.modal.promptCopyError', 'Failed to copy prompt.'));
    }
  }, [selectedGeneration, t]);

  const handleDownloadImage = useCallback(async () => {
    if (!selectedGeneration || !selectedGeneration.images || selectedGeneration.images.length === 0) return;

    const isVideo = selectedGeneration.model === 'video';
    const fileUrl = isVideo ? selectedGeneration.images[0] : selectedGeneration.images[0];
    const promptPart = selectedGeneration.prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = new Date().toISOString().slice(0, 10);
    const fileName = isVideo
      ? `uma_ai_video_${promptPart}_${timestamp}.mp4`
      : `uma_ai_image_${promptPart}_${timestamp}.png`;

    try {
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.statusText}`);
      }
      const blob = await response.blob();

      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

      toast.success(
        t(
          isVideo ? 'dashboard.modal.downloadStartedVideo' : 'dashboard.modal.downloadStarted',
          isVideo ? 'Video download started!' : 'Image download started!'
        )
      );
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error(
        t(
          isVideo ? 'dashboard.modal.downloadErrorVideo' : 'dashboard.modal.downloadError',
          isVideo ? 'Failed to download video. The link might be broken or expired.' : 'Failed to download image. The link might be broken or expired.'
        )
      );
    }
  }, [selectedGeneration, t]);

  const renderGenerationsGrid = (gens: GenerationGroup[]) => {
    if (isLoading) {
      const skeletons = Array(10).fill(0).map((_, i) => (
        <GenerationGridItemSkeleton key={`skel-${i}`} />
      ));
      return (
        <MasonryGrid
          columnCount={{ default: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
          gap={16}
        >
          {skeletons}
        </MasonryGrid>
      );
    }

    if (gens.length === 0) {
      return (
        <div className="text-center text-black/60 dark:text-white/60 py-10">
          {t(activeTab === 'image' ? 'dashboard.noImageExamples' : activeTab === 'video' ? 'dashboard.noVideoExamples' : 'dashboard.noExamples')}
        </div>
      );
    }

    // Показываем только visibleCount элементов
    const visibleItems = gens.slice(0, visibleCount);
    const gridItems = visibleItems.map((item) => (
      <GenerationGridItem
        key={item.id}
        item={item}
        onItemClick={handleItemClick}
        onLike={handleLike}
        liked={likedIds.includes(item.id)}
      />
    ));

    return (
      <>
        <MasonryGrid
          columnCount={{ default: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
          gap={16}
        >
          {gridItems}
        </MasonryGrid>
        <div ref={sentinelRef} style={{ height: 1 }} />
      </>
    );
  };

  const handleImageClickForPreview = (imageUrl: string) => {
    setPreviewImageSrc(imageUrl);
    setIsImagePreviewModalOpen(true);
  };

  // Теперь используем кастомный MasonryGrid вместо CSS Columns

  // IntersectionObserver для подгрузки
  useEffect(() => {
    if (!sentinelRef.current) return;
    const observer = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setVisibleCount((prev) => prev + 20);
        }
      },
      { root: null, rootMargin: '200px', threshold: 0 }
    );
    observer.observe(sentinelRef.current);
    return () => observer.disconnect();
  }, [sentinelRef, generations, activeTab]);

  return (
    <AppLayout>
      <SEOHead
        title={i18n.language === 'ru'
          ? "Дашборд - UMA.AI"
          : "Dashboard - UMA.AI"
        }
        description={i18n.language === 'ru'
          ? "Просматривайте лучшие генерации сообщества UMA.AI. Изображения и видео, созданные с помощью искусственного интеллекта."
          : "Browse the best generations from UMA.AI community. Images and videos created with artificial intelligence."
        }
        keywords={i18n.language === 'ru'
          ? "дашборд, галерея ИИ, сообщество, генерации, изображения, видео"
          : "dashboard, AI gallery, community, generations, images, videos"
        }
        url="https://umaai.site/dashboard"
      />
      <div className="relative min-h-screen bg-white dark:bg-background px-4 sm:pl-2 sm:pr-5 sm:-ml-[170px]">
        {/* News Carousel */}
        <div className="pt-6 mb-6">
          <NewsCarousel />
        </div>

        {/* Отступ сверху и горизонтальные кнопки */}
        <div className="mb-8">
          {/* Десктопная версия - горизонтальный скролл с drag */}
          <div className="hidden sm:block">
            <div
              ref={scrollContainerRef}
              className="overflow-x-auto scrollbar-none cursor-grab active:cursor-grabbing"
              onMouseDown={handleMouseDown}
              onMouseLeave={handleMouseLeave}
              onMouseUp={handleMouseUp}
              onMouseMove={handleMouseMove}
              style={{ userSelect: 'none' }}
            >
              <div className="flex gap-6 justify-start items-center min-w-max px-2">
                <DashboardActionButton type="image" />
                <DashboardActionButton type="video" />
                <DashboardActionButton type="speech" />
                <DashboardActionButton type="text" />
              </div>
            </div>
          </div>

          {/* Мобильная версия - только Create Image и Create Video */}
          <div className="block sm:hidden">
            <div className="flex flex-col gap-6 justify-center items-center">
              <DashboardActionButton type="image" />
              <DashboardActionButton type="video" />
            </div>
          </div>
        </div>

        <div className="relative z-10 mb-6 sm:mb-10">
          <Tabs
            defaultValue="all"
            className="w-full"
            onValueChange={(value) => setActiveTab(value)}
          >
            <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto mb-4 sm:mb-6 neo-brutalist-white dark:neo-brutalist-black border border-black/10 dark:border-white/10">
              <TabsTrigger value="all">{t('dashboard.tabAll')}</TabsTrigger>
              <TabsTrigger value="image">{t('dashboard.tabImage')}</TabsTrigger>
              <TabsTrigger value="video">{t('dashboard.tabVideo')}</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              {renderGenerationsGrid(filteredGenerations)}
            </TabsContent>

            <TabsContent value="image">
              {renderGenerationsGrid(filteredGenerations.filter(gen => !gen.model || gen.model !== 'video'))}
            </TabsContent>

            <TabsContent value="video">
              {renderGenerationsGrid(filteredGenerations.filter(gen => gen.model === 'video'))}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {selectedGeneration && (
        <Dialog open={isModalOpen} onOpenChange={(isOpen) => {
          setIsModalOpen(isOpen);
          if (!isOpen) {
            setIsPromptExpandedInModal(false); // Reset on close
            setPromptRequiresExpansion(false);
            // setFirstImageOrientation(null); // Reset orientation - State removed
          }
        }}>
          <DialogContent
            className={cn(
              // Standard shadcn/ui classes for positioning, animation, and base styling
              "fixed left-[50%] top-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%]",
              "duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out",
              "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
              "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
              "data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]",
              "data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",

              // My custom classes for size, flex layout, background, border, and padding
              "max-h-[95vh] flex flex-col p-0", // p-0 overrides shadcn's p-6
              "bg-white/10 dark:bg-neutral-950/10 backdrop-blur-2xl", // Custom background & blur
              "border border-white/5 dark:border-black/5", // Custom border
              "shadow-2xl rounded-xl", // Custom shadow and rounding (shadcn has sm:rounded-lg, mine is rounded-xl)
              "w-full max-w-full sm:max-w-xl md:max-w-3xl lg:max-w-5xl xl:max-w-6xl", // Responsive max width
              "overflow-hidden" // Ensure content clipping
            )}
          >
            <DialogClose className="absolute right-3 top-3 z-50 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground p-1.5">
              <X className="h-5 w-5 text-neutral-700 dark:text-neutral-300" />
              <span className="sr-only">Close</span>
            </DialogClose>

            <DialogHeader className="sr-only">
              <DialogTitle>{selectedGeneration.prompt ? selectedGeneration.prompt.substring(0, 70) + (selectedGeneration.prompt.length > 70 ? '...' : '') : t('dashboard.modal.imageTitlePlaceholder', "Generated Image")}</DialogTitle>
              <DialogDescription>
                {selectedGeneration.prompt || t('dashboard.modal.imageDescriptionPlaceholder', "Detailed view of a generated image and its options.")}
              </DialogDescription>
            </DialogHeader>

            {/* Main content wrapper for adaptive layout */}
            <div className={cn(
              "flex w-full h-full overflow-hidden",
              // Layout: column on small screens, row on medium and larger screens
              "flex-col md:flex-row"
            )}>
              {/* Left Part: Images */}
              <div className={cn(
                "flex-shrink-0 p-3 sm:p-4 md:p-5", // Adjusted padding
                "w-full md:w-3/5", // Takes full width on small, 3/5 on medium+
                "flex flex-col items-center justify-center",
                "overflow-y-auto scrollbar-thin"
              )}>
                {/* Видео или изображение */}
                {selectedGeneration.model === 'video' ? (
                  selectedGeneration.thumbnail_url && selectedGeneration.thumbnail_url.endsWith('.gif') ? (
                    <img
                      src={selectedGeneration.thumbnail_url}
                      alt={t('dashboard.modal.generatedVideoPreviewAlt', 'Generated video preview')}
                      className="block object-contain w-full h-full max-h-full rounded-xl bg-black"
                      style={{ maxHeight: '70vh' }}
                    />
                  ) : (
                    <video
                      src={selectedGeneration.images[0]}
                      poster={selectedGeneration.thumbnail_url}
                      autoPlay
                      loop
                      muted
                      playsInline
                      controls
                      className="block object-contain w-full h-full max-h-full rounded-xl bg-black"
                      style={{ maxHeight: '70vh' }}
                    />
                  )
                ) : selectedGeneration.images.length === 1 ? (
                  <div
                    className="w-full flex items-center justify-center rounded-xl overflow-hidden cursor-pointer hover:opacity-80 transition-opacity aspect-auto max-h-[70vh]"
                    onClick={() => handleImageClickForPreview(selectedGeneration.images[0])}
                  >
                    <LazyImage
                      src={selectedGeneration.images[0]}
                      alt={`${t('dashboard.modal.generatedImageAlt', 'Generated image')} 1`}
                      className="block object-contain w-full h-full max-h-full rounded-xl"
                    />
                  </div>
                ) : selectedGeneration.images.length === 2 ? (
                  // Layout for 2 images: side-by-side
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 w-full items-center"> {/* items-center for vertical alignment if heights differ */}
                    {selectedGeneration.images.map((imgSrc: string, index: number) => (
                      <div
                        key={index}
                        className="w-full aspect-auto rounded-xl overflow-hidden flex items-center justify-center relative group cursor-pointer hover:opacity-80 transition-opacity max-h-[70vh]"
                        onClick={() => handleImageClickForPreview(imgSrc)}
                      >
                        <LazyImage
                          src={imgSrc}
                          alt={`${t('dashboard.modal.generatedImageAlt', 'Generated image')} ${index + 1}`}
                          className="w-full h-full object-contain rounded-xl max-h-full"
                        />
                      </div>
                    ))}
                  </div>
                ) : selectedGeneration.images.length === 4 ? (
                  // Layout for 4 images: 2x2 grid
                  <div className="grid grid-cols-2 grid-rows-2 gap-2 w-full">
                    {selectedGeneration.images.map((imgSrc: string, index: number) => (
                      <div
                        key={index}
                        className="aspect-square rounded-xl overflow-hidden flex items-center justify-center relative group cursor-pointer hover:opacity-80 transition-opacity max-h-[70vh]"
                        onClick={() => handleImageClickForPreview(imgSrc)}
                      >
                        <LazyImage
                          src={imgSrc}
                          alt={`${t('dashboard.modal.generatedImageAlt', 'Generated image')} ${index + 1}`}
                          className="w-full h-full object-contain rounded-xl max-h-full"
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  // Fallback for other counts (e.g., 3 images), horizontal scroll
                  <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-neutral-400 dark:scrollbar-thumb-neutral-600 scrollbar-track-transparent pb-2 w-full">
                    <div className="flex space-x-2">
                      {selectedGeneration.images.map((imgSrc, index) => (
                        <div
                          key={index}
                          className="w-[200px] sm:w-[250px] md:w-[300px] flex-shrink-0 rounded-xl overflow-hidden cursor-pointer hover:opacity-80 transition-opacity aspect-auto max-h-[70vh] min-w-[40vw] sm:min-w-[200px] flex items-center justify-center"
                          onClick={() => handleImageClickForPreview(imgSrc)}
                        >
                          <LazyImage
                            src={imgSrc}
                            alt={`${t('dashboard.modal.generatedImageAlt', 'Generated image')} ${index + 1}`}
                            className="w-full h-full object-contain rounded-xl"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Part: Info and Actions */}
              <div className={cn(
                "flex flex-col justify-between p-3 sm:p-4 md:p-5",
                "w-full md:w-2/5", // Takes full width on small, 2/5 on medium+
                "md:border-l md:border-black/10 dark:md:border-white/10", // Border on medium+
                "mt-3 md:mt-0", // Margin top on small screens, removed on medium+
                "overflow-y-auto scrollbar-thin"
              )}>
                <div> {/* Scrollable content area for details (prompt moved) */}
                  {/* TODO: Add other generation details here if needed (model, style, seed, etc.) */}
                  {/* Example:
                  <div className="mt-3 text-xs text-neutral-600 dark:text-neutral-400">
                    {selectedGeneration.model && <p>Model: {selectedGeneration.model}</p>}
                    {selectedGeneration.style && <p>Style: {selectedGeneration.style}</p>}
                    {selectedGeneration.aspect_ratio && <p>Aspect Ratio: {selectedGeneration.aspect_ratio}</p>}
                  </div>
                  */}
                </div>

                {/* Prompt section moved to right panel */}
                {selectedGeneration.prompt && ( // Ensure prompt exists before rendering
                  <div className="w-full pt-3 sm:pt-4 md:pt-0 md:mb-4"> {/* Adjusted margins/paddings */}
                    <h4 className="text-base font-semibold mb-1.5 text-[#000000]">{t('dashboard.modal.prompt', 'Prompt')}</h4>
                    <div className="max-h-48 overflow-y-auto scrollbar-thin mb-2"> {/* Scroll area specifically for prompt */}
                      <p
                        ref={promptModalRef}
                        className={cn(
                          "text-sm text-black dark:text-neutral-100 whitespace-pre-wrap break-words p-0", // Prompt text color to black for light theme
                          !isPromptExpandedInModal && "line-clamp-3" // Apply line-clamp only if not expanded
                        )}
                      >
                        {selectedGeneration.prompt}
                      </p>
                    </div>
                    {promptRequiresExpansion && (
                      <Button
                        variant="link"
                        size="sm"
                        className="text-xs h-auto p-0 self-start text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                        onClick={() => setIsPromptExpandedInModal(!isPromptExpandedInModal)}
                      >
                        {isPromptExpandedInModal ? t('dashboard.modal.showLess', 'Show less') : t('dashboard.modal.showMore', 'Show more')}
                      </Button>
                    )}
                  </div>
                )}
                {/* Кнопка Use prompt */}
                {selectedGeneration.prompt && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="ml-2"
                    onClick={() => {
                      const type = selectedGeneration.model === 'video' ? 'video' : 'image';
                      const params = new URLSearchParams();
                      params.set('prompt', selectedGeneration.prompt);
                      if (selectedGeneration.model) params.set('model', selectedGeneration.model);
                      if (selectedGeneration.style) params.set('style', selectedGeneration.style);
                      if (selectedGeneration.aspect_ratio) params.set('aspect_ratio', selectedGeneration.aspect_ratio);
                      window.location.href = `/${type}?${params.toString()}`;
                    }}
                  >
                    {t('dashboard.modal.usePrompt', 'Использовать промт')}
                  </Button>
                )}

                <div className="flex items-center justify-end space-x-2 sm:space-x-3 mt-4 pt-3 border-t border-black/5 dark:border-white/5">
                  <Button
                    variant="outline"
                    size="sm"
                    className="neo-brutalist-button dark:neo-brutalist-button-dark text-xs"
                    onClick={handleCopyPrompt}
                  >
                    <Copy size={14} className="mr-1.5" />
                    {t('dashboard.modal.copyPrompt', 'Copy Prompt')}
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    className="neo-brutalist-button-primary dark:neo-brutalist-button-primary-dark text-xs"
                    onClick={handleDownloadImage}
                  >
                    <Download size={14} className="mr-1.5" />
                    {t('dashboard.modal.downloadImage', 'Download')}
                  </Button>
                </div>
              </div>
            </div>
            {/* DialogClose has been removed. The default close button from Dialog component should be used, or DialogPrimitive.Close can be re-added if needed and default is disabled via a prop. */}
          </DialogContent>
        </Dialog>
      )}

      {previewImageSrc && (
        <Dialog open={isImagePreviewModalOpen} onOpenChange={(isOpen) => {
          setIsImagePreviewModalOpen(isOpen);
          if (!isOpen) {
            // Добавим небольшую задержку перед сбросом src, чтобы анимация закрытия успела отработать
            // Хотя для DialogContent без анимации это может быть не так критично
            setTimeout(() => setPreviewImageSrc(null), 300);
          }
        }}>
          <DialogContent
            className="p-0 bg-transparent border-none shadow-none max-w-5xl w-auto h-auto max-h-[90vh] overflow-visible" // overflow-visible для X кнопки
            onInteractOutside={() => {
              // Предотвращаем закрытие по клику на оверлей, если это нежелательно
              // e.preventDefault(); // Раскомментировать, если нужно запретить закрытие по клику вне контента
            }}
            // Убедимся, что это модальное окно появляется поверх первого
            style={{ zIndex: 100 }} // Больше чем у первого DialogContent (по умолчанию 50)
          >
            {/* <div className="relative w-full h-full flex items-center justify-center"> */}
              <LazyImage
                src={previewImageSrc}
                alt={t('dashboard.modal.enlargedImageAlt', "Enlarged generated image")}
                className="block object-contain w-auto h-auto max-w-full max-h-[85vh] rounded-xl shadow-2xl"
                // placeholderClassName="bg-muted rounded-xl" // Можно добавить плейсхолдер
              />
            {/* </div> */}
            <DialogClose
              className={cn(
                "absolute -top-3 -right-3 z-[101]", // z-index выше чем у изображения
                "rounded-full bg-background/80 backdrop-blur-sm p-1.5 text-foreground/80",
                "opacity-80 ring-offset-background transition-opacity hover:opacity-100",
                "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                "disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              )}
              aria-label={t('dashboard.modal.closeImagePreview', "Close image preview")}
            >
              <X className="h-5 w-5" />
            </DialogClose>
          </DialogContent>
        </Dialog>
      )}
    </AppLayout>
  );
};

export default Dashboard;
