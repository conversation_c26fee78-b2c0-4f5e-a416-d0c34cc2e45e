import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import AppLayout from '@/components/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { SendHorizonal, Loader2, Image as ImageIcon, X, Plus, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { generateText } from '@/utils/apiUtils';
import { toast } from 'sonner';
import ModelSelector from '@/components/generation/ModelSelector';
import { useAuth } from '@/context/AuthContext';
import { useSidebarState } from '@/context/SidebarContext';
import { saveTextGeneration, saveChatToDatabase, loadChatsFromDatabase, loadChatMessagesFromDatabase } from '@/utils/database';
import DottedBackground from '@/components/ui/dotted-background';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import SEOHead from '@/components/SEOHead';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import PrivateToggle from '@/components/ui/PrivateToggle';

// Кастомная тема Prism для белого кода и голубых комментариев
const customPrismStyle = {
  ...dark,
  'pre[class*="language-"]': {
    ...dark['pre[class*="language-"]'],
    background: "#232323",
    color: "#fff",
    boxShadow: "none",
    border: "none"
  },
  'code[class*="language-"]': {
    ...dark['code[class*="language-"]'],
    background: "#232323",
    color: "#fff"
  },
  'comment': {
    color: "#60a5fa", // голубой
    fontStyle: "italic"
  }
};
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { Check, Copy } from 'lucide-react'; // Import icons for copy button
import { dataUriToBlob, uploadImageToSupabaseStorage } from '@/utils/supabase'; // Import Supabase utilities

type Message = {
  role: "user" | "assistant";
  content: string;
  images?: string[];
};

interface MessageProps {
  role: "user" | "assistant"; // Add role property
  content: string;
  isUser: boolean;
  isLoading?: boolean;
  images?: string[];
}

interface Chat {
  id: string;
  title: string;
  messages: MessageProps[];
  created_at: string;
  updated_at: string;
}

const CodeBlock = ({ node, inline, className, children, ...props }: any) => {
  const [copied, setCopied] = useState(false);
  const match = /language-(\w+)/.exec(className || '');
  const codeContent = String(children).replace(/\n$/, '');

  const handleCopy = () => {
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return !inline && match ? (
    <div className="relative rounded-md overflow-hidden my-4" style={{ background: "#232323" }}>
      <SyntaxHighlighter
        style={customPrismStyle}
        language={match[1]}
        PreTag="div"
        {...props}
        className="!my-0 !p-4 !rounded-md"
        customStyle={{
          background: "#232323",
          color: "#fff",
          boxShadow: "none",
          border: "none"
        }}
      >
        {codeContent}
      </SyntaxHighlighter>
      <CopyToClipboard text={codeContent} onCopy={handleCopy}>
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-50 transition-colors"
        >
          {copied ? <Check size={16} /> : <Copy size={16} />}
        </Button>
      </CopyToClipboard>
    </div>
  ) : (
    <code className={cn(className, "bg-gray-800 text-gray-100 px-1 py-0.5 rounded")} {...props}>
      {children}
    </code>
  );
};

// Компонент формы параметров видео
const VideoParametersForm: React.FC<CreateModeProps> = ({
  videoProject,
  setVideoProject,
  setCreateModeStep,
  calculateProjectCost
}) => {

  const handleConfirm = () => {
    setCreateModeStep('parameters');
    // Отправляем подтверждение параметров
    const event = new CustomEvent('confirmParameters');
    window.dispatchEvent(event);
  };

  const estimatedCost = calculateProjectCost(
    videoProject.duration,
    videoProject.fragmentDuration,
    videoProject.transitionType,
    videoProject.resolution
  );

  return (
    <div className="mb-6 flex justify-start">
      <Card className="max-w-[80%] w-full">
        <CardHeader>
          <CardTitle className="text-lg">Укажите параметры видео</CardTitle>
          <p className="text-sm text-foreground/70">
            Настройте параметры для создания вашего видео. Каждый параметр влияет на качество и стоимость генерации.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Длительность видео */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Общая длительность видео</Label>
            <Select
              value={videoProject.duration.toString()}
              onValueChange={(value) => setVideoProject(prev => prev ? {...prev, duration: parseInt(value)} : null)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {videoProject.fragmentDuration === 5 ? (
                  <>
                    <SelectItem value="10">10 секунд (2 фрагмента)</SelectItem>
                    <SelectItem value="15">15 секунд (3 фрагмента)</SelectItem>
                    <SelectItem value="20">20 секунд (4 фрагмента)</SelectItem>
                    <SelectItem value="30">30 секунд (6 фрагментов)</SelectItem>
                    <SelectItem value="60">60 секунд (12 фрагментов)</SelectItem>
                    <SelectItem value="120">120 секунд (24 фрагмента)</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="16">16 секунд (2 фрагмента)</SelectItem>
                    <SelectItem value="24">24 секунды (3 фрагмента)</SelectItem>
                    <SelectItem value="32">32 секунды (4 фрагмента)</SelectItem>
                    <SelectItem value="48">48 секунд (6 фрагментов)</SelectItem>
                    <SelectItem value="80">80 секунд (10 фрагментов)</SelectItem>
                    <SelectItem value="120">120 секунд (15 фрагментов)</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
            <p className="text-xs text-foreground/60">Минимум 10 секунд, максимум 2 минуты</p>
          </div>

          {/* Длительность фрагмента */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Длительность одного фрагмента</Label>
            <Select
              value={videoProject.fragmentDuration.toString()}
              onValueChange={(value) => {
                const newFragmentDuration = parseInt(value) as 5 | 8;
                setVideoProject(prev => {
                  if (!prev) return null;

                  // Автоматически корректируем длительность видео при изменении длительности фрагмента
                  let newDuration = prev.duration;
                  if (newFragmentDuration === 8) {
                    // Переходим на 8-секундные фрагменты
                    if (prev.duration === 10) newDuration = 16;
                    else if (prev.duration === 15) newDuration = 24;
                    else if (prev.duration === 20) newDuration = 32;
                    else if (prev.duration === 30) newDuration = 48;
                    else if (prev.duration === 60) newDuration = 80;
                    else if (prev.duration === 120) newDuration = 120;
                  } else {
                    // Переходим на 5-секундные фрагменты
                    if (prev.duration === 16) newDuration = 15;
                    else if (prev.duration === 24) newDuration = 20;
                    else if (prev.duration === 32) newDuration = 30;
                    else if (prev.duration === 48) newDuration = 45;
                    else if (prev.duration === 80) newDuration = 60;
                    else if (prev.duration === 120) newDuration = 120;
                  }

                  return {...prev, fragmentDuration: newFragmentDuration, duration: newDuration};
                });
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 секунд (быстрее, дешевле)</SelectItem>
                <SelectItem value="8">8 секунд (медленнее, дороже)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-foreground/60">Влияет на плавность и стоимость генерации</p>
          </div>

          {/* Формат кадра */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Формат кадра</Label>
            <Select
              value={videoProject.aspectRatio}
              onValueChange={(value) => setVideoProject(prev => prev ? {...prev, aspectRatio: value as '16:9' | '9:16' | '1:1'} : null)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="16:9">16:9 (горизонтальное, YouTube)</SelectItem>
                <SelectItem value="9:16">9:16 (вертикальное, TikTok/Instagram)</SelectItem>
                <SelectItem value="1:1">1:1 (квадратное, Instagram)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Разрешение */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Разрешение</Label>
            <Select
              value={videoProject.resolution}
              onValueChange={(value) => setVideoProject(prev => prev ? {...prev, resolution: value as '360p' | '540p' | '720p' | '1080p'} : null)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="360p">360p (быстро, экономично)</SelectItem>
                <SelectItem value="540p">540p (стандартное качество)</SelectItem>
                <SelectItem value="720p">720p (HD, хорошее качество)</SelectItem>
                <SelectItem value="1080p">1080p (Full HD, максимальное качество)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Тип переходов */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Тип переходов</Label>
            <Select
              value={videoProject.transitionType}
              onValueChange={(value) => setVideoProject(prev => prev ? {...prev, transitionType: value as 'smooth' | 'scene_change'} : null)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="smooth">Плавные переходы (кадр → кадр)</SelectItem>
                <SelectItem value="scene_change">Смена сцен (только старт-кадр)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-foreground/60">
              Плавные переходы создают более естественное видео, смена сцен — более динамичное
            </p>
          </div>

          {/* Приватность */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Приватность</Label>
              <p className="text-xs text-foreground/60">Будет ли видео видно другим пользователям</p>
            </div>
            <PrivateToggle
              isPrivate={videoProject.isPrivate}
              onToggle={(isPrivate) => setVideoProject(prev => prev ? {...prev, isPrivate} : null)}
            />
          </div>

          {/* Стоимость */}
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Стоимость проекта:</span>
              <span className="text-lg font-bold text-primary">{estimatedCost} кредитов</span>
            </div>
            <p className="text-xs text-foreground/60 mt-1">
              Без учета возможных правок и перегенераций
            </p>
          </div>

          <Button onClick={handleConfirm} className="w-full">
            Подтвердить параметры
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

// Компонент отображения сценария
const ScenarioDisplay: React.FC<CreateModeProps & {
  onRegenerateScenario: () => void;
  onConfirmScenario: () => void;
  estimatedCost: number;
}> = ({ videoProject, onRegenerateScenario, onConfirmScenario, estimatedCost }) => {
  if (!videoProject?.scenario) return null;

  return (
    <div className="mb-6 flex justify-start">
      <Card className="max-w-[80%] w-full">
        <CardHeader>
          <CardTitle className="text-lg">Ваш сценарий</CardTitle>
          <p className="text-sm text-foreground/70">
            Проверьте сценарий перед генерацией раскадровки. Вы можете пересоздать его, если что-то не устраивает.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted/50 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre className="whitespace-pre-wrap text-sm font-mono">{videoProject.scenario}</pre>
          </div>

          <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Стоимость проекта:</span>
              <span className="text-lg font-bold text-primary">{estimatedCost} кредитов</span>
            </div>
            <p className="text-xs text-foreground/60 mt-1">
              Без учета возможных правок и перегенераций
            </p>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onRegenerateScenario}
              className="flex-1"
            >
              Пересоздать сценарий
            </Button>
            <Button
              onClick={onConfirmScenario}
              className="flex-1"
            >
              Подтвердить и продолжить
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Компонент модального окна для редактирования промта
const PromptEditModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (newPrompt: string) => void;
  currentPrompt: string;
  sceneNumber: number;
}> = ({ isOpen, onClose, onSave, currentPrompt, sceneNumber }) => {
  const [editedPrompt, setEditedPrompt] = useState(currentPrompt);

  useEffect(() => {
    setEditedPrompt(currentPrompt);
  }, [currentPrompt]);

  if (!isOpen) return null;

  const handleSave = () => {
    if (editedPrompt.trim() && editedPrompt !== currentPrompt) {
      onSave(editedPrompt.trim());
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background border border-border rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-border">
          <h3 className="text-lg font-semibold">Редактировать промт для сцены {sceneNumber}</h3>
          <p className="text-sm text-foreground/70 mt-1">
            Измените описание для генерации изображения этой сцены
          </p>
        </div>

        <div className="p-6">
          <label className="block text-sm font-medium mb-2">
            Промт для генерации (на английском языке):
          </label>
          <Textarea
            value={editedPrompt}
            onChange={(e) => setEditedPrompt(e.target.value)}
            placeholder="Введите детальное описание сцены на английском языке..."
            className="w-full h-32 resize-none"
            autoFocus
          />
          <p className="text-xs text-foreground/60 mt-2">
            Совет: Используйте детальные описания с указанием типа кадра, освещения, угла камеры и стиля
          </p>
        </div>

        <div className="p-6 border-t border-border flex gap-3 justify-end">
          <Button variant="outline" onClick={onClose}>
            Отмена
          </Button>
          <Button
            onClick={handleSave}
            disabled={!editedPrompt.trim() || editedPrompt === currentPrompt}
          >
            Сохранить и перегенерировать
          </Button>
        </div>
      </div>
    </div>
  );
};

// Компонент отображения отдельных видео фрагментов
const VideoFragmentsResult: React.FC<CreateModeProps & {
  messages: MessageProps[];
  setMessages: React.Dispatch<React.SetStateAction<MessageProps[]>>;
  user: any;
}> = ({ videoProject, setVideoProject, setCreateModeStep, messages, setMessages, user }) => {
  if (!videoProject?.scenes) return null;

  const completedVideos = videoProject.scenes.filter(scene => scene.videoUrl);
  const totalScenes = videoProject.scenes.length;

  // Функция для получения CSS класса aspect ratio
  const getAspectRatioClass = (aspectRatio: string) => {
    switch (aspectRatio) {
      case '16:9':
        return 'aspect-video'; // 16:9
      case '9:16':
        return 'aspect-[9/16]'; // 9:16 (вертикальное)
      case '1:1':
        return 'aspect-square'; // 1:1 (квадратное)
      default:
        return 'aspect-video'; // По умолчанию 16:9
    }
  };

  const handleCombineVideos = async () => {
    if (completedVideos.length !== totalScenes) {
      toast.error('Дождитесь завершения генерации всех видео');
      return;
    }

    setMessages(prev => [...prev, {
      content: "Объединяю видео фрагменты в финальное видео...",
      role: "assistant",
      isUser: false,
      isLoading: true
    }]);

    try {
      // Реализуем объединение видео через API
      const videoUrls = completedVideos.map(video => video.videoUrl!);

      console.log('Объединяем видео:', videoUrls);

      // Импортируем конфигурацию API
      const { API_CONFIG } = await import('../config/api');

      // Вызываем API для объединения видео
      const response = await fetch(`${API_CONFIG.video.baseUrl}${API_CONFIG.video.endpoints.combine}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoUrls: videoUrls,
          aspectRatio: videoProject.aspectRatio,
          resolution: videoProject.resolution,
          userId: user?.id || 'anonymous'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      const combinedVideoUrl = result.videoUrl;

      if (!combinedVideoUrl) {
        throw new Error('Не получен URL объединенного видео');
      }

      // Обновляем проект с объединенным видео
      setVideoProject(prev => prev ? { ...prev, combinedVideoUrl } : null);

      // Показываем финальный результат
      setMessages(prev => [...prev, {
        content: "FINAL_VIDEO_COMPLETE",
        role: "assistant",
        isUser: false
      }]);

      // Сохраняем в историю и дашборд
      if (user && videoProject) {
        try {
          const saveResponse = await fetch(`${API_CONFIG.video.baseUrl}${API_CONFIG.video.endpoints.save}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.id,
              videoUrl: combinedVideoUrl,
              isPrivate: videoProject.isPrivate,
              prompt: videoProject.prompt,
              duration: videoProject.duration,
              resolution: videoProject.resolution,
              aspectRatio: videoProject.aspectRatio,
              fragmentDuration: videoProject.fragmentDuration,
              transitionType: videoProject.transitionType,
              scenes: videoProject.scenes?.map(scene => ({
                prompt: scene.prompt,
                imageUrl: scene.imageUrl,
                videoUrl: scene.videoUrl
              }))
            }),
          });

          if (saveResponse.ok) {
            console.log('Видео сохранено в историю и дашборд');
          } else {
            console.error('Ошибка сохранения в историю:', await saveResponse.text());
          }
        } catch (saveError) {
          console.error('Ошибка сохранения в историю:', saveError);
        }
      }

      toast.success('Видео успешно объединено и сохранено!');

    } catch (error) {
      console.error('Error combining videos:', error);
      toast.error('Ошибка при объединении видео: ' + (error instanceof Error ? error.message : 'Неизвестная ошибка'));

      // Убираем loading сообщение
      setMessages(prev => prev.filter(msg => !msg.isLoading));
    }
  };

  return (
    <div className="mb-6 flex justify-start">
      <div className="max-w-[80%] w-full">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Видео фрагменты готовы!</CardTitle>
            <p className="text-sm text-foreground/70">
              Создано {completedVideos.length} из {totalScenes} видео фрагментов. Вы можете объединить их в финальное видео.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {videoProject.scenes.map((scene, index) => (
                <div key={scene.id} className="space-y-2">
                  <div className={`${getAspectRatioClass(videoProject.aspectRatio)} bg-muted rounded-lg overflow-hidden border`}>
                    {scene.videoUrl ? (
                      <video
                        src={scene.videoUrl}
                        controls
                        className="w-full h-full object-cover"
                        poster={scene.imageUrl}
                      >
                        Ваш браузер не поддерживает видео.
                      </video>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                          <p className="text-xs text-foreground/60">Генерация...</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Фрагмент {index + 1}</p>
                    <p className="text-xs text-foreground/60 line-clamp-2">{scene.prompt}</p>
                    {scene.videoUrl && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs w-full"
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = scene.videoUrl!;
                          link.download = `fragment-${index + 1}.mp4`;
                          link.click();
                        }}
                      >
                        Скачать фрагмент
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t">
              <Button
                className="w-full"
                onClick={handleCombineVideos}
                disabled={completedVideos.length !== totalScenes}
              >
                {completedVideos.length === totalScenes
                  ? 'Сохранить результат'
                  : `Ожидание завершения генерации (${completedVideos.length}/${totalScenes})`
                }
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Компонент отображения финального видео
const FinalVideoResult: React.FC<CreateModeProps> = ({ videoProject }) => {
  if (!videoProject?.combinedVideoUrl) return null;

  // Функция для получения CSS класса aspect ratio
  const getAspectRatioClass = (aspectRatio: string) => {
    switch (aspectRatio) {
      case '16:9':
        return 'aspect-video'; // 16:9
      case '9:16':
        return 'aspect-[9/16]'; // 9:16 (вертикальное)
      case '1:1':
        return 'aspect-square'; // 1:1 (квадратное)
      default:
        return 'aspect-video'; // По умолчанию 16:9
    }
  };

  return (
    <div className="mb-6 flex justify-start">
      <div className="max-w-[80%] w-full">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Ваше видео готово!</CardTitle>
            <p className="text-sm text-foreground/70">
              Финальное видео создано и сохранено в вашу коллекцию.
            </p>
          </CardHeader>
          <CardContent>
            <div className={`${getAspectRatioClass(videoProject.aspectRatio)} bg-muted rounded-lg overflow-hidden border mb-4`}>
              <video
                src={videoProject.combinedVideoUrl}
                controls
                className="w-full h-full object-cover"
              >
                Ваш браузер не поддерживает видео.
              </video>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = videoProject.combinedVideoUrl!;
                  link.download = `final-video-${Date.now()}.mp4`;
                  link.click();
                }}
              >
                Скачать видео
              </Button>
              <Button
                className="flex-1"
                onClick={() => {
                  // Создать новый проект
                  window.location.reload();
                }}
              >
                Создать новое видео
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Компонент отображения раскадровки
const StoryboardDisplay: React.FC<CreateModeProps & {
  messages: MessageProps[];
  setMessages: React.Dispatch<React.SetStateAction<MessageProps[]>>;
  user: any;
}> = ({ videoProject, setVideoProject, setCreateModeStep, messages, setMessages, user }) => {
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingSceneIndex, setEditingSceneIndex] = useState<number | null>(null);

  // Функция для получения CSS класса aspect ratio
  const getAspectRatioClass = (aspectRatio: string) => {
    switch (aspectRatio) {
      case '16:9':
        return 'aspect-video'; // 16:9
      case '9:16':
        return 'aspect-[9/16]'; // 9:16 (вертикальное)
      case '1:1':
        return 'aspect-square'; // 1:1 (квадратное)
      default:
        return 'aspect-video'; // По умолчанию 16:9
    }
  };

  if (!videoProject?.scenes) return null;

  const handleRegenerateScene = async (sceneIndex: number) => {
    if (!videoProject.scenes) return;

    const scene = videoProject.scenes[sceneIndex];
    console.log(`Перегенерация сцены ${sceneIndex + 1}:`, scene.prompt);

    // Сбрасываем изображение для показа анимации загрузки
    setVideoProject(prev => {
      if (!prev?.scenes) return prev;
      const updatedScenes = [...prev.scenes];
      updatedScenes[sceneIndex] = {
        ...updatedScenes[sceneIndex],
        imageUrl: undefined
      };
      return { ...prev, scenes: updatedScenes };
    });

    try {
      // Импортируем функцию генерации
      const { generateImageWithReplicate } = await import('../utils/replicateImageGeneration');

      const replicateOptions = {
        prompt: scene.prompt,
        model: 'imagen4',
        userId: videoProject.prompt, // Используем как временный ID
        aspectRatio: videoProject.aspectRatio || "16:9",
        safetyFilterLevel: "block_medium_and_above",
        visibility: 'private' as 'public' | 'private'
      };

      const result = await generateImageWithReplicate(replicateOptions);

      if (result && result.images && result.images.length > 0) {
        // Обновляем сцену с новым URL изображения
        setVideoProject(prev => {
          if (!prev?.scenes) return prev;
          const updatedScenes = [...prev.scenes];
          updatedScenes[sceneIndex] = {
            ...updatedScenes[sceneIndex],
            imageUrl: result.images[0].url
          };
          return { ...prev, scenes: updatedScenes };
        });
      }
    } catch (error) {
      console.error(`Error regenerating scene ${sceneIndex + 1}:`, error);
    }
  };

  const handleEditPrompt = (sceneIndex: number) => {
    setEditingSceneIndex(sceneIndex);
    setEditModalOpen(true);
  };

  const handleSavePrompt = (newPrompt: string) => {
    if (editingSceneIndex === null) return;

    // Обновляем промт
    setVideoProject(prev => {
      if (!prev?.scenes) return prev;
      const updatedScenes = [...prev.scenes];
      updatedScenes[editingSceneIndex] = {
        ...updatedScenes[editingSceneIndex],
        prompt: newPrompt,
        imageUrl: undefined // Сбрасываем изображение для перегенерации
      };
      return { ...prev, scenes: updatedScenes };
    });

    // Автоматически перегенерируем с новым промтом
    setTimeout(() => handleRegenerateScene(editingSceneIndex), 100);
  };

  const handleStartVideoGeneration = async () => {
    if (!videoProject?.scenes || videoProject.scenes.length === 0) {
      toast.error('Нет сцен для генерации видео');
      return;
    }

    // Проверяем, что все изображения сгенерированы
    const missingImages = videoProject.scenes.filter(scene => !scene.imageUrl);
    if (missingImages.length > 0) {
      toast.error(`Дождитесь генерации всех изображений. Осталось: ${missingImages.length}`);
      return;
    }

    console.log('Начинаем генерацию видео...');
    setCreateModeStep('generation');

    try {
      // Импортируем функцию генерации видео
      const { generateVideoWithReplicate } = await import('../utils/replicateVideoGeneration');

      // Генерируем видео для каждой сцены
      const videoUrls: string[] = [];

      for (let i = 0; i < videoProject.scenes.length; i++) {
        const scene = videoProject.scenes[i];

        setMessages(prev => [...prev, {
          content: `Генерирую видео для сцены ${i + 1} из ${videoProject.scenes!.length}...`,
          role: "assistant",
          isUser: false,
          isLoading: true
        }]);

        try {
          // Определяем размеры на основе разрешения и соотношения сторон
          const getDimensions = (resolution: string, aspectRatio: string) => {
            const ratios = {
              '16:9': { width: 1024, height: 576 },
              '9:16': { width: 576, height: 1024 },
              '1:1': { width: 768, height: 768 }
            };

            const baseRatio = ratios[aspectRatio as keyof typeof ratios] || ratios['16:9'];

            // Масштабируем под разрешение
            const scales = {
              '360p': 0.5,
              '540p': 0.75,
              '720p': 1.0,
              '1080p': 1.5
            };

            const scale = scales[resolution as keyof typeof scales] || 1.0;

            return {
              width: Math.round(baseRatio.width * scale),
              height: Math.round(baseRatio.height * scale)
            };
          };

          const { width, height } = getDimensions(videoProject.resolution, videoProject.aspectRatio);

          console.log(`Размеры для сцены ${i + 1}:`, {
            resolution: videoProject.resolution,
            aspectRatio: videoProject.aspectRatio,
            calculatedWidth: width,
            calculatedHeight: height
          });

          // Логика для плавных переходов vs смены сцен
          let startImageUrl = scene.imageUrl;
          let endImageUrl = undefined;
          let mode = 'img2video';

          if (videoProject.transitionType === 'smooth' && i < videoProject.scenes.length - 1) {
            // Плавный переход: используем текущий кадр как start, следующий как end
            endImageUrl = videoProject.scenes[i + 1].imageUrl;
            mode = 'img2video'; // PixVerse поддерживает start+end изображения
          }
          // Для последней сцены или при смене сцен используем только стартовое изображение

          const videoOptions = {
            prompt: scene.prompt,
            model: 'pixverse/pixverse-v4.5',
            width,
            height,
            duration: videoProject.fragmentDuration,
            userId: user?.id || 'anonymous',
            startImageUrl: startImageUrl,
            endImageUrl: endImageUrl, // Для плавных переходов
            mode: mode,
            aspectRatio: videoProject.aspectRatio,
            quality: videoProject.resolution,
            motion_mode: 'default',
            visibility: videoProject.isPrivate ? 'private' as const : 'public' as const
          };

          console.log(`Генерация видео для сцены ${i + 1}:`, videoOptions);

          const videoUrl = await generateVideoWithReplicate(videoOptions);
          videoUrls.push(videoUrl);

          // Обновляем сцену с URL видео
          setVideoProject(prev => {
            if (!prev?.scenes) return prev;
            const updatedScenes = [...prev.scenes];
            updatedScenes[i] = {
              ...updatedScenes[i],
              videoUrl: videoUrl
            };
            return { ...prev, scenes: updatedScenes };
          });

        } catch (error) {
          console.error(`Error generating video for scene ${i + 1}:`, error);
          toast.error(`Ошибка генерации видео для сцены ${i + 1}: ${error}`);
        }
      }

      // Показываем результат отдельных видео
      setMessages(prev => [...prev, {
        content: "VIDEO_FRAGMENTS_COMPLETE", // Новый маркер для отдельных видео
        role: "assistant",
        isUser: false
      }]);

      toast.success(`Генерация завершена! Создано ${videoUrls.length} видео.`);

    } catch (error) {
      console.error('Error in video generation:', error);
      toast.error('Ошибка при генерации видео: ' + error);
    }
  };

  return (
    <>
      <div className="mb-6 flex justify-start">
        <div className="max-w-[80%] w-full">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Раскадровка готова</CardTitle>
              <p className="text-sm text-foreground/70">
                Ваша раскадровка создана. Вы можете отредактировать любой кадр перед генерацией видео.
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {videoProject.scenes.map((scene, index) => (
                  <div key={scene.id} className="space-y-2">
                    <div className={`${getAspectRatioClass(videoProject.aspectRatio)} bg-muted rounded-lg overflow-hidden border`}>
                      {scene.imageUrl ? (
                        <img
                          src={scene.imageUrl}
                          alt={`Сцена ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="text-center">
                            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                            <p className="text-xs text-foreground/60">Генерация...</p>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Сцена {index + 1}</p>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs"
                          onClick={() => handleRegenerateScene(index)}
                        >
                          Перегенерировать
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs"
                          onClick={() => handleEditPrompt(index)}
                        >
                          Изменить промт
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 pt-4 border-t">
                <Button
                  className="w-full"
                  onClick={handleStartVideoGeneration}
                >
                  Подтвердить и начать генерацию видео
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Модальное окно редактирования промта */}
      <PromptEditModal
        isOpen={editModalOpen}
        onClose={() => {
          setEditModalOpen(false);
          setEditingSceneIndex(null);
        }}
        onSave={handleSavePrompt}
        currentPrompt={editingSceneIndex !== null ? videoProject.scenes[editingSceneIndex]?.prompt || '' : ''}
        sceneNumber={editingSceneIndex !== null ? editingSceneIndex + 1 : 0}
      />
    </>
  );
};



// Тип для видео проекта
type VideoProjectType = {
  prompt: string;
  duration: number;
  fragmentDuration: 5 | 8;
  aspectRatio: '16:9' | '9:16' | '1:1';
  resolution: '360p' | '540p' | '720p' | '1080p';
  transitionType: 'smooth' | 'scene_change';
  isPrivate: boolean;
  scenario?: string;
  scenes?: Array<{
    id: string;
    description: string;
    prompt: string;
    imageUrl?: string;
    videoUrl?: string;
  }>;
  estimatedCost?: number;
  combinedVideoUrl?: string; // URL объединенного финального видео
};

// Интерфейс для пропсов компонентов Create Mode
interface CreateModeProps {
  videoProject: VideoProjectType;
  setVideoProject: React.Dispatch<React.SetStateAction<VideoProjectType | null>>;
  setCreateModeStep: React.Dispatch<React.SetStateAction<'input' | 'parameters' | 'confirm' | 'scenario' | 'storyboard' | 'generation' | 'assembly'>>;
  calculateProjectCost: (duration: number, fragmentDuration: 5 | 8, transitionType: 'smooth' | 'scene_change', resolution?: '360p' | '540p' | '720p' | '1080p') => number;
}

const Message = ({
  content,
  isUser,
  isLoading,
  images,
  createModeProps,
  messages,
  setMessages,
  user
}: MessageProps & {
  createModeProps?: CreateModeProps;
  messages?: MessageProps[];
  setMessages?: React.Dispatch<React.SetStateAction<MessageProps[]>>;
  user?: any;
}) => {
  // Обработка специальных маркеров для Create Mode
  if (content === "PARAMETERS_FORM" && createModeProps) {
    return <VideoParametersForm {...createModeProps} />;
  }

  if (content === "SCENARIO_DISPLAY" && createModeProps) {
    return <ScenarioDisplay
      {...createModeProps}
      onRegenerateScenario={() => {
        // Пересоздаем сценарий
        const event = new CustomEvent('regenerateScenario');
        window.dispatchEvent(event);
      }}
      onConfirmScenario={() => {
        // Подтверждаем сценарий
        const event = new CustomEvent('confirmScenario');
        window.dispatchEvent(event);
      }}
      estimatedCost={createModeProps.calculateProjectCost(
        createModeProps.videoProject.duration,
        createModeProps.videoProject.fragmentDuration,
        createModeProps.videoProject.transitionType,
        createModeProps.videoProject.resolution
      )}
    />;
  }

  if (content === "STORYBOARD_DISPLAY" && createModeProps && messages && setMessages) {
    return <StoryboardDisplay {...createModeProps} messages={messages} setMessages={setMessages} user={user} />;
  }

  if (content === "VIDEO_FRAGMENTS_COMPLETE" && createModeProps && messages && setMessages) {
    return <VideoFragmentsResult {...createModeProps} messages={messages} setMessages={setMessages} user={user} />;
  }

  if (content === "FINAL_VIDEO_COMPLETE" && createModeProps) {
    return <FinalVideoResult {...createModeProps} />;
  }

  return (
    <div className={cn(
      "mb-6",
      isUser ? "flex justify-end" : "flex justify-start"
    )}>
      <div className={cn(
        "max-w-[80%] rounded-2xl p-4",
        isUser
          ? "bg-primary text-primary-foreground !text-white"
          : "bg-background border border-border shadow-sm"
      )}>
        {isLoading ? (
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-foreground/50 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-foreground/50 rounded-full animate-pulse animation-delay-200"></div>
            <div className="w-2 h-2 bg-foreground/50 rounded-full animate-pulse animation-delay-400"></div>
          </div>
        ) : (
          <div>
            <div className={cn("prose dark:prose-invert", isUser ? "text-white" : "text-foreground")}>
              <ReactMarkdown
                components={{
                  code: CodeBlock,
                  h1: ({ node, ...props }) => <h1 className="text-xl font-bold" {...props} />,
                  h2: ({ node, ...props }) => <h2 className="text-lg font-bold" {...props} />,
                  h3: ({ node, ...props }) => <h3 className="text-base font-bold" {...props} />,
                  h4: ({ node, ...props }) => <h4 className="text-base font-semibold" {...props} />,
                  h5: ({ node, ...props }) => <h5 className="text-base font-medium" {...props} />,
                  h6: ({ node, ...props }) => <h6 className="text-base font-normal" {...props} />,
                  p: ({ node, ...props }) => <p className={isUser ? "!text-white" : ""} {...props} />, // Apply !text-white to p tags for user messages
                }}
              >
                {content}
              </ReactMarkdown>
              {images && images.length > 0 && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`Generated or uploaded image ${index + 1}`}
                      className="rounded-lg w-full object-cover"
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Define the ModelType type
type ModelType =
  | "geminiPro"
  | "geminiFlash"
  | "geminiImage"
  | "mistralM"    // mistral-medium-2505
  | "mistralL"    // mistral-large-2411
  | "pixtralL";   // pixtral-large-2411

const modelOptions = [
  { value: "mistralM", label: "Mistral Medium" },      // mistral-medium-2505
  { value: "mistralL", label: "Mistral Large" },       // mistral-large-2411
  { value: "pixtralL", label: "Pixtral Large" },       // pixtral-large-2411
  { value: "geminiPro", label: "Gemini 2.5 Pro" },
  { value: "geminiFlash", label: "Gemini 2.5 Flash" },
  { value: "geminiImage", label: "Gemini 2.0 Image" },
];


const TextGeneration = () => {
  const { t, i18n } = useTranslation(); // Initialize useTranslation
  const { user } = useAuth();
  const { isExpanded: sidebarExpanded } = useSidebarState();
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [messages, setMessages] = useState<MessageProps[]>([
    { content: t('textGen.initialGreeting'), role: "assistant", isUser: false }, // Translate initial message
  ]);

  // Create Mode states
  const [isCreateMode, setIsCreateMode] = useState(false);
  const [createModeStep, setCreateModeStep] = useState<'input' | 'parameters' | 'confirm' | 'scenario' | 'storyboard' | 'generation' | 'assembly'>('input');
  const [videoProject, setVideoProject] = useState<VideoProjectType | null>(null);
  const [isGeneratingScenario, setIsGeneratingScenario] = useState(false);
  const [isGeneratingStoryboard, setIsGeneratingStoryboard] = useState(false);
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [attachedImages, setAttachedImages] = useState<string[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [currentModel, setCurrentModel] = useState<ModelType>("mistralM");
  const [isChatsPanelOpen, setIsChatsPanelOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // --- throttle state ---
  const lastSendRef = useRef<number>(0);
  const THROTTLE_MS = 2500; // 2.5 сек между запросами

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load chats on component mount
  useEffect(() => {
    if (user) {
      loadChats();
    }
  }, [user]);

  // Обработчик события подтверждения параметров
  useEffect(() => {
    const handleConfirmParameters = () => {
      if (videoProject && createModeStep === 'parameters') {
        handleCreateModeStep('confirm');
      }
    };

    const handleRegenerateScenario = () => {
      if (videoProject && createModeStep === 'scenario') {
        const estimatedCost = calculateProjectCost(
          videoProject.duration,
          videoProject.fragmentDuration,
          videoProject.transitionType,
          videoProject.resolution
        );
        generateScenario(videoProject, estimatedCost);
      }
    };

    const handleConfirmScenario = () => {
      console.log('handleConfirmScenario called', { videoProject, createModeStep });
      if (videoProject && createModeStep === 'scenario') {
        console.log('Conditions met, starting storyboard generation');
        setMessages(prev => [...prev, {
          content: "Начинаю генерацию раскадровки...",
          role: "assistant",
          isUser: false
        }]);

        generateStoryboard(videoProject, videoProject.scenario || '');
      } else {
        console.log('Conditions not met for storyboard generation');
      }
    };

    window.addEventListener('confirmParameters', handleConfirmParameters);
    window.addEventListener('regenerateScenario', handleRegenerateScenario);
    window.addEventListener('confirmScenario', handleConfirmScenario);

    return () => {
      window.removeEventListener('confirmParameters', handleConfirmParameters);
      window.removeEventListener('regenerateScenario', handleRegenerateScenario);
      window.removeEventListener('confirmScenario', handleConfirmScenario);
    };
  }, [videoProject, createModeStep]);

  const loadChats = async () => {
    if (!user) return;
    try {
      const loadedChats = await loadChatsFromDatabase(user.id);
      const formattedChats = loadedChats.map(chat => ({
        ...chat,
        created_at: chat.created_at,
        updated_at: chat.updated_at
      }));
      setChats(formattedChats);
    } catch (error) {
      console.error('Error loading chats:', error);
    }
  };

  const createNewChat = () => {
    // Save current chat if it has messages (асинхронно, не блокируя UI)
    if (currentChatId && messages.length > 1 && user) {
      saveChatToDatabase(currentChatId, user.id, messages).catch(console.error);
    }
    
    // Create new chat instantly
    const newChatId = Date.now().toString();
    const newChat: Chat = {
      id: newChatId,
      title: t('textGen.newChat') || 'Новый чат',
      messages: [{ content: t('textGen.initialGreeting') || 'Привет! Я Uma AI. Чем могу помочь?', role: "assistant", isUser: false }],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    setChats(prev => [newChat, ...prev]);
    setCurrentChatId(newChatId);
    setMessages([{ content: t('textGen.initialGreeting') || 'Привет! Я Uma AI. Чем могу помочь?', role: "assistant", isUser: false }]);
    setAttachedImages([]);
    setIsChatsPanelOpen(false); // Закрываем панель на мобильных
  };

  const loadChat = async (chatId: string) => {
    // Save current chat before switching (асинхронно)
    if (currentChatId && messages.length > 1 && user) {
      saveChatToDatabase(currentChatId, user.id, messages).catch(console.error);
    }
    
    try {
      const chatMessages = await loadChatMessagesFromDatabase(chatId);
      setMessages(chatMessages.length > 0 ? chatMessages : [{ content: t('textGen.initialGreeting') || 'Привет! Я Uma AI. Чем могу помочь?', role: "assistant", isUser: false }]);
      setCurrentChatId(chatId);
      setAttachedImages([]);
      setIsChatsPanelOpen(false); // Закрываем панель на мобильных
    } catch (error) {
      console.error('Error loading chat:', error);
      toast.error(t('textGen.loadChatError') || 'Ошибка при загрузке чата');
    }
  };

  const updateChatTitle = (chatId: string, firstUserMessage: string) => {
    const title = firstUserMessage.slice(0, 20) + (firstUserMessage.length > 20 ? '...' : '');
    setChats(prev => prev.map(chat =>
      chat.id === chatId ? { ...chat, title, updated_at: new Date().toISOString() } : chat
    ));
  };

  // Функция для расчета стоимости проекта
  const calculateProjectCost = (duration: number, fragmentDuration: 5 | 8, transitionType: 'smooth' | 'scene_change', resolution: '360p' | '540p' | '720p' | '1080p' = '720p') => {
    const fragmentCount = Math.ceil(duration / fragmentDuration);

    // Стоимость генерации сценария (Mistral Medium 3)
    const scenarioCost = 5;

    // Стоимость генерации кадров раскадровки (Imagen 4) - 8 кредитов за кадр
    const storyboardCost = fragmentCount * 8;

    // Стоимость генерации видео (PixVerse v4.5) - зависит от разрешения, длительности и режима движения
    let videoCostPerFragment = 0;

    if (fragmentDuration === 5) {
      switch (resolution) {
        case '360p':
        case '540p':
          videoCostPerFragment = 90; // normal mode
          break;
        case '720p':
          videoCostPerFragment = 120; // normal mode
          break;
        case '1080p':
          videoCostPerFragment = 240; // normal mode
          break;
      }
    } else { // 8 секунд
      switch (resolution) {
        case '360p':
        case '540p':
          videoCostPerFragment = 180; // normal mode
          break;
        case '720p':
          videoCostPerFragment = 240; // normal mode
          break;
        case '1080p':
          videoCostPerFragment = 480; // normal mode (предполагаемая цена)
          break;
      }
    }

    const videoCost = fragmentCount * videoCostPerFragment;

    return scenarioCost + storyboardCost + videoCost;
  };

  // Функция генерации раскадровки через Imagen 4
  const generateStoryboard = async (project: NonNullable<typeof videoProject>, scenario: string) => {
    console.log('generateStoryboard called with:', { project, scenario });
    setIsGeneratingStoryboard(true);
    try {
      // Парсим сценарий для извлечения промтов
      const sceneMatches = scenario.match(/СЦЕНА \d+.*?\nПромт для генерации: (.*?)(?=\n\nСЦЕНА|\n\n$|$)/gs);

      if (!sceneMatches) {
        throw new Error('Не удалось распарсить сценарий');
      }

      const scenes: Array<{
        id: string;
        description: string;
        prompt: string;
        imageUrl?: string;
        videoUrl?: string;
      }> = sceneMatches.map((match, index) => {
        const promptMatch = match.match(/Промт для генерации: (.*?)$/s);
        const prompt = promptMatch ? promptMatch[1].trim() : `Сцена ${index + 1}`;

        return {
          id: `scene-${index + 1}`,
          description: `Сцена ${index + 1}`,
          prompt: prompt
        };
      });

      // Обновляем проект со сценами БЕЗ изображений
      setVideoProject(prev => prev ? { ...prev, scenes } : null);

      // СРАЗУ показываем раскадровку (пустую, с анимацией загрузки)
      setMessages(prev => [...prev, {
        content: "STORYBOARD_DISPLAY", // Специальный маркер для отображения раскадровки
        role: "assistant",
        isUser: false
      }]);

      setCreateModeStep('storyboard');

      // Генерируем изображения для каждой сцены АСИНХРОННО
      const generateSceneImage = async (sceneIndex: number) => {
        const scene = scenes[sceneIndex];

        try {
          // Генерируем изображение через Imagen 4
          const { generateImageWithReplicate } = await import('../utils/replicateImageGeneration');

          const replicateOptions = {
            prompt: scene.prompt,
            model: 'imagen4',
            userId: user?.id,
            aspectRatio: project.aspectRatio || "16:9",
            safetyFilterLevel: "block_medium_and_above",
            visibility: 'private' as 'public' | 'private'
          };

          const result = await generateImageWithReplicate(replicateOptions);

          if (result && result.images && result.images.length > 0) {
            // Обновляем конкретную сцену с URL изображения
            setVideoProject(prev => {
              if (!prev?.scenes) return prev;
              const updatedScenes = [...prev.scenes];
              updatedScenes[sceneIndex] = {
                ...updatedScenes[sceneIndex],
                imageUrl: result.images[0].url
              };
              return { ...prev, scenes: updatedScenes };
            });
          }
        } catch (error) {
          console.error(`Error generating image for scene ${sceneIndex + 1}:`, error);
        }
      };

      // Запускаем генерацию всех изображений параллельно
      const imagePromises = scenes.map((_, index) => generateSceneImage(index));
      await Promise.all(imagePromises);

    } catch (error) {
      console.error('Error generating storyboard:', error);
      setMessages(prev => [...prev, {
        content: "Ошибка при генерации раскадровки. Попробуйте еще раз.",
        role: "assistant",
        isUser: false
      }]);
    } finally {
      setIsGeneratingStoryboard(false);
    }
  };

  // Функция обработки этапов Create Mode
  const handleCreateModeStep = async (userInput: string) => {
    setIsTyping(true);

    try {
      switch (createModeStep) {
        case 'input':
          // Этап 1: Пользователь описал видео
          setMessages(prev => [...prev, {
            content: "Принято! Давайте выберем параметры будущего видео.",
            role: "assistant",
            isUser: false
          }]);

          // Переходим к этапу параметров
          setCreateModeStep('parameters');
          setVideoProject({
            prompt: userInput,
            duration: 10, // По умолчанию
            fragmentDuration: 5,
            aspectRatio: '16:9',
            resolution: '720p',
            transitionType: 'smooth',
            isPrivate: false
          });

          // Показываем форму параметров
          setTimeout(() => {
            setMessages(prev => [...prev, {
              content: "PARAMETERS_FORM", // Специальный маркер для отображения формы
              role: "assistant",
              isUser: false
            }]);
          }, 500);
          break;

        case 'parameters':
        case 'confirm':
          // Этап 2: Параметры подтверждены, генерируем сценарий
          if (videoProject) {
            const estimatedCost = calculateProjectCost(
              videoProject.duration,
              videoProject.fragmentDuration,
              videoProject.transitionType,
              videoProject.resolution
            );

            // Показываем анимацию генерации
            setMessages(prev => [...prev, {
              content: `Генерирую сценарий для видео длительностью ${videoProject.duration} секунд...`,
              role: "assistant",
              isUser: false,
              isLoading: true
            }]);

            // Генерируем сценарий через Mistral
            await generateScenario(videoProject, estimatedCost);
          }
          break;

        default:
          break;
      }
    } catch (error) {
      console.error('Error in Create Mode step:', error);
      setMessages(prev => [...prev, {
        content: "Произошла ошибка. Попробуйте еще раз.",
        role: "assistant",
        isUser: false
      }]);
    } finally {
      setIsTyping(false);
    }
  };

  // Функция генерации сценария через Mistral
  const generateScenario = async (project: NonNullable<typeof videoProject>, estimatedCost: number) => {
    setIsGeneratingScenario(true);
    try {
      const fragmentCount = Math.ceil(project.duration / project.fragmentDuration);

      const systemPrompt = `You are an AI expert in cinematic storyboard creation for AI video generation.

TASK: Create a detailed scene-by-scene storyboard for video based on user description. Write ALL prompts in ENGLISH optimized for Google Imagen-4.

PROJECT PARAMETERS:
- Duration: ${project.duration} seconds
- Fragment duration: ${project.fragmentDuration} seconds
- Scenes needed: ${fragmentCount}
- Aspect ratio: ${project.aspectRatio}
- Resolution: ${project.resolution}

IMAGEN-4 PROMPT STRUCTURE:
"[Shot type]: [Subject] [action]. [Setting]. [Lighting]. Camera: [angle]. [Style]. ${project.aspectRatio} aspect ratio, ${project.resolution} resolution."

SHOT TYPES: Wide shot, Close-up, Medium shot, Bird's eye view
LIGHTING: soft golden hour lighting, dramatic lighting, bright daylight
CAMERA ANGLES: low angle, high angle, eye level
STYLE: cinematic, photorealistic, professional photography

EXAMPLE:
"Wide shot: Panda entering house doorway. Cozy family living room with surprised family members. Soft warm indoor lighting. Camera: eye level. Pixar animation style, colorful, detailed. 16:9 aspect ratio, 720p resolution."

OUTPUT FORMAT (exactly):
СЦЕНА 1 (0-${project.fragmentDuration}с): [Russian scene description]
Промт для генерации: [English Imagen-4 prompt]

СЦЕНА 2 (${project.fragmentDuration}-${project.fragmentDuration * 2}с): [Russian scene description]
Промт для генерации: [English Imagen-4 prompt]

Continue for all ${fragmentCount} scenes.`;

      // Пробуем разные модели с fallback и таймаутом
      let response;
      const modelsToTry = ['geminiPro', 'mistralL', 'mistralM'] as const; // Начинаем с Gemini
      let lastError: Error | null = null;

      for (const modelToTry of modelsToTry) {
        try {
          console.log(`Trying model: ${modelToTry}`);

          // Создаем промис с таймаутом
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), 30000); // 30 секунд
          });

          const generatePromise = generateText({
            model: modelToTry,
            prompt: `${systemPrompt}\n\nОписание пользователя: "${project.prompt}"`,
            messages: []
          });

          response = await Promise.race([generatePromise, timeoutPromise]);
          console.log(`Successfully used model: ${modelToTry}`);
          break; // Успешно получили ответ
        } catch (error: any) {
          lastError = error;
          const errorMsg = error?.message || '';

          // Если это ошибка capacity/rate limit/timeout, пробуем следующую модель
          if (
            errorMsg.includes('Service tier capacity exceeded') ||
            errorMsg.includes('429') ||
            errorMsg.includes('504') ||
            errorMsg.includes('timeout') ||
            errorMsg.includes('rate limit') ||
            errorMsg.includes('превышена квота') ||
            errorMsg.includes('Request timeout')
          ) {
            console.log(`Model ${modelToTry} failed (${errorMsg}), trying next model...`);
            continue;
          } else {
            // Если это другая ошибка, прерываем попытки
            throw error;
          }
        }
      }

      if (!response) {
        throw lastError || new Error('Все модели недоступны или перегружены. Попробуйте позже.');
      }

      console.log('generateScenario response:', response);

      if (response?.choices?.[0]?.message?.content) {
        const scenario = response.choices[0].message.content;
        console.log('Generated scenario:', scenario);

        // Обновляем проект с сценарием
        setVideoProject(prev => prev ? { ...prev, scenario } : null);

        // Показываем сценарий пользователю с кнопкой пересоздания
        setMessages(prev => [...prev, {
          content: "SCENARIO_DISPLAY", // Специальный маркер для отображения сценария
          role: "assistant",
          isUser: false
        }]);

        // Переходим к этапу сценария (ждем подтверждения пользователя)
        setCreateModeStep('scenario');
        console.log('Set createModeStep to scenario');
      } else {
        console.log('No content in response:', response);
        throw new Error('Пустой ответ от модели');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setMessages(prev => [...prev, {
        content: "Ошибка при генерации сценария. Попробуйте еще раз.",
        role: "assistant",
        isUser: false
      }]);
    } finally {
      setIsGeneratingScenario(false);
    }
  };



  const handleSend = async () => {
    if (!inputValue.trim()) return;

    // --- throttle ---
    const now = Date.now();
    if (now - lastSendRef.current < THROTTLE_MS) {
      toast.error("Пожалуйста, подождите пару секунд перед следующей генерацией.");
      return;
    }
    lastSendRef.current = now;

    // Create new chat if none exists
    if (!currentChatId && user) {
      const newChatId = Date.now().toString();
      setCurrentChatId(newChatId);

      // Update chat title with first user message
      const title = inputValue.slice(0, 20) + (inputValue.length > 20 ? '...' : '');
      const newChat: Chat = {
        id: newChatId,
        title,
        messages: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setChats(prev => [newChat, ...prev]);
    }

    // Add user message
    const newMessages: MessageProps[] = [...messages, {
      content: inputValue,
      role: "user" as const,
      isUser: true,
      images: attachedImages.length > 0 ? attachedImages : undefined
    }];
    setMessages(newMessages);

    // Update chat title if this is the first user message
    if (currentChatId && messages.length === 1) {
      updateChatTitle(currentChatId, inputValue);
    }

    // Handle Create Mode logic
    if (isCreateMode) {
      await handleCreateModeStep(inputValue);
      setInputValue("");
      return;
    }

    setInputValue("");
    setIsTyping(true);

    try {
      // Clear attached images after sending
      setAttachedImages([]);

      // Add temporary loading message
      setMessages(prev => [...prev, { content: "", role: "assistant", isUser: false, isLoading: true }]);

      // Upload attached images to Supabase Storage if any
      let uploadedImageUrls: string[] = [];
      if (attachedImages.length > 0 && user) {
        try {
          const uploadPromises = attachedImages.map(async (dataUrl) => {
            const blob = dataUriToBlob(dataUrl);
            return uploadImageToSupabaseStorage(user.id, blob);
          });
          uploadedImageUrls = await Promise.all(uploadPromises);
          console.log('Uploaded images to Supabase:', uploadedImageUrls);
        } catch (uploadError) {
          console.error('Error uploading images to Supabase:', uploadError);
          toast.error(t('textGen.imageUploadErrorToast') || 'Ошибка при загрузке изображения');
          setIsTyping(false);
          return; // Stop execution if image upload fails
        }
      }

      // Filter out loading messages and the initial assistant message
      const filteredMessages = messages
        .filter(msg => !msg.isLoading)
        .filter((msg, index) => !(index === 0 && msg.role === 'assistant'));

      // Find the index of the last assistant message
      const lastAssistantIndex = filteredMessages.map(msg => msg.role).lastIndexOf('assistant');

      // Include messages up to the last assistant message, limiting total history length
      const chatHistory: Message[] = filteredMessages
        .slice(Math.max(0, filteredMessages.length - 10), lastAssistantIndex + 1) // Limit to last 10 messages
        .map(msg => ({
          role: msg.role,
          content: msg.content,
          images: undefined // Очищаем картинки из истории, чтобы не отправлять их повторно
        }));

      // Add the current user message to the history, including newly uploaded images
      chatHistory.push({
        role: "user",
        content: inputValue,
        images: uploadedImageUrls.length > 0 ? uploadedImageUrls : undefined // Use uploaded URLs
      });

      let response;
      try {
        response = await generateText({
          model: currentModel,
          prompt: inputValue, // The prompt is still needed for the initial message if chatHistory is empty
          messages: chatHistory,
          // images: attachedImages // No longer needed here, as images are now part of chatHistory
        });
      } catch (err: any) {
        // Явная обработка различных типов ошибок
        let msg = err?.message || "";

        if (
          msg.includes("504") ||
          msg.toLowerCase().includes("timeout") ||
          msg.toLowerCase().includes("function_invocation_timeout")
        ) {
          toast.error("Сервер перегружен или не отвечает. Попробуйте позже или уменьшите частоту запросов.");
        } else if (
          msg.includes("429") ||
          msg.toLowerCase().includes("rate limit") ||
          msg.toLowerCase().includes("превышена квота") ||
          msg.toLowerCase().includes("service tier capacity exceeded") ||
          msg.toLowerCase().includes("пропускная способность") ||
          msg.toLowerCase().includes("capacity exceeded")
        ) {
          if (msg.includes("Service tier capacity exceeded") || msg.includes("пропускная способность")) {
            toast.error("Модель Mistral перегружена. Система автоматически попробует другую модель.", {
              duration: 5000
            });
          } else {
            toast.error("API временно перегружен. Попробуйте позже или используйте другую модель (например, Gemini).", {
              duration: 6000
            });
          }
        } else {
          toast.error(t('textGen.generationErrorToast') || 'Не удалось сгенерировать ответ. Пожалуйста, попробуйте снова.');
        }
        setMessages(prev => {
          const newMessages = [...prev];
          newMessages.pop(); // Remove loading message
          newMessages.push({
            content: "Ошибка генерации: " + msg,
            role: "assistant",
            isUser: false,
            images: undefined
          });
          return newMessages;
        });
        setIsTyping(false);
        return;
      }

      // Extract the response text from the API response
      let responseText = t('textGen.defaultError') || 'Не удалось обработать запрос. Пожалуйста, попробуйте снова.';

      if (response && response.choices && response.choices[0] && response.choices[0].message && response.choices[0].message.content) {
        // Mistral API returns content directly in message.content for chat completions
        responseText = response.choices[0].message.content;

        // Update messages with the actual response (text only for now, as Mistral chat doesn't return images in this format)
        setMessages(prev => {
          const newMessages = [...prev];
          newMessages.pop(); // Remove loading message
          newMessages.push({
            content: responseText,
            role: "assistant",
            isUser: false,
            images: undefined // Mistral chat completion doesn't return images in this structure
          });
          return newMessages;
        });

        // Save chat to database if user is logged in
        if (user && currentChatId) {
          const updatedMessages: MessageProps[] = [...newMessages];
          updatedMessages.pop(); // Remove loading message
          updatedMessages.push({
            content: responseText,
            role: "assistant" as const,
            isUser: false,
            images: undefined
          });

          // Сохраняем асинхронно, не блокируя UI
          saveChatToDatabase(currentChatId, user.id, updatedMessages).catch(console.error);

          await saveTextGeneration({
            user_id: user.id,
            prompt: inputValue,
            response: responseText || '',
            model: currentModel,
            images: undefined // No images from this API
          });
        }

      } else {
        // Handle cases where the response structure is not as expected but no explicit error was thrown
        console.error('Unexpected API response structure:', response);
        // Check if there's an error message in the response itself
        if (response && response.error && response.error.message) {
          responseText = `Mistral API error: ${response.error.message}`;
        } else {
          responseText = t('textGen.defaultError') || 'Не удалось обработать запрос. Пожалуйста, попробуйте снова.';
        }

        setMessages(prev => {
          const newMessages = [...prev];
          newMessages.pop(); // Remove loading message
          newMessages.push({
            content: responseText,
            role: "assistant",
            isUser: false,
            images: undefined // Ensure no images on unexpected response
          });
          return newMessages;
        });
      }

    } catch (error) {
      console.error('Error generating text:', error);
      const errorMessage = error instanceof Error ? error.message : (t('textGen.generationErrorMessage') || 'Извините, произошла ошибка при обработке вашего запроса. Пожалуйста, попробуйте снова.');
      toast.error(t('textGen.generationErrorToast') || 'Не удалось сгенерировать ответ. Пожалуйста, попробуйте снова.');

      // Replace loading message with error message
      setMessages(prev => {
        const newMessages = [...prev];
        newMessages.pop(); // Remove loading message
        newMessages.push({
          content: errorMessage, // Use the actual error message or translated generic
          role: "assistant",
          isUser: false,
          images: undefined // Ensure no images on error
        });
        return newMessages;
      });
    } finally {
      setIsTyping(false);
    }
  };

  return (
    <AppLayout>
      <SEOHead
        title={i18n.language === 'ru'
          ? "Генерация текста с помощью ИИ - UMA.AI"
          : "AI Text Generation - UMA.AI"
        }
        description={i18n.language === 'ru'
          ? "Создавайте тексты с помощью искусственного интеллекта. Чат с ИИ, генерация контента, помощь в написании."
          : "Create texts using artificial intelligence. AI chat, content generation, writing assistance."
        }
        keywords={i18n.language === 'ru'
          ? "генерация текста, ИИ, искусственный интеллект, чат с ИИ, помощь в написании"
          : "text generation, AI, artificial intelligence, AI chat, writing assistance"
        }
        url="https://umaai.site/text"
      />
      <div className={cn(
        "flex flex-col h-screen pt-4 px-0 relative transition-all duration-300 ease-in-out",
        "sm:-ml-[170px]",
        sidebarExpanded ? "sm:ml-0" : ""
      )}>
        <DottedBackground type="dots" className="opacity-30" />

        <div className="flex items-center justify-between px-6 py-3 border-b border-border relative z-10 bg-background/80 backdrop-blur-sm">
          <h1 className="text-xl font-bold text-foreground">{t('textGen.pageTitle') || 'Генерация текста'}</h1>
          {!isCreateMode && (
            <ModelSelector
              models={modelOptions}
              currentModel={currentModel}
              onSelectModel={(value) => setCurrentModel(value as ModelType)}
            />
          )}
        </div>
        
        <div className="flex flex-1 overflow-hidden">
          <div className="flex-1 overflow-y-auto px-6 py-6 relative z-10">
            {messages.map((message, index) => (
              <Message
                key={index}
                role={message.role} // Pass the role property
                content={message.content}
                isUser={message.isUser}
                isLoading={message.isLoading}
                images={message.images}
                createModeProps={videoProject ? {
                  videoProject,
                  setVideoProject,
                  setCreateModeStep,
                  calculateProjectCost
                } : undefined}
                messages={messages}
                setMessages={setMessages}
                user={user}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>
          
          {/* Кнопка для мобильных устройств */}
          <Button
            variant="ghost"
            size="icon"
            className="fixed top-20 right-4 z-20 lg:hidden bg-background/80 backdrop-blur-sm border border-border shadow-lg"
            onClick={() => setIsChatsPanelOpen(!isChatsPanelOpen)}
          >
            {isChatsPanelOpen ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </Button>

          {/* Панель чатов */}
          <div className={cn(
            "w-64 border-l border-border p-4 bg-background/80 backdrop-blur-sm overflow-y-auto transition-transform duration-300 ease-in-out",
            "lg:block lg:static lg:translate-x-0",
            "fixed top-0 right-0 h-full z-10",
            isChatsPanelOpen ? "translate-x-0" : "translate-x-full lg:translate-x-0"
          )}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-foreground">
                {t('chats') || (t('textGen.chats') || 'Чаты')}
              </h3>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-foreground/70 hover:text-foreground"
                onClick={createNewChat}
              >
                <Plus size={14} />
              </Button>
            </div>
            <div className="space-y-2">
              {chats.map((chat) => (
                <div
                  key={chat.id}
                  className={cn(
                    "p-2 rounded-lg cursor-pointer text-sm transition-colors",
                    currentChatId === chat.id
                      ? "bg-primary/10 text-primary border border-primary/20"
                      : "hover:bg-foreground/5 text-foreground/70"
                  )}
                  onClick={() => loadChat(chat.id)}
                >
                  <div className="truncate">{chat.title}</div>
                  <div className="text-xs text-foreground/50 mt-1">
                    {new Date(chat.updated_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Overlay для мобильных */}
          {isChatsPanelOpen && (
            <div
              className="fixed inset-0 bg-black/20 z-5 lg:hidden"
              onClick={() => setIsChatsPanelOpen(false)}
            />
          )}
        </div>
        
        <div className="px-6 py-4 border-t border-border relative z-10 bg-background/80 backdrop-blur-sm">
          <div className="flex items-start gap-2 max-w-3xl mx-auto">
            <div className="flex-1 relative max-w-3xl mx-auto">
              <div className="relative flex">
                {attachedImages.length > 0 && (
                  <div className="absolute -top-6 left-0 right-0 flex gap-2 overflow-x-auto pb-2 px-2">
                    {attachedImages.map((image, index) => (
                      <div key={index} className="relative flex-shrink-0">
                        <img
                          src={image}
                          alt={`Attached ${index + 1}`}
                          className="w-14 h-14 rounded-lg object-cover border border-border shadow-sm"
                        />
                        <button
                          onClick={() => setAttachedImages(prev => prev.filter((_, i) => i !== index))}
                          className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-background border border-border flex items-center justify-center hover:bg-foreground/5"
                        >
                          <X size={10} className="text-foreground/70" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                
                <Textarea
                  value={inputValue}
                  onChange={e => setInputValue(e.target.value)}
                  placeholder={t('textGen.inputPlaceholder') || 'Введите ваше сообщение...'}
                  className={cn(
                    "w-full bg-background border-border rounded-2xl resize-none focus:border-primary text-foreground pr-4 pl-4",
                    attachedImages.length > 0 ? "min-h-[120px] pt-14 pb-2" : "h-[60px] py-[18px]"
                  )}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />

                {/* Невидимый фон для кнопки на всю ширину - для переноса текста */}
                <div
                  className="absolute bottom-0 left-0 right-0 h-12 z-5"
                  style={{ backgroundColor: 'var(--background)' }}
                />

                {/* Create Mode Toggle */}
                <div className="absolute bottom-3 left-4 z-10">
                  <div className="relative">
                    {/* Адаптивное свечение Ambilight */}
                    <div
                      className={cn(
                        "absolute inset-0 rounded-lg blur-sm transition-all duration-500 ease-in-out",
                        isCreateMode ? "opacity-0 scale-100" : "opacity-60 scale-110"
                      )}
                      style={{
                        background: `
                          radial-gradient(circle at 0% 0%, rgba(120, 119, 198, 0.6) 0%, transparent 30%),
                          radial-gradient(circle at 100% 0%, rgba(255, 119, 198, 0.6) 0%, transparent 30%),
                          radial-gradient(circle at 100% 100%, rgba(255, 184, 119, 0.6) 0%, transparent 30%),
                          radial-gradient(circle at 0% 100%, rgba(120, 255, 119, 0.6) 0%, transparent 30%),
                          radial-gradient(circle at 50% 0%, rgba(119, 198, 255, 0.6) 0%, transparent 30%),
                          radial-gradient(circle at 50% 100%, rgba(255, 119, 255, 0.6) 0%, transparent 30%)
                        `,
                        zIndex: -1
                      }}
                    />

                    <button
                    onClick={() => {
                      setIsCreateMode(!isCreateMode);
                      if (!isCreateMode) {
                        setCreateModeStep('input');
                        setMessages([
                          { content: "Добро пожаловать в режим создания видео! Опишите, какое видео вы хотите создать.", role: "assistant", isUser: false }
                        ]);
                      } else {
                        setMessages([
                          { content: t('textGen.initialGreeting'), role: "assistant", isUser: false }
                        ]);
                        setVideoProject(null);
                        setCreateModeStep('input');
                      }
                    }}
                    className="px-2 py-1 rounded-lg text-xs font-medium transition-all duration-500 ease-in-out text-white whitespace-nowrap border"
                    style={{
                      background: `
                        radial-gradient(circle at 20% 50%, ${isCreateMode ? 'rgba(120, 119, 198, 0.1)' : 'rgba(120, 119, 198, 0.2)'} 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, ${isCreateMode ? 'rgba(255, 119, 198, 0.1)' : 'rgba(255, 119, 198, 0.2)'} 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, ${isCreateMode ? 'rgba(255, 184, 119, 0.1)' : 'rgba(255, 184, 119, 0.2)'} 0%, transparent 50%),
                        radial-gradient(circle at 0% 50%, ${isCreateMode ? 'rgba(120, 255, 119, 0.1)' : 'rgba(120, 255, 119, 0.2)'} 0%, transparent 50%),
                        radial-gradient(circle at 80% 50%, ${isCreateMode ? 'rgba(119, 198, 255, 0.1)' : 'rgba(119, 198, 255, 0.2)'} 0%, transparent 50%),
                        ${isCreateMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)'}
                      `,
                      borderColor: `transparent`,
                      borderImage: `
                        radial-gradient(circle at 20% 50%, ${isCreateMode ? 'rgba(120, 119, 198, 0.2)' : 'rgba(120, 119, 198, 0.4)'} 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, ${isCreateMode ? 'rgba(255, 119, 198, 0.2)' : 'rgba(255, 119, 198, 0.4)'} 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, ${isCreateMode ? 'rgba(255, 184, 119, 0.2)' : 'rgba(255, 184, 119, 0.4)'} 0%, transparent 50%),
                        radial-gradient(circle at 0% 50%, ${isCreateMode ? 'rgba(120, 255, 119, 0.2)' : 'rgba(120, 255, 119, 0.4)'} 0%, transparent 50%),
                        radial-gradient(circle at 80% 50%, ${isCreateMode ? 'rgba(119, 198, 255, 0.2)' : 'rgba(119, 198, 255, 0.4)'} 0%, transparent 50%)
                      `,
                      borderImageSlice: 1
                    }}
                  >
                    Create Mode
                  </button>
                  </div>
                </div>

                <div className="absolute right-3 top-3 flex items-center gap-2">
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    className="hidden"
                    id="image-upload"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      files.forEach(file => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                          setAttachedImages(prev => [...prev, e.target?.result as string]);
                        };
                        reader.readAsDataURL(file);
                      });
                      e.target.value = '';
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-foreground/70 hover:text-foreground"
                    onClick={() => document.getElementById('image-upload')?.click()}
                  >
                    <ImageIcon size={18} />
                  </Button>
                </div>
              </div>
            </div>
            
            <Button 
              size="icon"
              className="flex h-[60px] w-[60px] rounded-2xl bg-primary hover:bg-primary/90 text-primary-foreground items-center justify-center"
              onClick={handleSend}
              disabled={isTyping || !inputValue.trim()}
            >
              {isTyping ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <SendHorizonal size={18} />
              )}
            </Button>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default TextGeneration;
