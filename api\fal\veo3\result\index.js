// Fal API result endpoint for Veo3 model (как в Kling 2.1)
// GET /api/fal/veo3/result?requestId=<request_id>&model=<model_id>&userId=<user_id>&prompt=<prompt>&visibility=<visibility>

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "../../../utils/database.js";

// Функция для создания GIF из видео (асинхронно)
async function createGifAsync(videoUrl, generationId, userId) {
  try {
    console.log(`[VEO3 RESULT] Starting GIF creation for generation ${generationId}`);
    
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site';
    
    const response = await fetch(`${baseUrl}/api/create-gif`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoUrl: videoUrl,
        generationId: generationId,
        userId: userId
      })
    });

    if (response.ok) {
      console.log(`[VEO3 RESULT] GIF creation started for generation ${generationId}`);
    } else {
      console.error(`[VEO3 RESULT] GIF creation failed for generation ${generationId}:`, await response.text());
    }
  } catch (error) {
    console.error(`[VEO3 RESULT] GIF creation error for generation ${generationId}:`, error);
  }
}

export default async function handler(req, res) {
  // Разрешаем только GET запросы
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility, cost, duration } = req.query;

    console.log('[VEO3 RESULT] Getting result for request:', requestId);

    // Проверяем обязательные параметры
    if (!requestId) {
      return res.status(400).json({ error: 'Request ID is required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[VEO3 RESULT] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    console.log('[VEO3 RESULT] Getting result for request:', requestId);

    // Сначала получаем статус, чтобы получить response_url (как в polling)
    const statusUrl = `https://queue.fal.run/fal-ai/veo3/requests/${requestId}/status`;
    console.log('[VEO3 RESULT] Getting status from:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[VEO3 RESULT] Status API error:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Fal API status error',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[VEO3 RESULT] Status response:', statusData);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({
        error: 'Generation not completed',
        status: statusData.status
      });
    }

    // Получаем результат через response_url (как в polling)
    console.log('[VEO3 RESULT] Getting result from response_url:', statusData.response_url);

    const resultResponse = await fetch(statusData.response_url, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!resultResponse.ok) {
      const errorText = await resultResponse.text();
      console.error('[VEO3 RESULT] Result API error:', errorText);
      return res.status(resultResponse.status).json({
        error: 'Fal API result error',
        details: errorText
      });
    }

    const result = await resultResponse.json();

    console.log('[VEO3 RESULT] Final result:', result);

    // Проверяем наличие видео в результате
    if (!result.video || !result.video.url) {
      console.error('[VEO3 RESULT] No video URL in result');
      return res.status(500).json({ error: 'No video URL in result' });
    }

    const videoUrl = result.video.url;
    console.log('[VEO3 RESULT] Video URL:', videoUrl);

    // Сохраняем результат в базу данных, если указан userId
    if (userId && userId !== 'anonymous') {
      try {
        await saveVeo3Result(videoUrl, requestId, userId, prompt, visibility, cost);
        console.log('[VEO3 RESULT] Result saved to database');
      } catch (saveError) {
        console.error('[VEO3 RESULT] Error saving to database:', saveError);
        // Не прерываем выполнение, возвращаем результат клиенту
      }
    }

    // Возвращаем результат клиенту
    return res.status(200).json({
      video_url: videoUrl,
      status: 'completed',
      request_id: requestId
    });

  } catch (error) {
    console.error('[VEO3 RESULT] Unexpected error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}

// Функция для сохранения результата Veo3 в базу данных
async function saveVeo3Result(falVideoUrl, falRequestId, userId, prompt, visibility, cost) {
  try {
    console.log(`[VEO3 RESULT SAVE] Processing video: ${falVideoUrl}`);

    // Скачиваем видео
    const videoBuffer = await downloadFile(falVideoUrl);
    console.log(`[VEO3 RESULT SAVE] Downloaded video, size: ${videoBuffer.length} bytes`);

    // Загружаем видео в Supabase (правильная сигнатура: buffer, fileName)
    const fileName = `veo3_${falRequestId}_${Date.now()}.mp4`;
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[VEO3 RESULT SAVE] Uploaded to Supabase: ${supabaseVideoUrl}`);

    // Генерируем миниатюру (пропускаем если FFmpeg недоступен)
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      const thumbnailFileName = `veo3_thumb_${falRequestId}_${Date.now()}.jpg`;
      thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
      console.log(`[VEO3 RESULT SAVE] Generated thumbnail: ${thumbnailUrl}`);
    } catch (thumbError) {
      console.error(`[VEO3 RESULT SAVE] Thumbnail generation failed (FFmpeg not available):`, thumbError.message);
      // Продолжаем без thumbnail - это не критично
    }

    // Определяем стоимость и длительность
    const finalCost = cost ? parseInt(cost) : 1280;
    const finalDuration = 8; // Veo3 всегда 8 секунд

    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: 'fal-ai/veo3',
      duration: finalDuration,
      cost: finalCost,
      visibility: visibility || 'public'
    });

    console.log(`[VEO3 RESULT SAVE] Saved with ID: ${savedGeneration.id}`);

    // Start GIF creation
    createGifAsync(supabaseVideoUrl, savedGeneration.id, userId).catch(error => {
      console.error(`[VEO3 RESULT SAVE] GIF error:`, error);
    });

    return savedGeneration;

  } catch (error) {
    console.error(`[VEO3 RESULT SAVE] Database error:`, error);
    throw error;
  }
}
