// Импортируем необходимые модули
import axios from 'axios';

// Константы с данными для аутентификации в ЮKassa
const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
const API_URL = 'https://api.yookassa.ru/v3';

export default async function handler(req, res) {
  // Явно устанавливаем заголовок Content-Type для JSON
  res.setHeader('Content-Type', 'application/json');
  
  // Разрешаем только GET-запросы для получения информации о платеже
  if (req.method === 'GET') {
    try {
      const { id } = req.query;
      
      if (!id) {
        return res.status(400).json({ error: 'Payment ID is required' });
      }
      
      console.log('Получен запрос на проверку статуса платежа:', id);
      
      // HTTP Basic Auth
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      // Отправляем запрос к API ЮKassa
      const response = await axios.get(`${API_URL}/payments/${id}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Получен ответ от ЮKassa:', response.data);
      return res.status(200).json(response.data);
    } catch (error) {
      console.error('Ошибка при получении информации о платеже:', error.response?.data || error.message);
      return res.status(error.response?.status || 500).json({
        error: 'Ошибка при получении информации о платеже',
        details: error.response?.data || error.message
      });
    }
  }
  // Если метод запроса не поддерживается
  else {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      error: 'Method Not Allowed',
      message: `Method ${req.method} is not allowed`
    });
  }
} 