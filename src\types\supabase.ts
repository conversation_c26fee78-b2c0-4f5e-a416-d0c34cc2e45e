export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          avatar_url: string | null
          name: string | null
          created_at: string
          updated_at: string
          credits: number
        }
        Insert: {
          id: string
          email: string
          avatar_url?: string | null
          name?: string | null
          created_at?: string
          updated_at?: string
          credits?: number
        }
        Update: {
          id?: string
          email?: string
          avatar_url?: string | null
          name?: string | null
          created_at?: string
          updated_at?: string
          credits?: number
        }
      }
      image_generations: {
        Row: {
          id: string
          user_id: string
          prompt: string
          model: string
          image_urls: string[]
          style: string | null
          aspect_ratio: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          prompt: string
          model: string
          image_urls: string[]
          style?: string | null
          aspect_ratio?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          prompt?: string
          model?: string
          image_urls?: string[]
          style?: string | null
          aspect_ratio?: string | null
          created_at?: string
        }
      }
      text_generations: {
        Row: {
          id: string
          user_id: string
          prompt: string
          response: string
          model: string | null
          images: string[] | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          prompt: string
          response: string
          model?: string | null
          images?: string[] | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          prompt?: string
          response?: string
          model?: string | null
          images?: string[] | null
          created_at?: string
        }
      }
      chat_sessions: {
        Row: {
          id: string
          user_id: string
          title: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          created_at?: string
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          session_id: string
          role: string
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          role: string
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          role?: string
          content?: string
          created_at?: string
        }
      }
      user_credits: {
        Row: {
          id: string
          user_id: string
          amount: number
          transaction_type: string
          description: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          amount: number
          transaction_type: string
          description: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          amount?: number
          transaction_type?: string
          description?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
} 