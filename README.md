# UMA.AI - Платформа искусственного интеллекта

UMA.AI - это мощная платформа для генерации контента с использованием передовых технологий искусственного интеллекта. Создавайте тексты, изображения и видео с помощью ИИ.

## Функциональность

- Аутентификация пользователей (email/пароль и Google)
- Генерация изображений с различными стилями и пропорциями
- Генерация текста на основе запросов пользователя
- Генерация видео (в разработке)
- Мультиязычный интерфейс (английский и русский)
- Темная и светлая темы

## Технологии

- React
- TypeScript
- Vite
- Tailwind CSS
- Shadcn UI
- Supabase (аутентификация и база данных)
- i18next (многоязычность)

## Установка и запуск

1. Клонируйте репозиторий:

```bash
git clone https://github.com/your-username/enigma-genesis.git
cd enigma-genesis
```

2. Установите зависимости:

```bash
npm install
```

3. Запустите локальный сервер разработки:

```bash
npm run dev
```

4. Откройте http://localhost:8080 в вашем браузере.

## Загрузка на GitHub

1. Создайте новый репозиторий на GitHub (если еще не создали).

2. Инициализируйте Git в вашем проекте (если еще не сделали):

```bash
git init
```

3. Добавьте все файлы в индекс:

```bash
git add .
```

4. Создайте первый коммит:

```bash
git commit -m "Initial commit"
```

5. Добавьте удаленный репозиторий:

```bash
git remote add origin https://github.com/your-username/enigma-genesis.git
```

6. Отправьте изменения в репозиторий:

```bash
git push -u origin main
```

## Деплой на GitHub Pages

1. Пакет gh-pages уже установлен в проекте.

2. Скрипты для деплоя уже добавлены в package.json.

3. Запустите команду для деплоя:

```bash
npm run deploy
```

4. Ваш сайт будет доступен по адресу: `https://your-username.github.io/enigma-genesis`

## Настройка Supabase и Google Auth

1. Создайте проект в [Supabase](https://supabase.com/).

2. Создайте проект в [Google Cloud Console](https://console.cloud.google.com/).

3. Настройте OAuth для Google в Google Cloud Console:
   - Создайте учетные данные OAuth
   - Настройте экран подтверждения
   - Добавьте URI перенаправления: `https://your-supabase-project.supabase.co/auth/v1/callback`

4. Добавьте ID клиента и секрет Google в настройки аутентификации Supabase.

5. Настройте URL-адрес сайта в Supabase.

## Структура проекта

- `/src` - исходный код приложения
  - `/components` - React компоненты
  - `/context` - контекст для состояния приложения
  - `/utils` - утилиты и функции для работы с API
  - `/pages` - страницы приложения
  - `/locales` - файлы с переводами
- `/public` - статические файлы

## Лицензия

MIT

## Решение проблемы с отсутствующей колонкой updated_at

Если при работе с приложением возникают ошибки, связанные с колонкой `updated_at` в таблице `profiles`, выполните следующие действия:

1. Войдите в панель управления Supabase
2. Перейдите в раздел SQL Editor
3. Выполните следующие SQL-запросы:

```sql
-- Проверьте существование колонки
SELECT EXISTS (
  SELECT 1 
  FROM information_schema.columns 
  WHERE table_name = 'profiles' 
    AND column_name = 'updated_at'
) as column_exists;

-- Если column_exists = false, добавьте колонку
ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Обновите существующие записи
UPDATE profiles
SET updated_at = created_at
WHERE updated_at IS NULL;

-- Создайте триггер для автоматического обновления
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Добавьте триггер, если он еще не существует
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_trigger 
    WHERE tgname = 'set_updated_at' 
    AND tgrelid = 'profiles'::regclass
  ) THEN
    CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;
```

После выполнения этих запросов перезапустите приложение. Колонка `updated_at` теперь должна корректно работать.
