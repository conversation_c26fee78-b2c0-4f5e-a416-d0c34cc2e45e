# Исправление ошибки с отсутствующим столбцом 'cost'

## Проблема
В логах видна ошибка: `Could not find the 'cost' column of 'image_generations' in the schema cache`

## Решение
Необходимо выполнить SQL скрипт для добавления отсутствующего столбца `cost` в таблицу `image_generations`.

## Инструкция по выполнению

1. Откройте панель управления Supabase
2. Перейдите в раздел "SQL Editor"
3. Выполните содержимое файла `supabase-add-cost-column.sql`:

```sql
-- Добавляет столбец cost в таблицу image_generations
-- Этот столбец необходим для сохранения стоимости генерации изображений

DO $$ 
BEGIN
  -- Проверяем существует ли столбец cost в таблице image_generations
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'image_generations' 
      AND column_name = 'cost'
  ) THEN
    -- Если столбец не существует, добавляем его
    EXECUTE 'ALTER TABLE image_generations ADD COLUMN cost INTEGER DEFAULT 0';
    RAISE NOTICE 'Столбец cost добавлен в таблицу image_generations';
  ELSE
    RAISE NOTICE 'Столбец cost уже существует в таблице image_generations';
  END IF;
END $$;

-- Обновляем все существующие записи, у которых cost равен NULL
UPDATE image_generations
SET cost = 0
WHERE cost IS NULL;

-- Добавляем комментарий к столбцу
COMMENT ON COLUMN image_generations.cost IS 'Стоимость генерации изображения в кредитах';
```

4. Нажмите "Run" для выполнения скрипта

## Результат
После выполнения скрипта:
- Столбец `cost` будет добавлен в таблицу `image_generations`
- Все существующие записи получат значение `cost = 0`
- Новые генерации будут корректно сохраняться с указанием стоимости
- Ошибка в webhook'е исчезнет