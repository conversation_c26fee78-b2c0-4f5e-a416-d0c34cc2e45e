-- Скрипт для проверки текущего состояния пользователей и конфигурации аутентификации

-- 1. Проверяем текущую конфигурацию авторизации
SELECT (coalesce(current_setting('auth.external_email_confirmation_enabled', true), 'off')) as email_confirmation_required;

-- 2. Получаем информацию о текущих пользователях
SELECT 
  id, 
  email, 
  email_confirmed_at IS NOT NULL as is_email_confirmed,
  created_at,
  last_sign_in_at,
  raw_app_meta_data->'provider' as provider
FROM auth.users
ORDER BY created_at DESC
LIMIT 20;

-- 3. Проверяем таблицу идентификаций
SELECT 
  user_id, 
  provider,
  identity_data
FROM auth.identities
ORDER BY user_id DESC
LIMIT 20;

-- 4. Проверяем профили пользователей
SELECT 
  id,
  email,
  name,
  credits,
  created_at,
  updated_at
FROM profiles
ORDER BY created_at DESC
LIMIT 20;

-- 5. Проверяем политики безопасности для таблицы profiles
SELECT 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM pg_policies
WHERE schemaname = 'public' AND tablename = 'profiles'
ORDER BY tablename, policyname; 