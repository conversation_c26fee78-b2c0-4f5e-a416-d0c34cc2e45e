import { toast } from 'sonner';

export interface VideoGenerationOptions {
  prompt: string;
  startImageUrl?: string;
  endImageUrl?: string;
  duration?: number;
  aspectRatio?: string;
  loop?: boolean;
  userId?: string;
}


interface ReplicateOptions {
  prompt: string;
  model?: string;
  width?: number;
  height?: number;
  steps?: number;
  duration?: number;
  userId: string;
  startImageUrl?: string;
  endImageUrl?: string;
  mode?: string;
  aspectRatio?: string;
  loop?: boolean;
  concepts?: string[];
  // Параметры для pixverse
  quality?: string;
  motion_mode?: string;
  style?: string;
  effect?: string;
  seed?: number;
  negative_prompt?: string;
  last_frame_image?: string;
  // Параметр для приватности
  visibility?: 'public' | 'private';
}

// Используем наш бэкенд вместо прямого доступа к API
const API_URL = (import.meta.env.VITE_API_URL?.split(',')[0] || 'http://localhost:3000').trim();
console.log('DEBUG VITE_API_URL:', import.meta.env.VITE_API_URL);

/**
 * Generates video using the Replicate API with the luma/ray-flash-2-540p model
 */
import { uploadVideoToSupabaseStorage } from './supabase';

export const generateVideoWithReplicate = async (options: ReplicateOptions): Promise<string> => {
  console.log('Starting video generation with Replicate:', options);

  try {
    // Настройки запроса
    const {
      prompt,
      model = 'luma/ray-flash-2-540p',
      width = 1024,
      height = 576,
      duration = 4,
      userId,
      startImageUrl,
      endImageUrl,
      mode = 'text2video'
    } = options;

    console.log(`Sending request to ${API_URL}/api/replicate`);

    // Отправляем запрос на генерацию видео через бэкенд
    const response = await fetch(`${API_URL}/api/replicate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: model,
        input: {
          prompt,
          width,
          height,
          duration,
          start_image: startImageUrl,
          end_image: endImageUrl,
          aspect_ratio: options.aspectRatio,
          loop: options.loop,
          model: model, // Добавляем model в input для совместимости
          // Передаем userId и метаданные внутри input для корректной обработки вебхуком
          ...(userId && { userId: userId }),
          // Передаем информацию о приватности через метаданные (не влияет на модель)
          ...(options.visibility && { metadata_visibility: options.visibility }),
          // Передаем все дополнительные параметры из options
          ...(options.quality && { quality: options.quality }),
          ...(options.motion_mode && { motion_mode: options.motion_mode }),
          ...(options.style && { style: options.style }),
          ...(options.effect && { effect: options.effect }),
          ...(options.seed && { seed: options.seed }),
          ...(options.negative_prompt && { negative_prompt: options.negative_prompt }),
          ...(options.last_frame_image && { last_frame_image: options.last_frame_image })
          // Параметр visibility НЕ передается в API модели, он только для нашей БД
        },
        mode,
        // userId больше не передается здесь, он теперь внутри input
        // userId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    console.log('DEBUG: Ответ сервера при генерации видео:', data);

    // Получаем ссылку на видео (Replicate или polling)
    let replicateVideoUrl: string | undefined;
    if (data.success && data.videoUrl) {
      replicateVideoUrl = data.videoUrl;
    } else if (data.id && (data.status === "starting" || data.status === "processing")) {
      const result = await pollVideoCompletion(data.id);
      if (result && result.output) {
        replicateVideoUrl = Array.isArray(result.output) ? result.output[0] : result.output;
      } else {
        throw new Error('Video generation finished but no output received');
      }
    } else {
      throw new Error('Failed to generate video');
    }

    // Если нет userId — возвращаем временную ссылку (fallback)
    if (!userId) {
      return replicateVideoUrl!;
    }

    // Скачиваем видеофайл по временной ссылке
    const videoResponse = await fetch(replicateVideoUrl!);
    if (!videoResponse.ok) {
      throw new Error('Failed to download video from Replicate');
    }
    const videoBlob = await videoResponse.blob();

    // Проверка типа и размера видео
    if (videoBlob.type !== 'video/mp4' || videoBlob.size === 0) {
      console.error(
        `Ошибка: получен невалидный видеофайл. Тип: ${videoBlob.type}, Размер: ${videoBlob.size}. Ссылка: ${replicateVideoUrl}`
      );
      if (typeof toast === "function") {
        toast.error?.("Ошибка: получен невалидный видеофайл от Replicate. Проверьте ссылку и формат.");
      }
      // Возвращаем временную ссылку, чтобы не сохранять битый файл
      return replicateVideoUrl!;
    }

    // Загружаем видео в Supabase Storage
    const supabaseVideoUrl = await uploadVideoToSupabaseStorage(userId, videoBlob, replicateVideoUrl);

    // Возвращаем постоянную ссылку Supabase
    return supabaseVideoUrl;
  } catch (error) {
    console.error('Error generating video with Replicate:', error);
    if (typeof toast === "function") {
      let errorText = typeof error === "object" && error !== null && "message" in error
        ? (error as any).message
        : String(error);
      toast.error?.("Ошибка при загрузке видео в Supabase: " + errorText);
    }
    throw error;
  }
};

/**
 * Опрашивает API Replicate для получения результата генерации видео
 */
const pollVideoCompletion = async (predictionId: string, maxAttempts = 60): Promise<any> => {
  console.log(`Starting to poll for video generation result (ID: ${predictionId})`);
  toast.info("Генерация видео в процессе. Это может занять несколько минут...");

  let attempt = 0;
  let retryDelay = 5000; // Начальная задержка 5 секунд
  const maxRetryDelay = 15000; // Максимальная задержка в 15 секунд

  while (attempt < maxAttempts) {
    try {
      // Используем серверный API-эндпоинт для проверки статуса
      const response = await fetch(`${API_URL}/api/replicate/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          predictionId
        })
      });

      // Обработка ошибки превышения лимита запросов (429)
      if (response.status === 429) {
        console.log(`Rate limit exceeded (429) while polling. Retrying in ${retryDelay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));

        // Увеличиваем задержку для следующей попытки
        retryDelay = Math.min(retryDelay * 2, maxRetryDelay);
        attempt++;
        continue;
      }

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`Error polling prediction (attempt ${attempt + 1}):`, errorData);
        throw new Error(errorData.detail || 'Failed to check video generation status');
      }

      const prediction = await response.json();
      const status = prediction.status;

      console.log(`Polling attempt ${attempt + 1}, status: ${status}`);

      if (status === 'succeeded') {
        toast.success("Видео успешно сгенерировано!");
        return prediction;
      } else if (status === 'failed') {
        toast.error(`Генерация видео не удалась: ${prediction.error || 'Неизвестная ошибка'}`);
        throw new Error(prediction.error || 'Video generation failed');
      } else if (status === 'canceled') {
        toast.error("Генерация видео была отменена");
        throw new Error('Video generation was canceled');
      }

      // Статус все еще "starting" или "processing", ждем и опрашиваем снова
      // Для видео используем более длительные интервалы
      const delay = attempt < 5 ? 5000 : 10000; // 5 секунд для первых 5 попыток, потом 10 секунд
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    } catch (error) {
      console.error(`Error during polling (attempt ${attempt + 1}):`, error);
      // Если ошибка не связана с состоянием предсказания, пробуем снова
      await new Promise(resolve => setTimeout(resolve, retryDelay));

      // Увеличиваем задержку для следующей попытки
      retryDelay = Math.min(retryDelay * 2, maxRetryDelay);
      attempt++;
    }
  }

  toast.error("Время ожидания генерации видео истекло");
  throw new Error('Timeout waiting for video generation');
};

export default generateVideoWithReplicate;
