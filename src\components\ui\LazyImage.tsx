import React, { useState, useEffect, useCallback } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholderClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
}

// Кэш для уже загруженных изображений
const imageCache = new Map<string, string>();

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  placeholderClassName,
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [retryAttempted, setRetryAttempted] = useState(false);

  // Стабилизируем колбэки с помощью useCallback
  // Это гарантирует, что они не будут меняться при каждом рендере родителя,
  // если только сами onLoad/onError не изменятся.
  const stableOnLoad = useCallback(() => {
    onLoad?.();
  }, [onLoad]);

  const stableOnError = useCallback(() => {
    onError?.();
  }, [onError]);

  useEffect(() => {
    let isMounted = true;
    // Сбрасываем состояние ошибки и загрузки при изменении src
    setError(false);
    setIsLoading(true);
    setImageSrc(''); // Очищаем предыдущее изображение
    setRetryAttempted(false); // Сбрасываем флаг повторной попытки

    const loadImage = () => {
      if (!src) {
        if (isMounted) {
          console.error(`LazyImage: src is empty or undefined for alt: "${alt}"`);
          setError(true);
          setIsLoading(false);
          stableOnError(); // Вызываем колбэк ошибки
        }
        return;
      }

      // Если изображение уже было загружено ранее, используем его из кэша
      if (imageCache.has(src)) {
        if (isMounted) {
          setImageSrc(imageCache.get(src) || '');
          setIsLoading(false);
          stableOnLoad(); // Вызываем колбэк загрузки
        }
        return;
      }

      // Иначе загружаем изображение
      const img = new Image();
      img.src = src;

      img.onload = () => {
        if (isMounted) {
          // Сохраняем в кэш
          imageCache.set(src, src);
          setImageSrc(src);
          setIsLoading(false);
          stableOnLoad(); // Вызываем колбэк загрузки
        }
      };

      img.onerror = (event: any) => { // Приводим тип к any для доступа к свойствам
        if (isMounted) {
          console.error(`LazyImage: Error loading image. Src: "${src}", Alt: "${alt}". Event:`, event);
          // Попытка получить более специфическую информацию об ошибке, если доступно
          // Проверяем, является ли event объектом и имеет ли свойство error
          if (event && typeof event === 'object' && 'error' in event && event.error) {
             console.error('LazyImage: Detailed Error:', event.error);
          } else if (event && typeof event === 'object' && 'message' in event) {
             console.error('LazyImage: Error Message:', event.message);
          } else {
             console.error('LazyImage: No specific error details available on event object.');
          }

          // Попробуем перезагрузить изображение один раз через 3 секунды
          if (!retryAttempted) {
            console.log(`LazyImage: Попытка перезагрузки изображения через 3 секунды: ${src}`);
            setRetryAttempted(true);
            setTimeout(() => {
              if (isMounted) {
                loadImage();
              }
            }, 3000);
            return;
          }

          setError(true);
          setIsLoading(false);
          stableOnError(); // Вызываем колбэк ошибки
        }
      };
    };

    loadImage();

    // Очистка
    return () => {
      isMounted = false;
    };
  }, [src, stableOnLoad, stableOnError, alt]); // Добавляем stableOnLoad, stableOnError и alt в зависимости

  if (isLoading) {
    return (
      <div
        className={`${placeholderClassName || className} animate-pulse bg-gray-200 dark:bg-gray-700`}
        aria-label={`Загрузка ${alt}...`}
      />
    );
  }

  if (error) {
    // Если произошла ошибка загрузки, не отображаем ничего
    return null;
  }

  return (
    <div className="overflow-hidden rounded-xl w-full h-full">
      <img
        src={imageSrc}
        alt={alt}
        className={`object-contain w-full h-full block ${className || ''}`}
        loading="lazy"
      />
    </div>
  );
};

export default LazyImage;
