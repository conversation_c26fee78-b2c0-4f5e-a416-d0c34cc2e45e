// Скрипт для принудительной очистки кэша CSP
(function() {
  // Устанавливаем метку времени в localStorage
  const timestamp = new Date().getTime();
  localStorage.setItem('csp_cache_buster', timestamp);
  
  // Выводим сообщение в консоль
  console.log('CSP cache buster initialized:', timestamp);
  
  // Функция для добавления параметра версии к ресурсам
  function addVersionToResources() {
    document.querySelectorAll('link[rel="stylesheet"], script[src]').forEach(el => {
      if (el.src || el.href) {
        const url = new URL(el.src || el.href, window.location.origin);
        if (url.hostname === window.location.hostname) {
          url.searchParams.set('v', timestamp);
          if (el.src) el.src = url.toString();
          if (el.href) el.href = url.toString();
        }
      }
    });
  }
  
  // Запускаем после загрузки DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addVersionToResources);
  } else {
    addVersionToResources();
  }
})(); 