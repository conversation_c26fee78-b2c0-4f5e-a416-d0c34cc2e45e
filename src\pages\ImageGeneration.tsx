import { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Wand2, Image, Download, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { saveImageGeneration, getImageGenerations, getUserCredits } from '@/utils/database';
import { supabase } from '@/utils/supabase';
import RecentGenerations from '@/components/generation/RecentGenerations';
import DottedBackground from '@/components/ui/dotted-background';
import AppLayout from '@/components/layout/AppLayout';
import { generateImageWithReplicate } from '@/utils/replicateImageGeneration';
import ImageInput from '@/components/generation/ImageInput';
import ImageCountSelector from '@/components/generation/ImageCountSelector';
import AspectRatioSelector from '@/components/generation/AspectRatioSelector';
import CharacterReferenceInput from '@/components/generation/CharacterReferenceInput';
import BagelModeSelector from '@/components/generation/BagelModeSelector';
import SEOHead from '@/components/SEOHead';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ImageModal from "@/components/ui/ImageModal";
import PrivateToggle from "@/components/ui/PrivateToggle";
import ShimmerEffect from "@/components/ui/ShimmerEffect";

interface GeneratedImageData {
  url?: string;
  b64_json?: string;
}

interface ModelOption {
  id: 'together' | 'ideogram' | 'fluxpro' | 'hidream' | 'minimax-image01' | 'imagen4' | 'bagel' | 'flux-kontext-pro' | 'flux-kontext-max';
  name: string;
  description: string;
  cost: number;
  image: string;
  isNew?: boolean; // Добавляем флаг для новых моделей
}

interface RecentGenerationDisplayItem {
  id: string;
  type: "image";
  prompt: string;
  result: string; // image URL
  timestamp: Date;
  local?: boolean;
  aspectRatioString?: string; // e.g., "1024:1024"
}

// Helper function to parse aspect ratio string
const parseAspectRatio = (aspectRatioString: string): { width: number; height: number } => {
  switch (aspectRatioString) {
    case "1:1": return { width: 1024, height: 1024 };
    case "16:9": return { width: 1792, height: 1024 };
    case "9:16": return { width: 1024, height: 1792 };
    case "4:3": return { width: 1024, height: 768 };
    case "3:4": return { width: 768, height: 1024 };
    case "3:2": return { width: 1080, height: 720 };
    case "2:3": return { width: 720, height: 1080 };
    default:
      console.warn(`Unsupported aspect ratio: ${aspectRatioString}, attempting to parse or defaulting to 1024x1024`);
      const parts = aspectRatioString.split(':');
      if (parts.length === 2) {
        const wRatio = parseInt(parts[0], 10);
        const hRatio = parseInt(parts[1], 10);
        if (!isNaN(wRatio) && !isNaN(hRatio) && wRatio > 0 && hRatio > 0) {
            // Scale to a common base, e.g., 1024 for the larger dimension, ensuring divisibility by common factors (e.g., 8 or 64)
            let finalWidth, finalHeight;
            if (wRatio >= hRatio) {
                finalWidth = 1024;
                finalHeight = Math.round((1024 * hRatio) / wRatio);
            } else {
                finalHeight = 1024;
                finalWidth = Math.round((1024 * wRatio) / hRatio);
            }
            // Ensure dimensions are multiples of 8 (common requirement for some models)
            finalWidth = Math.round(finalWidth / 8) * 8;
            finalHeight = Math.round(finalHeight / 8) * 8;
            return { width: finalWidth > 0 ? finalWidth : 1024, height: finalHeight > 0 ? finalHeight : 1024 };
        }
      }
      return { width: 1024, height: 1024 }; // Fallback
  }
};

const ImageGeneration = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const { theme } = useTheme();
  const [prompt, setPrompt] = useState("");
  const location = useLocation();

  // Подхватываем prompt из query-параметра
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const promptFromQuery = params.get('prompt');
    if (promptFromQuery) setPrompt(promptFromQuery);
  }, [location.search]);
  const [imageCount, setImageCount] = useState<number>(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStep, setGenerationStep] = useState(0);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImageData[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string[]>(
    []
  );
  // удалено: selectedImageForModal, setSelectedImageForModal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hoveredModelImage, setHoveredModelImage] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [aspectRatio, setAspectRatio] = useState<string>("1:1");
  const [selectedService, setSelectedService] = useState<'ideogram' | 'fluxpro' | 'hidream' | 'minimax-image01' | 'imagen4' | 'bagel' | 'flux-kontext-pro' | 'flux-kontext-max'>('ideogram');
  const [userCredits, setUserCredits] = useState(0);
  const [isLoadingCredits, setIsLoadingCredits] = useState(false);
  const [recentGenerations, setRecentGenerations] = useState<RecentGenerationDisplayItem[]>([]);

  // Новые состояния для новых моделей
  const [characterReferenceImages, setCharacterReferenceImages] = useState<string[]>([]);
  const [bagelMode, setBagelMode] = useState<'text2img' | 'edit'>('text2img');
  const [editImage, setEditImage] = useState<string | null>(null);

  // Состояние для приватности генерации
  const [isPrivate, setIsPrivate] = useState<boolean>(false);

  // Добавляем useRef для отслеживания таймера обновления генераций
  const updateTimerId = useRef<number | null>(null);
  const isLoadingGenerations = useRef<boolean>(false);

  // Supabase Realtime подписка на новые генерации
  useEffect(() => {
    if (!user?.id) return;
    // Подписка на вставку новых строк в image_generations для текущего пользователя
    const channel = supabase
      .channel('realtime-image-generations')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'image_generations',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          // payload.new содержит новую генерацию
          const newGen = payload.new;
          if (newGen && newGen.image_urls && newGen.image_urls.length > 0) {
            setGeneratedImages(
              newGen.image_urls.map((url: any) => ({
                url: Array.isArray(url) ? url[0] : (typeof url === 'object' && url?.url ? url.url : String(url))
              }))
            );
            setGenerationStep(4);
            toast.success('Новое изображение(я) сгенерировано!');
            loadRecentGenerations(); // Refresh recent generations
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const loadRecentGenerations = async () => {
    if (user?.id && !isLoadingGenerations.current) {
      isLoadingGenerations.current = true;
      try {
        const fetchedGenerations = await getImageGenerations(user.id);
        if (fetchedGenerations && fetchedGenerations.length > 0) {
          const formattedGenerations = fetchedGenerations.map(gen => {
            const imageUrl = gen.image_urls && gen.image_urls.length > 0 ? gen.image_urls[0] : null;

            if (!imageUrl || !gen.id) return null;

            // gen.created_at from getImageGenerations is a string, needs conversion
            return {
              id: gen.id,
              type: "image" as const,
              prompt: gen.prompt || '',
              result: imageUrl,
              timestamp: new Date(gen.created_at),
              local: gen.local || false,
              aspectRatioString: gen.aspect_ratio || "1:1",
            };
          }).filter(Boolean) as RecentGenerationDisplayItem[];

          const sortedGenerations = formattedGenerations
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, 5);

          setRecentGenerations(sortedGenerations);
        } else {
          setRecentGenerations([]);
        }
      } catch (error) {
        console.error("Error loading recent image generations:", error);
        toast.error(t('imageGen.error.generation'));
        setRecentGenerations([]);
      } finally {
        isLoadingGenerations.current = false;
      }
    }
  };

  useEffect(() => {
    if (user?.id) {
      loadRecentGenerations();
    }
    // Clear timeout on unmount if updateTimerId was used for polling, not strictly needed for current loadRecentGenerations
    return () => {
      if (updateTimerId.current) {
        clearTimeout(updateTimerId.current);
      }
    };
  }, [user?.id, i18n.language]);

  // Функция для получения картинки модели
  const getModelImage = (modelId: string): string => {
    switch (modelId) {
      case 'ideogram':
        return '/Ideogram.png';
      case 'hidream':
        return '/Ideogram3.png';
      case 'minimax-image01':
        return '/Image1.png';
      case 'imagen4':
        return '/Imagen4.png';
      case 'fluxpro':
        return theme === 'dark' ? '/FluxPro_Dark.png' : '/FluxPro_Light.png';
      case 'bagel':
        return theme === 'dark' ? '/Bagel_Dark.png' : '/Bagel_Light.png';
      case 'flux-kontext-pro':
        return theme === 'dark' ? '/FluxKontextProDark.webp' : '/FluxKontextProLight.webp';
      case 'flux-kontext-max':
        return theme === 'dark' ? '/FluxKontextMaxDark.webp' : '/FluxKontextMaxLight.webp';
      default:
        return '/Ideogram.png';
    }
  };

  // Предопределенные стили изображений
  const imageStyleOptions = [
    { id: 'photorealistic', name: i18n.language === 'ru' ? 'Фотореалистичный' : 'Photorealistic', prompt: 'ultra high resolution photorealistic image, masterfully composed, perfect professional lighting, exquisite detail, state-of-the-art digital photography, 8K resolution, expert framing, studio-quality lighting, crisp focus, stunning photographic realism' },
    { id: 'paint', name: i18n.language === 'ru' ? 'Живопись' : 'Painting', prompt: 'masterful oil painting, exhibition-quality artwork, rich textures, visible brushstrokes, deep pigments, canvas texture, museum-worthy composition, fine art painting techniques, glazing layers, impasto highlights, expertly balanced color harmony' },
    { id: 'watercolor', name: i18n.language === 'ru' ? 'Акварель' : 'Watercolor', prompt: 'delicate watercolor painting, translucent washes, controlled water flow, bleeding colors, subtle color gradients, handmade watercolor paper texture, spontaneous wet-in-wet technique, graceful brushwork, soft color blending, skillful white space preservation' },
    { id: 'sketch', name: i18n.language === 'ru' ? 'Эскиз' : 'Sketch', prompt: 'masterful hand-drawn sketch, confident linework, subtle shading, precise hatching technique, graphite pencil texture, expert proportions, deliberate mark-making, varied line weight, thoughtful composition, professional draftsmanship' },
    { id: 'cartoon', name: i18n.language === 'ru' ? 'Мультяшный' : 'Cartoon', prompt: 'professional cartoon illustration, stylized character design, clean vector-like linework, vibrant flat colors, appealing exaggerated proportions, polished animation-ready style, professional character design, cohesive visual language' },
    { id: 'abstract', name: i18n.language === 'ru' ? 'Абстрактный' : 'Abstract', prompt: 'sophisticated abstract composition, gallery-quality non-representational artwork, bold geometric forms, harmonious color theory, meaningful negative space, emotionally evocative, museum-worthy abstraction, balanced visual rhythm, conceptual depth' },
    { id: 'fantasy', name: i18n.language === 'ru' ? 'Фэнтези' : 'Fantasy', prompt: 'epic fantasy artwork, mystical atmosphere, magical elements, ethereal lighting, detailed fantasy world, high fantasy aesthetic, otherworldly landscape, mythical creatures, arcane symbols, majestic scenery, intricate fantasy details' },
    { id: 'cyberpunk', name: i18n.language === 'ru' ? 'Киберпанк' : 'Cyberpunk', prompt: 'futuristic cyberpunk scene, neon-lit urban dystopia, high-tech low-life aesthetic, cybernetic enhancements, rain-slicked streets, holographic displays, vibrant contrasting colors, digital decay, foggy atmosphere, towering megastructures, advanced technology' },
  ];

  // Модели для генерации изображений
  const modelOptions: ModelOption[] = [
    { id: 'ideogram', name: t('imageGen.models.ideogram'), description: t('imageGen.models.ideogramDesc'), cost: 10, image: getModelImage('ideogram') },
    { id: 'fluxpro', name: t('imageGen.models.fluxpro') || 'Flux Pro', description: t('imageGen.models.fluxproDesc') || 'Model specialized for photo editing and enhancement', cost: 15, image: getModelImage('fluxpro') },
    { id: 'flux-kontext-pro', name: t('imageGen.models.fluxKontextPro') || 'Flux Kontext Pro', description: t('imageGen.models.fluxKontextProDesc') || 'State-of-the-art image editing model with high-quality outputs and excellent prompt following', cost: 10, image: getModelImage('flux-kontext-pro'), isNew: true },
    { id: 'flux-kontext-max', name: t('imageGen.models.fluxKontextMax') || 'Flux Kontext Max', description: t('imageGen.models.fluxKontextMaxDesc') || 'Premium model with maximum performance and improved typography generation', cost: 25, image: getModelImage('flux-kontext-max'), isNew: true },
    { id: 'hidream', name: t('imageGen.models.ideogram3'), description: t('imageGen.models.ideogram3Desc'), cost: 15, image: getModelImage('hidream') },
    { id: 'minimax-image01', name: 'Image-01', description: t('imageGen.models.minimaxDesc'), cost: 5, image: getModelImage('minimax-image01') },
    { id: 'imagen4', name: 'Imagen-4', description: t('imageGen.models.imagen4Desc'), cost: 15, image: getModelImage('imagen4') },
    { id: 'bagel', name: 'Bagel', description: t('imageGen.models.bagelDesc'), cost: 25, image: getModelImage('bagel') }
  ];

  // Подхватываем model из query-параметра после определения modelOptions
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const modelFromQuery = params.get('model');

    if (modelFromQuery) {
      // Проверяем, что модель существует в списке доступных моделей
      const validModel = modelOptions.find(m => m.id === modelFromQuery);
      if (validModel) {
        setSelectedService(modelFromQuery as any);
      }
    }
  }, [location.search, modelOptions]);

  // Загружаем баланс пользователя при монтировании компонента
  useEffect(() => {
    const loadUserCredits = async () => {
      if (user?.id) {
        setIsLoadingCredits(true);
        try {
          const credits = await getUserCredits(user.id);
          setUserCredits(Number(credits));
        } catch (error) {
          console.error('Ошибка загрузки баланса:', error);

          // При сетевых ошибках не изменяем значение
          if (!(error instanceof Error && error.message.includes('Network error'))) {
            setUserCredits(0);
          }
        } finally {
          setIsLoadingCredits(false);
        }
      }
    };

    loadUserCredits();

    // Слушатель события обновления кредитов
    const handleCreditsUpdated = (event: CustomEvent) => {
      const { userId, newBalance } = event.detail;
      if (user?.id === userId) {
        console.log(`Обновление баланса на странице изображений: ${newBalance} кредитов`);
        setUserCredits(Number(newBalance));
      }
    };

    // Добавляем слушатель события
    window.addEventListener('creditsUpdated', handleCreditsUpdated as EventListener);

    return () => {
      window.removeEventListener('creditsUpdated', handleCreditsUpdated as EventListener);
    };
  }, [user?.id]);

  // Получение стоимости в токенах
  const getGenerationCost = () => {
    const selectedModel = modelOptions.find(m => m.id === selectedService);
    const costPerImage = selectedModel?.cost || 2; // Default to 2 if model not found

    // Cost is always per image multiplied by the number of images requested.
    return costPerImage * imageCount;
  };

  const getEnhancedPrompt = () => {
    if (!selectedStyle) return prompt;
    const style = imageStyleOptions.find(s => s.id === selectedStyle);
    if (!style) return prompt;
    return `${prompt}. ${style.prompt}`;
  };

  const handleGenerate = async () => {
    // Логируем выбранный сервис сразу при запуске
    console.log('handleGenerate: selectedService =', selectedService);

    if (!prompt.trim()) {
      toast.error(t('imageGen.error.promptRequired'));
      return;
    }

    if (!user) {
      toast.error(t('imageGen.error.authRequired'));
      return;
    }

    const selectedModelDetails = modelOptions.find(m => m.id === selectedService);
    if (!selectedModelDetails) {
      toast.error(t('imageGen.error.modelNotFound'));
      return;
    }

    const costPerImage = selectedModelDetails.cost;
    const totalCost = costPerImage * imageCount;

    if (userCredits < totalCost) {
      toast.error(t('imageGen.error.insufficientCredits', { count: totalCost }));
      return;
    }

    setIsGenerating(true);
    setGenerationStep(1); // Start step
    setGeneratedImages([]); // Clear previous images
    setSelectedImage([]);

    const enhancedPrompt = getEnhancedPrompt();
    const { width, height } = parseAspectRatio(aspectRatio); // Parse aspect ratio string

    // Логируем параметры для отладки до try/catch
    console.log('handleGenerate: enhancedPrompt =', enhancedPrompt, 'width =', width, 'height =', height, 'aspectRatio =', aspectRatio, 'selectedImage =', selectedImage);

    try {
      let finalImageUrls: string[] = [];
      let replicatePredictionId: string | null = null;

      // Блок для together удалён, так как этой модели больше нет
      if (selectedService === 'ideogram' || selectedService === 'hidream' || selectedService === 'minimax-image01' || selectedService === 'imagen4' || selectedService === 'bagel' || selectedService === 'flux-kontext-pro' || selectedService === 'flux-kontext-max') {
        setGenerationStep(2); // API call step
        let replicateOptions: any;
        if (selectedService === 'ideogram' || selectedService === 'hidream') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: selectedModelDetails.id,
            userId: user.id,
            numberOfImages: imageCount,
            width: width,
            height: height,
            aspectRatio: aspectRatio,
            reference_images: selectedImage && selectedImage.length > 0 ? selectedImage : undefined,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else if (selectedService === 'minimax-image01') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: 'minimax-image01',
            userId: user.id,
            aspectRatio: aspectRatio || "1:1",
            numberOfImages: imageCount,
            characterReferenceImages: characterReferenceImages,
            promptOptimizer: true,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else if (selectedService === 'imagen4') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: 'imagen4',
            userId: user.id,
            aspectRatio: aspectRatio || "1:1",
            safetyFilterLevel: "block_medium_and_above",
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else if (selectedService === 'bagel') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: 'bagel',
            userId: user.id,
            bagelMode: bagelMode,
            editImage: bagelMode === 'edit' ? editImage : undefined,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else if (selectedService === 'flux-kontext-pro') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: 'flux-kontext-pro',
            userId: user.id,
            input_image: selectedImage && selectedImage.length > 0 ? selectedImage[0] : undefined,
            aspectRatio: aspectRatio || "1:1",
            seed: undefined,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else if (selectedService === 'flux-kontext-max') {
          replicateOptions = {
            prompt: enhancedPrompt,
            model: 'flux-kontext-max',
            userId: user.id,
            input_image: selectedImage && selectedImage.length > 0 ? selectedImage[0] : undefined,
            aspectRatio: aspectRatio || "1:1",
            seed: undefined,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
          };
        } else {
          throw new Error(t('imageGen.error.invalidService'));
        }

        // Для отладки: логируем выбранный сервис и параметры
        console.log('selectedService:', selectedService, 'replicateOptions:', replicateOptions);

        const replicateResponse = await generateImageWithReplicate(replicateOptions);
        setGenerationStep(3); // Processing step
        if (replicateResponse && replicateResponse.images && replicateResponse.images.length > 0) {
          finalImageUrls = replicateResponse.images.map((img: GeneratedImageData) => {
            if (img.url) return img.url;
            if (img.b64_json) return `data:image/png;base64,${img.b64_json}`;
            return null;
          }).filter(Boolean) as string[];

          // Получаем replicatePredictionId из ответа
          replicatePredictionId = replicateResponse.replicatePredictionId || null;

          if (finalImageUrls.length === 0) {
            throw new Error(t('imageGen.error.noImagesReplicate'));
          }
        } else {
          throw new Error(t('imageGen.error.noImagesReplicate'));
        }

      } else if (selectedService === 'fluxpro') {
        setGenerationStep(2); // API call step
        const replicateOptions = {
            prompt: enhancedPrompt,
            model: 'black-forest-labs/flux-canny-pro',
            userId: user.id,
            control_image: selectedImage && selectedImage.length > 0
              ? selectedImage[0]
              : undefined,
            guidance: 30,
            steps: 28,
            safety_tolerance: 2,
            prompt_upsampling: false,
            output_format: 'jpg',
            seed: undefined,
            visibility: (isPrivate ? 'private' : 'public') as 'public' | 'private',
        };
        // Для отладки: логируем выбранный сервис и параметры
        console.log('selectedService:', selectedService, 'replicateOptions:', replicateOptions);

        const replicateResponse = await generateImageWithReplicate(replicateOptions);
        setGenerationStep(3); // Processing step

        if (replicateResponse && replicateResponse.images && replicateResponse.images.length > 0) {
          finalImageUrls = replicateResponse.images.map((img: GeneratedImageData) => {
            if (img.url) return img.url;
            if (img.b64_json) return `data:image/png;base64,${img.b64_json}`;
            return null;
          }).filter(Boolean) as string[];

          // Обновляем replicatePredictionId для fluxpro
          if (!replicatePredictionId) {
            replicatePredictionId = replicateResponse.replicatePredictionId || null;
          }

          if (finalImageUrls.length === 0) {
            throw new Error(t('imageGen.error.noImagesReplicate'));
          }
        } else {
          throw new Error(t('imageGen.error.noImagesReplicate'));
        }
      } else {
        throw new Error(t('imageGen.error.invalidService'));
      }

      if (finalImageUrls.length > 0) {
        setGeneratedImages(
          finalImageUrls.map((url: any) =>
            typeof url === 'string'
              ? { url }
              : Array.isArray(url) && typeof url[0] === 'string'
                ? { url: url[0] }
                : typeof url === 'object' && url && 'url' in url
                  ? { url: url.url }
                  : { url: String(url) }
          )
        );
        setSelectedImage(finalImageUrls);
        setGenerationStep(4); // Completion step
        toast.success(t('imageGen.success.generation', { count: finalImageUrls.length }));

        // Save all generated images. saveImageGeneration will handle processing the array.
        // Note: For webhook-based models, saving is handled by webhook, so we skip client-side saving
        const isWebhookBasedModel = ['ideogram', 'hidream', 'ideogram3', 'minimax-image01', 'imagen4', 'bagel', 'flux-kontext-pro', 'flux-kontext-max'].includes(selectedService);

        if (finalImageUrls.length > 0 && !isWebhookBasedModel) {
          await saveImageGeneration({
            user_id: user.id,
            prompt: enhancedPrompt,
            image_url: finalImageUrls, // Pass the array of URLs
            model: selectedModelDetails.id,
            style: selectedStyle ? imageStyleOptions.find(s => s.id === selectedStyle)?.name : 'Default',
            aspect_ratio: aspectRatio,
            cost_per_image: costPerImage, // Pass the cost per image
            visibility: isPrivate ? 'private' : 'public', // Передаем параметр приватности
            replicate_prediction_id: replicatePredictionId || undefined, // Добавляем prediction ID для предотвращения дублей
            // Properties num_images and service are removed as they are not in GenerationSaveData
          });
        }

        // The 'creditsUpdated' event is now dispatched by updateUserCredits within saveImageGeneration.
        // The client-side dispatch here was removed to avoid redundancy and potential conflicts.
        window.dispatchEvent(new CustomEvent('generationsUpdated', { detail: { userId: user.id, type: 'image' } }));

        // Обновляем кредиты после завершения генерации (для webhook-based моделей)
        if (['ideogram', 'hidream', 'ideogram3', 'minimax-image01', 'imagen4', 'bagel', 'flux-kontext-pro', 'flux-kontext-max'].includes(selectedService)) {
          setTimeout(async () => {
            if (user?.id) {
              try {
                const credits = await getUserCredits(user.id);
                setUserCredits(credits);
                console.log('Обновлен баланс кредитов после генерации:', credits);
              } catch (error) {
                console.error('Ошибка обновления баланса:', error);
              }
            }
          }, 2000); // Даем webhook время обработать результат
        }

        setTimeout(loadRecentGenerations, 1000);

      } else {
        throw new Error(t('imageGen.error.noImagesGenerated'));
      }

    } catch (error: any) {
      console.error("Error generating image:", error);
      toast.error(error.message || t('imageGen.error.generation'));
      setGenerationStep(0);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = (image: GeneratedImageData) => {
    if (!image) return;

    try {
      // Получаем URL изображения
      const imageUrl = image.url || (image.b64_json ? `data:image/png;base64,${image.b64_json}` : null);

      if (!imageUrl) {
        toast.error(t('imageGen.error.downloadFailed'));
        return;
      }

      if (image.url) {
        // Скачиваем изображение по url через fetch, чтобы не открывать новую вкладку
        fetch(image.url)
          .then(response => response.blob())
          .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `enigma-generated-${Date.now()}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
          })
          .catch(() => {
            toast.error(t('imageGen.error.downloadFailed'));
          });
      } else if (image.b64_json) {
        // Для base64 данных напрямую создаем ссылку
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `enigma-generated-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Ошибка при скачивании изображения:', error);
      toast.error(t('imageGen.error.downloadFailed'));
    }
  };

  // Calculate aspect ratio class for display
  const getAspectRatioClass = () => {
    const parsed = parseAspectRatio(aspectRatio); // Use the existing parsing function
    if (!parsed || parsed.width === 0) return "aspect-square"; // Fallback for safety
    const ratio = parsed.height / parsed.width;
    if (ratio === 1) return "aspect-square";
    // Adjusted logic for portrait/landscape based on common aspect ratios
    if (parsed.width < parsed.height) return "aspect-[9/16]"; // Portrait
    if (parsed.width > parsed.height) return "aspect-[16/9]"; // Landscape
    return "aspect-square"; // Default for 1:1 or fallback
  };

  // Добавляем эффект для загрузки генераций при монтировании
  useEffect(() => {
    const loadUserGenerations = async () => {
      if (user?.id && !isLoadingGenerations.current) {
        isLoadingGenerations.current = true;
        try {
          console.log('Загрузка генераций пользователя:', user.id);
          const userGenerations = await getImageGenerations(user.id);

          // Используем Map для дополнительной проверки и предотвращения дубликатов
          const uniqueGenerationsMap = new Map();

          userGenerations.forEach(gen => {
            // Используем ID как ключ для предотвращения дубликатов
            if (!uniqueGenerationsMap.has(gen.id)) {
              uniqueGenerationsMap.set(gen.id, {
                id: gen.id,
                type: "image" as const,
                prompt: gen.prompt,
                result: gen.image_urls && gen.image_urls.length > 0 ? gen.image_urls[0] : null,
                timestamp: new Date(gen.created_at), // gen.created_at is a string
                local: gen.local || false,
                aspectRatioString: gen.aspect_ratio
              });
            }
          });

          // Преобразуем Map в массив и сортируем по времени создания (сначала новые)
          const uniqueGenerations = Array.from(uniqueGenerationsMap.values())
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

          console.log(`Получено ${userGenerations.length} генераций, после удаления дубликатов: ${uniqueGenerations.length}`);
          setRecentGenerations(uniqueGenerations);
        } catch (error) {
          console.error('Не удалось загрузить генерации пользователя:', error);
        } finally {
          isLoadingGenerations.current = false;
        }
      }
    };

    loadUserGenerations();

    // Добавляем слушатель события обновления генераций с дебаунсингом
    const handleGenerationsUpdated = (event: any) => {
      if (event.detail && event.detail.userId === user?.id) {
        console.log('Обнаружено обновление генераций, планируем обновление списка...');

        // Очищаем предыдущий таймер, если он существует
        if (updateTimerId.current !== null) {
          clearTimeout(updateTimerId.current);
        }

        // Устанавливаем новый таймер с увеличенной задержкой для debounce
        updateTimerId.current = window.setTimeout(() => {
          console.log('Выполняем отложенное обновление списка генераций');
          loadUserGenerations();
          updateTimerId.current = null;
        }, 1500); // Увеличиваем задержку до 1.5 секунд для дебаунсинга
      }
    };

    window.addEventListener('generationsUpdated', handleGenerationsUpdated);

    return () => {
      window.removeEventListener('generationsUpdated', handleGenerationsUpdated);
      // Очищаем таймер при размонтировании компонента
      if (updateTimerId.current !== null) {
        clearTimeout(updateTimerId.current);
      }
    };
  }, [user?.id]);

  // Очистка таймера при размонтировании компонента
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  return (
    <AppLayout>
      <SEOHead
        title={i18n.language === 'ru'
          ? "Генерация изображений с помощью ИИ - UMA.AI"
          : "AI Image Generation - UMA.AI"
        }
        description={i18n.language === 'ru'
          ? "Создавайте уникальные изображения с помощью искусственного интеллекта. Множество стилей, высокое качество, быстрая генерация."
          : "Create unique images using artificial intelligence. Multiple styles, high quality, fast generation."
        }
        keywords={i18n.language === 'ru'
          ? "генерация изображений, ИИ, искусственный интеллект, создание картинок, нейросети"
          : "image generation, AI, artificial intelligence, picture creation, neural networks"
        }
        url="https://umaai.site/image"
      />
      <div className="py-4 sm:py-8 px-4 sm:px-8 relative min-h-screen">
        <DottedBackground type="dots" className="opacity-30" />

        <div className="flex flex-col xl:flex-row max-w-6xl mx-auto relative z-10">
          <div className="flex-1">
            <div className="mb-6 sm:mb-8">
              <h1 className="text-2xl sm:text-3xl font-bold mb-3 text-foreground">{t('header.imageGen')}</h1>
              <p className="text-foreground/70">{t('features.imageGenDescription')}</p>
              {!isLoadingCredits && user?.id && (
                <div className="mt-2 flex flex-wrap items-center gap-2">
                  <p className="text-sm font-medium flex items-center">
                    <Sparkles size={16} className="mr-1 text-yellow-500" />
                    Баланс: {userCredits} токенов
                  </p>
                </div>
              )}
            </div>

            <ShimmerEffect isActive={isGenerating}>
              <div className="rounded-xl p-4 sm:p-6 mb-10 border bg-card text-card-foreground shadow-sm">
              <div className="relative mb-4">
                {selectedService === 'minimax-image01' && (
                  <div className="w-full mb-4">
                    <CharacterReferenceInput
                      images={characterReferenceImages}
                      onChange={setCharacterReferenceImages}
                      maxImages={3}
                    />
                  </div>
                )}

                {selectedService === 'bagel' && (
                  <div className="w-full mb-4">
                    <BagelModeSelector
                      mode={bagelMode}
                      onChange={setBagelMode}
                    />
                    {bagelMode === 'edit' && (
                      <div className="mt-4">
                        <ImageInput
                          onImageSelect={(img) => setEditImage(img)}
                          value={editImage ? [editImage] : []}
                          mode="default"
                          onImagesChange={(imgs) => setEditImage(imgs[0] || null)}
                        />
                      </div>
                    )}
                  </div>
                )}

                <div className="flex items-start gap-2 w-full">

                  {selectedService === 'hidream' && (
                    <ImageInput
                      onImageSelect={(img) => setSelectedImage(img ? [img] : [])}
                      multiple
                      value={selectedImage}
                      mode="style_reference"
                      onImagesChange={setSelectedImage}
                    />
                  )}
                  {(selectedService === 'fluxpro' || selectedService === 'flux-kontext-pro' || selectedService === 'flux-kontext-max') && (
                    <div className="w-12 flex-shrink-0 flex items-center h-full">
                      <ImageInput
                        onImageSelect={img => setSelectedImage(img ? [img] : [])}
                        value={selectedImage}
                        mode="style_reference"
                        onImagesChange={setSelectedImage}
                      />
                    </div>
                  )}
                  <Textarea
                    value={prompt}
                    onChange={e => setPrompt(e.target.value)}
                    placeholder={t('imageGen.placeholders.imagePrompt')}
                    className={cn(
                      "resize-none text-base rounded-xl min-h-[44px] max-h-40 flex-1",
                      selectedService === 'fluxpro' && "ml-1"
                    )}
                    style={{
                      minWidth: 0,
                      width: "100%",
                    }}
                  />
                  <Button
                    className={cn(
                      "h-11 min-w-[90px] ml-2",
                      isGenerating && "w-[135px]"
                    )}
                    onClick={handleGenerate}
                    disabled={
                      isGenerating ||
                      !prompt.trim() ||
                      ((selectedService === 'fluxpro' || selectedService === 'flux-kontext-pro' || selectedService === 'flux-kontext-max') && (!selectedImage || selectedImage.length === 0))
                    }
                  >
                    {isGenerating ? (
                      <div className="flex items-center justify-center w-full">
                        <span className="mr-2 text-white">{t('imageGen.actions.generating')}</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"></div>
                          <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-200"></div>
                          <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-400"></div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <Wand2 size={18} className="mr-2" />
                        <span className="mr-2 text-white">{i18n.language === 'ru' ? 'Создать' : 'Generate'}</span>
                        <div className="flex items-center text-xs bg-white/20 text-white px-1.5 py-0.5 rounded-full">
                          <Sparkles size={12} className="mr-0.5" />
                          {getGenerationCost()}
                        </div>
                      </div>
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-6">
                {/* Первая строка: Image count, Format, Model, Style, Private */}
                <div className="flex flex-wrap items-center gap-3">
                  {selectedService !== 'imagen4' && selectedService !== 'bagel' && selectedService !== 'flux-kontext-pro' && selectedService !== 'flux-kontext-max' && (
                    <ImageCountSelector
                      count={imageCount}
                      onChange={setImageCount}
                    />
                  )}

                  {selectedService !== 'fluxpro' && (
                    <AspectRatioSelector
                      value={aspectRatio}
                      onChange={setAspectRatio}
                      model={selectedService}
                    />
                  )}

                  <div className="flex items-center gap-3">
                    <span className="text-sm text-foreground/70">
                      {i18n.language === 'ru' ? 'Модель:' : 'Model:'}
                    </span>
                    <Select
                      value={selectedService}
                      onValueChange={(value: any) => {
                        setSelectedService(value);
                        // Сбрасываем выбранное изображение, если модель не поддерживает img2img
                        if (!['hidream', 'fluxpro', 'flux-kontext-pro', 'flux-kontext-max'].includes(value) && selectedImage) {
                          setSelectedImage([]);
                        }
                      }}
                    >
                      <SelectTrigger className="w-auto min-w-[160px]">
                        <SelectValue>
                          {modelOptions.find(m => m.id === selectedService)?.name || t('imageGen.settings.selectModel')}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="w-[90vw] sm:w-[400px] md:w-[450px] lg:w-[500px]">
                        {modelOptions.map(model => (
                          <SelectItem key={model.id} value={model.id} className="p-3">
                            <div className="flex items-start gap-3 w-full">
                              <div className="flex-shrink-0 relative">
                                <img
                                  src={model.image}
                                  alt={model.name}
                                  className="w-16 h-16 sm:w-20 sm:h-20 object-contain rounded-lg cursor-pointer transition-transform hover:scale-105"
                                  onMouseEnter={() => {
                                    // Очищаем предыдущий таймер если есть
                                    if (hoverTimeout) {
                                      clearTimeout(hoverTimeout);
                                    }
                                    // Устанавливаем новый таймер на 500мс
                                    const timeout = setTimeout(() => {
                                      setHoveredModelImage(model.image);
                                    }, 500);
                                    setHoverTimeout(timeout);
                                  }}
                                  onMouseLeave={() => {
                                    // Очищаем таймер при уходе курсора
                                    if (hoverTimeout) {
                                      clearTimeout(hoverTimeout);
                                      setHoverTimeout(null);
                                    }
                                    setHoveredModelImage(null);
                                  }}
                                />
                                {selectedService === model.id && (
                                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                      <svg className="w-4 h-4 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  </div>
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium text-sm truncate">{model.name}</span>
                                  <div className="flex items-center gap-2">
                                    {model.isNew && (
                                      <div className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                                        New
                                      </div>
                                    )}
                                    <div className="flex items-center text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full whitespace-nowrap">
                                      <Sparkles size={12} className="mr-1" />
                                      {model.cost}
                                    </div>
                                  </div>
                                </div>
                                <p className="text-xs text-muted-foreground line-clamp-2">
                                  {model.description || (i18n.language === 'ru' ? 'Высококачественная генерация изображений' : 'High-quality image generation')}
                                </p>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className="text-sm text-foreground/70">
                      {i18n.language === 'ru' ? 'Стиль:' : 'Style:'}
                    </span>
                    <Select
                      value={selectedStyle || ''}
                      onValueChange={(value) => setSelectedStyle(value || null)}
                    >
                      <SelectTrigger className="w-full sm:w-[220px] md:w-[250px]">
                        <SelectValue placeholder={t('imageGen.settings.selectStyle') || "Select style"}>
                          {selectedStyle ? imageStyleOptions.find(s => s.id === selectedStyle)?.name : t('imageGen.settings.selectStyle') || "Select style"}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {imageStyleOptions.map((style) => (
                          <SelectItem key={style.id} value={style.id}>
                            <div className="flex flex-col">
                              <span>{style.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Private Toggle - всегда последний параметр */}
                  <PrivateToggle
                    isPrivate={isPrivate}
                    onToggle={setIsPrivate}
                  />
                </div>
              </div>

              {isGenerating && (
                <div className="mt-8 text-center">
                  <div className="w-full bg-foreground/10 rounded-full h-2 mb-4">
                    <div
                      className="bg-foreground h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${(generationStep / 4) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-foreground/70 text-sm">
                    {generationStep === 1 && t('imageGen.steps.starting')}
                    {generationStep === 2 && t('imageGen.steps.creating')}
                    {generationStep === 3 && t('imageGen.steps.finalizing')}
                    {generationStep === 4 && t('imageGen.steps.saving')}
                  </p>
                </div>
              )}

              {generatedImages.length > 0 && (
                <div className="mt-8">
                  <h3 className="text-xl font-bold mb-4 text-foreground">{t('imageGen.sections.generatedImages')}</h3>
                  <div className={cn(
                    "grid gap-4",
                    generatedImages.length === 4 ? "grid-cols-2" : // 2x2 for 4 images
                    generatedImages.length === 3 ? "grid-cols-1 sm:grid-cols-3" :
                    generatedImages.length === 2 ? "grid-cols-1 sm:grid-cols-2" :
                    "grid-cols-1" // 1 column for 1 image
                  )}>
                    {generatedImages.map((image, index) => (
                      <div
                        key={index}
                        className={cn(
                          "relative group rounded-lg overflow-hidden shadow-md cursor-pointer",
                          generatedImages.length === 4 ? "aspect-square" : getAspectRatioClass()
                        )}
                        onClick={() => setIsModalOpen(true)}
                      >
                        <img
                          src={image.url || `data:image/png;base64,${image.b64_json}`}
                          alt={`${t('imageGen.alt.generated', { index: index + 1 })}`}
                          className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 backdrop-blur-[2px]">
                          <Button
                            size="icon"
                            variant="ghost"
                            className="text-white hover:bg-white/20 p-2 rounded-full"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownload(image);
                            }}
                            title={t('imageGen.actions.download')}
                          >
                            <Download className="w-6 h-6" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <ImageModal
                      isOpen={isModalOpen}
                      onClose={() => setIsModalOpen(false)}
                      images={generatedImages.map(img => ({
                        url: img.url || `data:image/png;base64,${img.b64_json}`,
                        aspectRatio
                      }))}
                    />
                  </div>
                </div>
              )}

              {!isGenerating && generatedImages.length === 0 && (
                <div className="mt-8 text-center p-10 border border-dashed border-foreground/20 rounded-2xl">
                  <Image className="w-12 h-12 mx-auto text-foreground/30" />
                   <p className="text-muted-foreground mt-2">{t('imageGen.emptyState')}</p> {/* Added empty state text */}
                </div>
              )}
              </div>
            </ShimmerEffect>
          </div>

          <div className="w-full xl:w-64 xl:ml-6 mb-6 xl:mb-0 xl:hidden">
            <RecentGenerations
              generations={recentGenerations}
              type="image"
            />
          </div>

          <div className="w-64 ml-6 hidden xl:block">
            <RecentGenerations
              generations={recentGenerations}
              type="image"
              className="sticky top-24"
            />
          </div>
        </div>
      </div>
      {/* Закрывающий тег для .py-4 sm:py-8 px-4 sm:px-8 relative min-h-screen */}

      {/* Hover preview для изображений моделей */}
      {hoveredModelImage && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
          <div className="relative bg-black/90 rounded-lg p-4 max-w-2xl max-h-[80vh] shadow-2xl pointer-events-auto">
            <button
              onClick={() => {
                setHoveredModelImage(null);
                if (hoverTimeout) {
                  clearTimeout(hoverTimeout);
                  setHoverTimeout(null);
                }
              }}
              className="absolute top-2 right-2 bg-white/20 hover:bg-white/30 text-white rounded-full p-1.5 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <img
              src={hoveredModelImage}
              alt="Model example preview"
              className="max-w-full max-h-full object-contain rounded"
            />
          </div>
        </div>
      )}
    </AppLayout>
  );
};

export default ImageGeneration;
