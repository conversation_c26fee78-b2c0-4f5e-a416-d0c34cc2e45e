-- Простой скрипт для подтверждения email пользователей и создания профилей

-- 1. Показать информацию о существующих пользователях
SELECT 
  id, 
  email, 
  email_confirmed_at,
  created_at,
  last_sign_in_at
FROM auth.users
ORDER BY created_at DESC
LIMIT 10;

-- 2. Подтвердить всех пользователей, у которых не подтвержден email
UPDATE auth.users 
SET email_confirmed_at = NOW()
WHERE email_confirmed_at IS NULL;

-- 3. Показать обновленную информацию о пользователях
SELECT 
  id, 
  email, 
  email_confirmed_at IS NOT NULL as is_confirmed,
  created_at
FROM auth.users
ORDER BY created_at DESC
LIMIT 10;

-- 4. Проверить конкретного пользователя по email и создать профиль
DO $$
DECLARE
  user_email TEXT := '<EMAIL>';
  found_user_id UUID;
BEGIN
  -- Получаем ID пользователя
  SELECT id INTO found_user_id 
  FROM auth.users
  WHERE email = user_email;
  
  IF found_user_id IS NOT NULL THEN
    RAISE NOTICE 'Пользователь найден: ID = %', found_user_id;
    
    -- Подтвердить email пользователя
    UPDATE auth.users
    SET email_confirmed_at = NOW()
    WHERE id = found_user_id AND email_confirmed_at IS NULL;
    
    -- Проверяем наличие профиля
    IF EXISTS (SELECT 1 FROM profiles WHERE id = found_user_id) THEN
      RAISE NOTICE 'Профиль пользователя найден';
    ELSE
      -- Создаем профиль
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (found_user_id, user_email, 'User', 10, NOW(), NOW());
      
      RAISE NOTICE 'Создан профиль для пользователя: %', found_user_id;
    END IF;
  ELSE
    RAISE NOTICE 'Пользователь с email % не найден', user_email;
  END IF;
END $$;

-- 5. Проверить регистрацию с новым email, если нужно (для тестирования)
-- Замените '<EMAIL>' на желаемый email
DO $$
DECLARE
  new_email TEXT := '<EMAIL>';
  new_user_id UUID;
BEGIN
  -- Проверяем, существует ли пользователь с таким email
  SELECT id INTO new_user_id
  FROM auth.users
  WHERE email = new_email;
  
  IF new_user_id IS NULL THEN
    RAISE NOTICE 'Пользователь с email % не существует. Чтобы зарегистрировать его, используйте форму регистрации в приложении', new_email;
  ELSE
    RAISE NOTICE 'Пользователь с email % уже существует (ID: %)', new_email, new_user_id;
    
    -- Подтверждаем email
    UPDATE auth.users
    SET email_confirmed_at = NOW()
    WHERE id = new_user_id;
    
    RAISE NOTICE 'Email пользователя подтвержден';
  END IF;
END $$; 