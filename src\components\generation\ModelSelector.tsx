import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>I<PERSON>, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

interface ModelOption {
  value: string;
  label: string;
}

interface ModelSelectorProps {
  models: ModelOption[];
  currentModel: string;
  onSelectModel: (modelValue: string) => void;
}

const ModelSelector = ({ models, currentModel, onSelectModel }: ModelSelectorProps) => {
  const currentModelOption = models.find(model => model.value === currentModel) || models[0];
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="neo-glass rounded-full px-4 py-1.5 flex items-center gap-2 hover:bg-foreground/5 text-foreground border border-border">
          <Sparkles size={16} className="text-green-500" />
          <span className="text-sm font-medium">{currentModelOption.label}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 neo-glass bg-background border border-border">
        <DropdownMenuLabel className="text-foreground">Select AI Model</DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-border" />
          {models.map((model) => (
            <DropdownMenuItem
              key={model.value}
              onClick={() => onSelectModel(model.value)}
              className={cn(
                "flex items-center justify-between cursor-pointer text-foreground",
                "hover:bg-foreground/5",
                currentModel === model.value && "bg-foreground/5"
              )}
            >
              {model.label}
              {currentModel === model.value && (
                <CheckIcon size={16} className="text-green-500" />
              )}
            </DropdownMenuItem>
          ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ModelSelector;
