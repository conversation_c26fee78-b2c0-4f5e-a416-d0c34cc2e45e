import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/layout/Header';
import HeroGeometric from '../components/ui/geometric-hero';
import FeaturesSection from '@/components/landing/FeaturesSection';
import ShowcaseSection from '@/components/landing/ShowcaseSection';
import PricingSection from '@/components/landing/PricingSection';
import CtaSection from '@/components/landing/CtaSection';
import Footer from '@/components/landing/Footer';
import GlowEffect from '@/components/ui/GlowEffect';
import SEOHead from '@/components/SEOHead';
import StructuredData from '@/components/StructuredData';


const Index = () => {
  const { t, i18n } = useTranslation();
  const featuresSectionRef = useRef<HTMLDivElement>(null);


  return (
    <div className="relative overflow-hidden">
      {/* SEO компоненты */}
      <SEOHead
        title={i18n.language === 'ru'
          ? "UMA.AI - Платформа искусственного интеллекта для генерации контента"
          : "UMA.AI - AI Platform for Content Generation"
        }
        description={i18n.language === 'ru'
          ? "Создавайте тексты, изображения и видео с помощью искусственного интеллекта. UMA.AI - мощная платформа для генерации контента с использованием передовых AI технологий."
          : "Create texts, images and videos using artificial intelligence. UMA.AI is a powerful platform for content generation using advanced AI technologies."
        }
        keywords={i18n.language === 'ru'
          ? "искусственный интеллект, генерация изображений, генерация видео, генерация текста, AI, нейросети, машинное обучение, UMA.AI"
          : "artificial intelligence, image generation, video generation, text generation, AI, neural networks, machine learning, UMA.AI"
        }
        url="https://umaai.site"
      />
      <StructuredData type="website" />
      <StructuredData type="organization" />
      <StructuredData type="software" />
      <StructuredData type="service" />

      {/* Glow effect positioned behind content but visible at the bottom */}
      <div className="absolute bottom-0 left-0 right-0 w-full z-0 pointer-events-none">
        <GlowEffect />
      </div>
      
      {/* Максимально мягкое свечение по бокам экрана в районе заголовка */}
      <div className="absolute w-full h-full z-0 pointer-events-none">
        {/* Левое мягкое свечение - нежно-голубое */}
        <div
          className="absolute top-[30%] left-[-15%] w-[40%] h-[55vh]"
          style={{
            background: "radial-gradient(circle at center, rgba(56, 189, 248, 0.08) 0%, rgba(56, 189, 248, 0.03) 40%, rgba(56, 189, 248, 0) 70%)",
            filter: "blur(150px)",
            opacity: 0.5
          }}
        />
        
        {/* Правое мягкое свечение - нежно-фиолетовое */}
        <div
          className="absolute top-[35%] right-[-15%] w-[40%] h-[55vh]"
          style={{
            background: "radial-gradient(circle at center, rgba(167, 139, 250, 0.08) 0%, rgba(167, 139, 250, 0.03) 40%, rgba(167, 139, 250, 0) 70%)",
            filter: "blur(150px)",
            opacity: 0.5
          }}
        />
      </div>
      
      {/* Content comes first in the DOM for accessibility */}
      <div className="relative z-10">
        <Header />
        <HeroGeometric
          title1={t('landing.welcomeTo')}
          title2={t('landing.appName')}
        />


        <div ref={featuresSectionRef}>
          <FeaturesSection />
        </div>
        <ShowcaseSection />
        <PricingSection />
        <CtaSection />
        <Footer />
      </div>
    </div>
  );
};

export default Index;
