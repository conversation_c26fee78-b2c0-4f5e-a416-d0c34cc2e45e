// Импортируем необходимые модули
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

// Константы с данными для аутентификации в ЮKassa
const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
const API_URL = 'https://api.yookassa.ru/v3';

export default async function handler(req, res) {
  // Разрешаем только POST-запросы для создания платежа
  if (req.method === 'POST') {
    try {
      const {
        amount,
        currency = 'RUB',
        description = 'Покупка кредитов UMA.AI',
        userId,
        creditsAmount,
        returnUrl
      } = req.body;

      // Создаем уникальный ключ идемпотентности для запроса
      const idempotenceKey = uuidv4();
      
      // Формируем данные для запроса
      const paymentData = {
        amount: {
          value: amount.toFixed(2),
          currency
        },
        capture: true,
        confirmation: {
          type: 'redirect',
          return_url: returnUrl
        },
        description,
        metadata: {
          userId,
          creditsAmount
        }
      };
      
      // HTTP Basic Auth
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      // Отправляем запрос к API ЮKassa
      const response = await axios.post(`${API_URL}/payments`, paymentData, {
        headers: {
          'Authorization': `Basic ${authHeader}`,
          'Idempotence-Key': idempotenceKey,
          'Content-Type': 'application/json'
        }
      });
      
      return res.status(200).json(response.data);
    } catch (error) {
      console.error('Ошибка при создании платежа:', error.response?.data || error.message);
      return res.status(error.response?.status || 500).json({
        error: 'Ошибка при создании платежа',
        details: error.response?.data || error.message
      });
    }
  } 
  // Для GET-запросов с ID платежа - получаем информацию о платеже
  else if (req.method === 'GET') {
    try {
      const { paymentId } = req.query;
      
      if (!paymentId) {
        return res.status(400).json({ error: 'Payment ID is required' });
      }
      
      // HTTP Basic Auth
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      // Отправляем запрос к API ЮKassa
      const response = await axios.get(`${API_URL}/payments/${paymentId}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`,
          'Content-Type': 'application/json'
        }
      });
      
      return res.status(200).json(response.data);
    } catch (error) {
      console.error('Ошибка при получении информации о платеже:', error.response?.data || error.message);
      return res.status(error.response?.status || 500).json({
        error: 'Ошибка при получении информации о платеже',
        details: error.response?.data || error.message
      });
    }
  }
  // Если метод запроса не поддерживается
  else {
    res.setHeader('Allow', ['POST', 'GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 