// Serverless function for Fal AI API (Kling Video 2.1 models)
// Supports: fal-ai/kling-video/v2.1/master/text-to-video, fal-ai/kling-video/v2.1/master/image-to-video,
//           fal-ai/kling-video/v2.1/pro/image-to-video, fal-ai/kling-video/v2.1/standard/image-to-video

import fetch from "node-fetch";
import sharp from "sharp";
import { updateUserCredits, uploadImageToSupabase } from "../utils/database.js";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Функция расчёта стоимости генерации видео для Fal Kling 2.1
function getFalVideoGenerationCost(model, duration) {
  if (!model || !duration) return 0;

  const durationNum = parseInt(duration);

  if (model.includes("kling-video/v2.1/master")) {
    return durationNum === 5 ? 320 : 640; // Master: 320 за 5 сек, 640 за 10 сек
  }

  if (model.includes("kling-video/v2.1/pro")) {
    return durationNum === 5 ? 120 : 240; // Pro: 120 за 5 сек, 240 за 10 сек
  }

  if (model.includes("kling-video/v2.1/standard")) {
    return durationNum === 5 ? 75 : 150; // Standard: 75 за 5 сек, 150 за 10 сек
  }

  return 0;
}

// Функция для обработки и кропа изображения
async function downloadAndCropImage(imageUrl, targetWidth, targetHeight) {
  try {
    console.log(`[FAL] Downloading image from: ${imageUrl}`);

    // Проверяем валидность URL
    if (!imageUrl || typeof imageUrl !== 'string') {
      throw new Error(`Invalid image URL: ${imageUrl}`);
    }

    // Добавляем таймаут для запроса
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 секунд таймаут

    const response = await fetch(imageUrl, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; FAL-API/1.0)',
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error(`[FAL] HTTP error downloading image: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.buffer();
    console.log(`[FAL] Downloaded image, size: ${buffer.length} bytes`);

    // Проверяем, что buffer не пустой
    if (!buffer || buffer.length === 0) {
      throw new Error('Downloaded image buffer is empty');
    }

    // Получаем информацию об изображении
    const metadata = await sharp(buffer).metadata();
    console.log(`[FAL] Original image dimensions: ${metadata.width}x${metadata.height}, format: ${metadata.format}`);

    // Проверяем, что изображение валидное
    if (!metadata.width || !metadata.height) {
      throw new Error('Invalid image: unable to read dimensions');
    }

    // Обрабатываем изображение
    const processedBuffer = await sharp(buffer)
      .resize(targetWidth, targetHeight, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 90 })
      .toBuffer();

    console.log(`[FAL] Processed image to ${targetWidth}x${targetHeight}`);

    return {
      buffer: processedBuffer,
      format: 'jpg',
      width: targetWidth,
      height: targetHeight
    };
  } catch (error) {
    console.error('[FAL] Error processing image:', error);
    console.error('[FAL] Error details:', {
      message: error.message,
      stack: error.stack,
      imageUrl: imageUrl,
      targetWidth: targetWidth,
      targetHeight: targetHeight
    });
    throw error;
  }
}

// Функция получения размеров по соотношению сторон
function getSizeByAspect(aspectRatio) {
  const aspectMap = {
    "16:9": { width: 1024, height: 576 },
    "9:16": { width: 576, height: 1024 },
    "1:1": { width: 1024, height: 1024 }
  };
  return aspectMap[aspectRatio] || aspectMap["16:9"];
}

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { model, input, userId } = req.body;

    console.log('[FAL] Received request:', { model, userId, inputKeys: Object.keys(input || {}) });

    if (!model) {
      return res.status(400).json({ error: 'Model is required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Рассчитываем стоимость
    const cost = getFalVideoGenerationCost(model, input?.duration || "5");
    console.log(`[FAL] Generation cost: ${cost} credits`);

    // Списываем кредиты, если указан userId
    if (userId && cost > 0) {
      try {
        console.log(`[FAL] Deducting ${cost} credits from user ${userId}`);
        await updateUserCredits(userId, -cost, 'generation', `Fal Video Generation (${model})`);
      } catch (creditError) {
        console.error('[FAL] Error deducting credits:', creditError);
        return res.status(400).json({ error: 'Insufficient credits or credit deduction failed' });
      }
    }

    // Подготавливаем input для Fal API
    let processedInput = { ...input };

    // Обрабатываем изображение, если оно есть
    if (input?.image_url) {
      try {
        console.log(`[FAL] Processing input image: ${input.image_url}`);
        const { width, height } = getSizeByAspect(input.aspect_ratio || "16:9");
        console.log(`[FAL] Target dimensions: ${width}x${height} for aspect ratio: ${input.aspect_ratio || "16:9"}`);

        const { buffer, format } = await downloadAndCropImage(input.image_url, width, height);

        // Загружаем обработанное изображение в Supabase
        const uniqueFileName = `fal-inputs/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${format}`;
        console.log(`[FAL] Uploading processed image to Supabase: ${uniqueFileName}`);

        const imageUrl = await uploadImageToSupabase(buffer, uniqueFileName, 'generations');

        processedInput.image_url = imageUrl;
        console.log(`[FAL] Processed and uploaded image: ${imageUrl}`);
      } catch (imageError) {
        console.error('[FAL] Error processing image:', imageError);
        console.error('[FAL] Image processing failed, details:', {
          originalUrl: input.image_url,
          aspectRatio: input.aspect_ratio,
          errorMessage: imageError.message
        });

        // Возвращаем ошибку пользователю вместо продолжения с оригинальным URL
        return res.status(400).json({
          error: 'Image processing failed',
          message: `Не удалось обработать входное изображение: ${imageError.message}`,
          details: imageError.message
        });
      }
    }

    // Удаляем параметры, которые не нужны для Fal API
    delete processedInput.userId;
    delete processedInput.metadata_visibility;

    console.log('[FAL] Sending request to Fal API:', { model, input: processedInput });

    // Отправляем запрос к Fal API
    const falResponse = await fetch(`https://queue.fal.run/${model}`, {
      method: 'POST',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: processedInput
      })
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL] API error:', errorText);

      // Возвращаем кредиты в случае ошибки
      if (userId && cost > 0) {
        try {
          await updateUserCredits(userId, cost, 'refund', `Fal API Error Refund (${model})`);
          console.log(`[FAL] Refunded ${cost} credits to user ${userId}`);
        } catch (refundError) {
          console.error('[FAL] Error refunding credits:', refundError);
        }
      }

      return res.status(falResponse.status).json({
        error: 'Fal API error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL] API response:', result);

    // Запускаем фоновый polling для обработки результата
    if (result.request_id) {
      try {
        console.log(`[FAL] Starting background polling for request ${result.request_id}`);

        // Запускаем HTTP вызов к фоновому поллингу
        const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site';

        // Запускаем обычный поллинг
        fetch(`${baseUrl}/api/fal/poll`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requestId: result.request_id,
            model: model,
            userId: userId,
            prompt: processedInput.prompt,
            visibility: input.metadata_visibility || 'public',
            cost: cost
          })
        }).catch(pollError => {
          console.error('[FAL] Error starting background polling:', pollError);
        });

        // Также запускаем простой сохранитель (попытается сразу получить результат)
        fetch(`${baseUrl}/api/fal-save`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requestId: result.request_id,
            model: model,
            userId: userId,
            prompt: processedInput.prompt,
            visibility: input.metadata_visibility || 'public'
          })
        }).catch(saveError => {
          console.error('[FAL] Error starting immediate save:', saveError);
        });

        console.log(`[FAL] Background polling started for request ${result.request_id}`);
      } catch (pollError) {
        console.error('[FAL] Error starting background polling:', pollError);
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
