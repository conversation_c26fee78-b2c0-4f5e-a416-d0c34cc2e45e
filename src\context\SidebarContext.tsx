import React, { createContext, useContext, useState, useEffect } from 'react';

interface SidebarContextType {
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export const useSidebarState = () => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebarState must be used within a SidebarProvider');
  }
  return context;
};

interface SidebarProviderProps {
  children: React.ReactNode;
}

export const SidebarProvider: React.FC<SidebarProviderProps> = ({ children }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Создаем глобальное событие для синхронизации состояния
  useEffect(() => {
    const handleSidebarStateChange = (event: CustomEvent) => {
      setIsExpanded(event.detail.expanded);
    };

    window.addEventListener('sidebarStateChanged', handleSidebarStateChange as EventListener);
    
    return () => {
      window.removeEventListener('sidebarStateChanged', handleSidebarStateChange as EventListener);
    };
  }, []);

  const value = {
    isExpanded,
    setIsExpanded: (expanded: boolean) => {
      setIsExpanded(expanded);
      // Отправляем событие для синхронизации
      window.dispatchEvent(new CustomEvent('sidebarStateChanged', { 
        detail: { expanded } 
      }));
    }
  };

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  );
};
