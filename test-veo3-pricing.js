// Тест новой ценовой модели Veo3
// Запустить: node test-veo3-pricing.js

// Имитация функции расчета стоимости
function getVeo3GenerationCost(duration, generateAudio = true) {
  // Veo3 поддерживает только 8 секунд
  // 1400 кредитов с аудио, 1000 кредитов без аудио
  return generateAudio ? 1400 : 1000;
}

// Тестовые случаи
console.log('=== Тест ценообразования Veo3 ===');

console.log('1. С аудио (по умолчанию):');
console.log(`   Стоимость: ${getVeo3GenerationCost(8)} кредитов`);
console.log(`   Ожидается: 1400 кредитов`);

console.log('\n2. С аудио (явно указано):');
console.log(`   Стоимость: ${getVeo3GenerationCost(8, true)} кредитов`);
console.log(`   Ожидается: 1400 кредитов`);

console.log('\n3. Без аудио:');
console.log(`   Стоимость: ${getVeo3GenerationCost(8, false)} кредитов`);
console.log(`   Ожидается: 1000 кредитов`);

console.log('\n4. Экономия при отключении аудио:');
const withAudio = getVeo3GenerationCost(8, true);
const withoutAudio = getVeo3GenerationCost(8, false);
const savings = withAudio - withoutAudio;
console.log(`   Экономия: ${savings} кредитов`);
console.log(`   Ожидается: 400 кредитов`);

console.log('\n=== Все тесты пройдены! ===');

// Проверка корректности
const tests = [
  { name: 'С аудио', result: getVeo3GenerationCost(8, true), expected: 1400 },
  { name: 'Без аудио', result: getVeo3GenerationCost(8, false), expected: 1000 },
  { name: 'По умолчанию', result: getVeo3GenerationCost(8), expected: 1400 },
  { name: 'Экономия', result: savings, expected: 400 }
];

let allPassed = true;
tests.forEach(test => {
  if (test.result !== test.expected) {
    console.error(`❌ ОШИБКА: ${test.name} - получено ${test.result}, ожидалось ${test.expected}`);
    allPassed = false;
  } else {
    console.log(`✅ ${test.name}: ${test.result} кредитов`);
  }
});

if (allPassed) {
  console.log('\n🎉 Все тесты прошли успешно!');
} else {
  console.log('\n❌ Некоторые тесты не прошли!');
  process.exit(1);
}
