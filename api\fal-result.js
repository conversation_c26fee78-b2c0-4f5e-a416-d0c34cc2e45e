// Fal API result endpoint for getting completed generation results
// GET /api/fal-result?requestId=<request_id>&model=<model_id>&userId=<user_id>&prompt=<prompt>&visibility=<visibility>

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL RESULT FIXED] ${req.method} request received with query:`, req.query);

  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    console.log(`[FAL RESULT] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility } = req.query;

    console.log('[FAL RESULT] Getting result for:', { requestId, model, userId });

    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL RESULT] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Сначала получаем статус, чтобы убедиться что генерация завершена
    const statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    console.log('[FAL RESULT] Getting status first from:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL RESULT] Status API error:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Fal API status error',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL RESULT] Status data:', statusData);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({
        error: 'Generation not completed',
        status: statusData.status
      });
    }

    // Используем response_url из статуса для получения результата
    const resultUrl = statusData.response_url;
    console.log('[FAL RESULT] Requesting result from response_url:', resultUrl);

    // Отправляем GET запрос к response_url
    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL RESULT] API error:', errorText);
      return res.status(falResponse.status).json({
        error: 'Fal API result error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL RESULT] Result response:', result);

    // Если есть видео и параметры для сохранения, сохраняем в БД
    if (result.video && result.video.url && userId && prompt) {
      try {
        console.log('[FAL RESULT] Saving video to database...');
        await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility || 'public', requestId);
        console.log('[FAL RESULT] Video saved to database successfully');
      } catch (saveError) {
        console.error('[FAL RESULT] Error saving video to database:', saveError);
        // Не возвращаем ошибку, так как видео все равно сгенерировано
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL RESULT] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Сохраняет видео в базу данных
 */
async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[FAL RESULT] Saving video to database: ${videoUrl}`);

    // Скачиваем видео
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[FAL RESULT] Downloaded video, size: ${videoBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    // Загружаем видео в Supabase Storage
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[FAL RESULT] Video uploaded to Storage: ${supabaseVideoUrl}`);

    // Генерируем thumbnail
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[FAL RESULT] Thumbnail uploaded: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[FAL RESULT] Error generating thumbnail:`, thumbnailError);
    }

    // Определяем стоимость
    let cost = 100; // По умолчанию
    if (model.includes('standard')) {
      cost = 75; // 5s = 75, 10s = 150
    } else if (model.includes('pro')) {
      cost = 120; // 5s = 120, 10s = 240
    } else if (model.includes('master')) {
      cost = 320; // 5s = 320, 10s = 640
    }

    // Сохраняем в БД
    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId,
      duration: 5, // По умолчанию
      cost: cost,
      visibility: visibility
    });

    console.log(`[FAL RESULT] Video generation saved to database with ID: ${videoId}`);

    // Автоматически создаем GIF превью для dashboard (в фоне)
    createGifThumbnailAsync(supabaseVideoUrl, savedGeneration.id, userId).catch(error => {
      console.error(`[FAL RESULT] Error creating GIF thumbnail:`, error);
    });

  } catch (error) {
    console.error(`[FAL RESULT] Error saving video to database:`, error);
    throw error;
  }
}

/**
 * Асинхронно создает GIF превью (не блокирует основной процесс)
 */
async function createGifThumbnailAsync(videoUrl, generationId, userId) {
  try {
    // Вызываем API для создания GIF
    const response = await fetch(`${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000'}/api/video/create-gif`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoUrl: videoUrl,
        generationId: generationId,
        userId: userId
      })
    });

    if (!response.ok) {
      throw new Error(`GIF creation API failed: ${response.status}`);
    }

    console.log(`[FAL RESULT] GIF creation started for generation ${generationId}`);
  } catch (error) {
    console.error(`[FAL RESULT] Error starting GIF creation:`, error);
  }
}
