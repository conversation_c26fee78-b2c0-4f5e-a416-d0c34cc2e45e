{"landing": {"introducing": "Introducing Uma AI", "welcomeTo": "Welcome to", "appName": "Uma AI", "title1": "Create with", "title2": "artificial intelligence", "subtitle": "Generate stunning text, images, and videos with our unified AI platform. Bring your creative visions to life in seconds.", "getStarted": "Get Started Free", "viewDemos": "View Demos", "ctaTitle": "Start Creating with Uma AI Today", "ctaSubtitle": "Join thousands of creators unlocking the power of AI-generated content. No credit card required to get started."}, "login": {"title": "Welcome Back", "subtitle": "Sign in to continue to Uma AI", "footer": "© {{year}} Uma AI. All rights reserved."}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "orContinueWith": "Or continue with", "name": "Name", "namePlaceholder": "Enter your name", "passwordTooShort": "Password must be at least 6 characters long", "signUpPrompt": "Don't have an account? ", "signUpLink": "Sign Up", "hasAccount": "Already have an account? ", "loginLink": "<PERSON><PERSON>", "forgotPasswordLink": "Forgot password?", "backToLogin": "Back to Login", "resetPasswordInstructions": "Enter your email for password reset", "resetPasswordSent": "Password reset email sent", "loginSuccess": "Login successful", "signInSuccess": "Login successful", "registerSuccess": "Registration successful! Logging you in...", "resetSuccess": "Password reset email sent! Check your inbox.", "authError": "Authentication failed. Check your credentials.", "socialSuccess": "Login via {{provider}} successful!", "socialError": "Login via {{provider}} failed. Please try again.", "showPassword": "Show password", "hidePassword": "Hide password", "footer": "© {{year}} Uma AI. All rights reserved.", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password"}, "buyCredits": {"buttonText": "Buy Credits", "title": "Buy Credits", "description": "Choose your plan", "test": "Test plan for development only", "testPlan": "Test", "credits": "credits", "pleaseSelectPlan": "Please select a plan", "creatingPayment": "Creating payment...", "redirecting": "Redirecting to payment page...", "loading": "Processing...", "proceedToPayment": "Proceed to Payment", "errors": {"invalidPaymentResponse": "Invalid payment response", "noConfirmationUrl": "No confirmation URL in payment response", "paymentCreation": "Error creating payment", "unknown": "Unknown error", "noEmail": "User email not found"}, "customAmount": "Custom Amount", "enterAmount": "Enter amount ($)", "minAmount": "Minimum amount: $20", "maxAmount": "Maximum amount: $1000", "creditsCalculated": "You will receive {{credits}} credits", "invalidAmount": "Enter a valid amount from $20 to $1000", "action": "Buy", "plans": {"test": {"name": "Test", "price": "$1", "credits": "1000 credits", "description": "Test plan for evaluation"}, "basic": {"name": "Basic", "price": "$10", "credits": "600 credits", "description": "For beginners"}, "standard": {"name": "Standard", "price": "$25", "credits": "1500 credits", "description": "Most popular choice"}, "premium": {"name": "Premium", "price": "$50", "credits": "4000 credits", "description": "For power users"}, "custom": {"name": "Ultra (Custom)", "price": "from $20", "credits": "credits at rate $1 = 2 credits", "description": "Choose any amount from $20 to $1000"}}, "features": {"text2img": "text2img (5 cr.)", "img2img": "img2img (10 cr.)", "video5s": "video (5s) (100 cr.)", "video9s": "video (9s) (150 cr.)", "unlimitedText": "* Text Generation - Unlimited"}}, "credits": {"unlimitedCredits": "Unlimited Credits", "plans": {"basic": {"name": "Basic"}, "standard": {"name": "Standard"}, "premium": {"name": "Premium"}}, "buyMore": "Buy More", "title": "Buy Credits", "description": "Choose a plan", "creditsLeft": "{{count}} credits left", "action": "Buy"}, "language": {"switch": "Switch Language", "select": "Select Language", "current": "Current Language", "en": "English", "ru": "Русский"}, "features": {"sectionTitle": "One Platform for AI Creation", "sectionSubtitle": "One platform for all your AI needs. Easily switch between different tools.", "textGenTitle": "Text Generation", "textGenDescription": "Create compelling content, stories, and responses with our advanced language models.", "imageGenTitle": "Image Generation", "imageGenDescription": "Turn your ideas into stunning visuals with our AI image generator.", "videoGenTitle": "Video Generation", "videoGenDescription": "Bring your stories to life with AI-generated videos. Turn text descriptions into captivating visual narratives."}, "showcase": {"sectionTitle": "Created with Uma AI", "sectionSubtitle": "See what's possible with our AI generation tools. From stunning visuals to creative content.", "item1Title": "Performance Machines", "item1Prompt": "Sports cars with custom styling in beautiful natural settings", "item2Title": "Urban Adventures", "item2Prompt": "Classic cars racing through futuristic cityscapes at night", "item3Title": "Luxury Escapes", "item3Prompt": "Luxury vehicles in scenic mountain locations with atmospheric mood"}, "header": {"dashboard": "Dashboard", "textGen": "Text Generation", "imageGen": "Image Generation", "videoGen": "Video Generation", "history": "History", "settings": "Settings", "profile": "Profile", "logout": "Logout", "loginButton": "<PERSON><PERSON>", "signUpButton": "Sign Up", "themeLight": "Light", "themeDark": "Dark", "themeSelect": "Select theme", "toggleTheme": "Toggle theme", "login": "Login / Sign Up"}, "sidebar": {"dashboard": "Dashboard", "history": "History", "text": "Text", "image": "Image", "video": "Video", "speech": "Speech", "analytics": "Analytics", "upgradePlan": "Upgrade Plan", "unlockFeatures": "Unlock all features", "profile": "Profile", "settings": "Settings", "home": "Home", "darkTheme": "Dark Theme", "lightTheme": "Light Theme"}, "pricing": {"sectionTitle": "Choose Your Plan", "sectionSubtitle": "Start for free, upgrade as you grow", "footer": "All prices in USD. VAT may apply.", "freePlan": {"title": "Test", "price": "$0.01", "description": "Test plan for development only", "button": "Start Test", "features": ["1000 credits", "text2img (5 cr.)", "img2img (10 cr.)", "video (5s) (100 cr.)", "video (9s) (150 cr.)"]}, "proPlan": {"title": "Basic", "price": "$3", "description": "Perfect for getting started", "button": "Choose Basic", "mostPopular": "For Beginners", "features": ["600 credits", "text2img (5 cr.)", "img2img (10 cr.)", "video (5s) (100 cr.)", "video (9s) (150 cr.)"]}, "standardPlan": {"title": "Standard", "price": "$7.50", "description": "Most popular choice", "button": "Choose Standard", "mostPopular": "Most Popular", "features": ["1500 credits", "text2img (5 cr.)", "img2img (10 cr.)", "video (5s) (100 cr.)", "video (9s) (150 cr.)"]}, "enterprisePlan": {"title": "Premium", "price": "$20", "description": "For power users", "button": "Choose Premium", "features": ["4000 credits", "text2img (5 cr.)", "img2img (10 cr.)", "video (5s) (100 cr.)", "video (9s) (150 cr.)"]}, "customPlan": {"title": "Ultra (Custom)", "price": "from $20", "description": "Choose any amount from $20 to $1000", "button": "<PERSON><PERSON>", "features": ["credits at rate $1 = 2 credits", "text2img (5 cr.)", "img2img (10 cr.)", "video (5s) (100 cr.)", "video (9s) (150 cr.)"]}}, "textGen": {"pageTitle": "Text Generation", "initialGreeting": "Hi! I'm <PERSON><PERSON>. How can I help you today?", "inputPlaceholder": "Enter your message...", "defaultError": "Failed to process request. Please try again.", "generationErrorToast": "Failed to generate response. Please try again.", "generationErrorMessage": "Sorry, there was an error processing your request. Please try again.", "chats": "Chats", "newChat": "New Chat", "loadChatError": "Error loading chat", "imageUploadErrorToast": "Error uploading image", "createMode": {"welcome": "Welcome to video creation mode! Describe what video you want to create.", "parametersTitle": "Specify video parameters", "parametersDescription": "Configure parameters for creating your video. Each parameter affects quality and generation cost.", "duration": "Total video duration", "fragmentDuration": "Fragment duration", "aspectRatio": "Frame format", "resolution": "Resolution", "transitionType": "Transition type", "privacy": "Privacy", "estimatedCost": "Project cost:", "confirmParameters": "Confirm parameters", "storyboardReady": "Storyboard ready", "storyboardDescription": "Your storyboard is created. You can edit any frame before video generation.", "regenerate": "Regenerate", "editPrompt": "Edit prompt", "confirmAndGenerate": "Confirm and start video generation"}}, "dashboard": {"title": "Dashboard", "subtitle": "Generate with AI", "tabAll": "All", "tabImage": "Images", "tabVideo": "Videos", "noExamples": "No generations to display", "noImageExamples": "No images to display", "noVideoExamples": "No videos to display", "history": "History", "historyDescription": "View your generation history", "noHistory": "Generation history is empty", "modal": {"seeMore": "See More", "seeLess": "See Less", "generatedImageAlt": "Generated image", "promptLabel": "Prompt:", "negativePromptLabel": "Negative Prompt:", "styleLabel": "Style:", "modelLabel": "Model:", "dimensionsLabel": "Dimensions:", "seedLabel": "Seed:", "closeButton": "Close", "downloadButton": "Download", "deleteButton": "Delete", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete this generation? This action cannot be undone.", "cancelButton": "Cancel", "errorImage": "Error loading image", "copyPrompt": "Copy Prompt", "downloadImage": "Download", "prompt": "Prompt", "usePrompt": "Use Prompt"}, "createImage": "Create Image", "createImageDesc": "Generate any image your imagination desires.", "createVideo": "Create Video", "createVideoDesc": "Create any video you want with AI."}, "imageGen": {"placeholders": {"imagePrompt": "Describe the image you want to create...", "style": "Select image style", "aspectRatio": "Select aspect ratio", "format": "Select format", "negativePrompt": "Enter negative prompt (optional)"}, "image": "image", "images": "images", "image_one": "image", "image_other": "images", "actions": {"generate": "Generate", "generating": "Generating", "download": "Download", "share": "Share", "save": "Save"}, "steps": {"starting": "Starting generation...", "creating": "Creating images...", "finalizing": "Finalizing...", "saving": "Saving image...", "complete": "Complete!"}, "sections": {"generatedImages": "Generated Images", "prompt": "Prompt", "settings": "Settings", "style": "Style", "aspectRatio": "Aspect Ratio", "imageCount": "Image Count", "format": "Format"}, "settings": {"modelLabel": "Generation model:", "selectModel": "Select model", "selectStyle": "Select style"}, "models": {"togetherDesc": "Fast image generation with fine details and artistic styles", "ideogram": "Ideogram 2", "ideogramDesc": "Universal model, supports img to img. Good at handling text", "fluxpro": "Flux Pro", "fluxproDesc": "Model specialized for photo editing and enhancement", "ideogram3": "Ideogram 3", "ideogram3Desc": "Next-generation model with improved quality and detail, supports img to img. Excellent with text", "minimaxDesc": "Highly detailed, hyperrealistic model", "imagen4Desc": "Advanced model for creating highly detailed images", "bagelDesc": "Versatile model with editing support and high quality. Perfect for image editing", "fluxKontextPro": "Flux Kontext Pro", "fluxKontextProDesc": "State-of-the-art image editing model with high-quality outputs and excellent prompt following", "fluxKontextMax": "Flux Kontext Max", "fluxKontextMaxDesc": "Premium model with maximum performance and improved typography generation"}, "options": {"imageCount": {"label": "Image count:", "one": "1 image", "two": "2 images", "three": "3 images", "four": "4 images"}, "format": {"label": "Format:", "square": "Square", "portrait": "Portrait", "landscape": "Landscape"}}, "error": {"invalidPrompt": "Please enter an image description", "emptyPrompt": "Please enter a prompt to generate an image.", "noPrompt": "Enter an image description", "promptTooLong": "Prompt is too long. Maximum 1000 characters.", "generation": "Error generating image. Please try again.", "rateLimit": "Too many requests. Please wait a few seconds.", "noImages": "Failed to generate images. Try changing the description.", "downloadFailed": "Failed to download image.", "promptRequired": "Please enter an image description", "authRequired": "Authentication required", "modelNotFound": "Model not found", "insufficientCredits": "Insufficient credits. Required: {{count}}", "noImagesTogether": "Failed to get images from Together AI", "noImagesReplicate": "Failed to get images from Replicate", "invalidService": "Unsupported generation service", "noImagesGenerated": "Failed to generate images"}, "success": {"generation": "Images generated successfully!", "save": "Image saved!", "download": "Image downloaded!"}, "characterReference": {"title": "Character Reference", "tooltip": "Upload face images to transfer character to generated image. Supports up to 3 images.", "uploadText": "Click or drag images here", "supportedFormats": "JPG, PNG, WebP", "maxSize": "Max 10MB", "maxReached": "Image limit reached ({{max}})", "tooManyImages": "Too many images. Maximum: {{max}}", "invalidFileType": "Unsupported file type. Use images.", "fileTooLarge": "File too large. Maximum 10MB.", "uploadError": "File upload error", "uploadSuccess": "Uploaded images: {{count}}", "previewAlt": "Image preview {{index}}", "imageCount": "{{current}} of {{max}} images"}, "bagel": {"modeTitle": "Operation Mode", "modes": {"text2img": "Generation", "text2imgDesc": "Create images from text description", "edit": "Editing", "editDesc": "Edit existing images with instructions"}}, "emptyState": "No generations yet. Create your first one!", "alt": {"generated": "Generated image", "selected": "Selected image for details"}}, "home": {"title": "Home", "subtitle": "Generate with AI"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "chat": "Text Generation", "image": "Image Generation", "video": "Video Generation", "speech": "Speech Generation", "history": "History", "profile": "Profile", "settings": "Settings", "upgradeAccount": "Upgrade Account", "buyCredits": "Buy Credits"}, "history": {"title": "History", "noHistory": "History is empty", "emptyMessage": "You don't have any saved generations yet", "download": "Download", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this generation?", "deleted": "Generation deleted"}, "profile": {"title": "Profile", "credits": "Credits", "refreshBalance": "Refresh Balance", "accountInfo": "Account Information", "signOut": "Sign Out", "deleteAccount": "Delete Account", "confirmDelete": "Are you sure you want to delete your account? This action is irreversible.", "secretTokens": "To<PERSON>s added!", "secretError": "Error adding tokens"}, "common": {"loading": "Loading..."}, "nav": {"videoGen": "Video Generation"}, "videoGen": {"pixverse": {"modelName": "Pixverse v4.5", "modelDescription": "Advanced model with unique effects and styles", "quality": "Quality", "motionMode": "Motion Mode", "style": "Style", "effect": "Effect", "seed": "Seed", "negativePrompt": "Negative Prompt", "settings": "Pixverse Settings", "qualityDescription": "Video resolution", "motionModeDescription": "Video motion mode", "seedDescription": "Random seed for reproducible generation", "negativePromptDescription": "What to avoid in the video", "styles": {"None": "None", "anime": "Anime", "3d_animation": "3D Animation", "clay": "<PERSON>", "cyberpunk": "Cyberpunk", "comic": "Comic"}, "effects": {"None": "None", "Let_s_YMCA_": "Let's YMCA!", "Subject_3_Fever": "Subject 3 Fever", "Ghibli_Live_": "Ghibli Live!", "Suit_Swagger": "Suit Swagger", "Muscle_Surge": "<PERSON><PERSON><PERSON>ge", "360__Microwave": "360° Microwave", "Warmth_of_Jesus": "Warmth of Jesus", "Emergency_Beat": "Emergency Beat", "Anything__Robot": "Anything, Robot", "Kungfu_Club": "Kungfu Club", "Mint_in_Box": "Mint in Box", "Retro_Anime_Pop": "Retro Anime Pop", "Vogue_Walk": "Vogue Walk", "Mega_Dive": "Mega Dive", "Evil_Trigger": "Evil Trigger"}, "motionModes": {"normal": "Normal", "smooth": "Smooth"}}, "kling": {"cfgScaleLabel": "Prompt deviation allowance"}, "veo3": {"modelName": "Veo3", "modelDescription": "Google's best video generation model. Generates high-quality videos with audio", "hasAudio": "Generates audio", "enhancePrompt": "Enhance Prompt", "enhancePromptDesc": "Automatically improves your prompt for better results", "generateAudio": "Generate Audio", "generateAudioDesc": "Generate sound for the video. Disabling reduces cost by 400 credits"}, "models": {"rayFlash2": "Fast and high-quality model. Supports image to video, start and end frame, looping", "kling16Standard": "Standard model with excellent quality and stability. Supports image to video", "kling16Pro": "High-quality model with detailed rendering. Supports image to video, start and end frame", "kling20": "Flagship model with dynamic movements and improved quality. Supports image to video", "pixverse": "Creative model with unique effects and styles. Supports image to video, start and end frame", "kling21Standard": "Standard model with good quality at affordable price. Image to video only", "kling21Pro": "Professional model with excellent quality and detail. Image to video only", "kling21Master": "Flagship model with superior quality. Supports text to video and image to video", "veo3": "Google's best video generation model. Generates 8-second high-quality videos with audio"}, "success": {"generation": "Video generated successfully!"}}, "speechGen": {"title": "Speech Generation", "textToSpeech": "Text to Speech", "voiceCloningTab": "Voice Cloning", "selectVoice": "Select Voice", "recordVoice": "Record Voice", "uploadAudio": "Upload Audio", "generateSpeech": "Generate Speech", "cloneVoice": "<PERSON><PERSON> Voice", "textPlaceholder": "Enter text to generate speech...", "voiceLibrary": {"title": "Voice Library", "default": "<PERSON><PERSON><PERSON>", "yours": "Yours", "community": "Community", "noUserVoices": "You don't have any cloned voices yet", "createFirstVoice": "Create your first voice in the \"Voice Cloning\" section", "noCommunityVoices": "No community voices yet", "beFirstToShare": "Be the first to share your voice!"}, "voiceCloning": {"voiceName": "Voice Name", "voiceNamePlaceholder": "Enter a name for your voice", "recordingTitle": "Voice Recording", "supportedFormats": "Supported formats: "}, "parameters": {"model": "Model", "speed": "Speed", "pitch": "Pitch", "volume": "Volume", "emotion": "Emotion", "languageBoost": "Language Boost", "englishNormalization": "English Normalization"}, "emotions": {"auto": "Auto", "neutral": "Neutral", "happy": "Happy", "sad": "Sad", "angry": "Angry", "fearful": "Fearful", "disgusted": "Disgusted", "surprised": "Surprised"}, "recording": {"start": "Start Recording", "stop": "Stop Recording", "recording": "Recording...", "duration": "Duration: {{duration}}", "maxDuration": "Maximum 5 minutes", "minDuration": "Minimum 10 seconds", "preview": "Preview", "rerecord": "Re-record"}, "cost": "Cost: {{cost}} tokens", "minCost": "Minimum cost: 5 tokens", "cloneCost": "Cloning cost: 900 tokens", "history": {"title": "Speech History", "empty": "No generated recordings", "play": "Play", "download": "Download"}, "errors": {"noText": "Enter text to generate", "noVoice": "Select a voice", "noRecording": "Make a voice recording", "recordingTooShort": "Recording too short (minimum 10 seconds)", "recordingTooLong": "Recording too long (maximum 5 minutes)", "invalidFile": "Unsupported file format", "fileTooLarge": "File too large (maximum 20MB)", "microphoneAccess": "No microphone access", "generation": "Speech generation error", "cloning": "Voice cloning error"}, "success": {"generated": "Speech generated successfully", "cloned": "Voice cloned successfully", "recorded": "Recording completed"}}, "privacy": {"private": "Private", "privateTooltip": "Make this generation private - it won't appear on the Dashboard", "privateTooltipRu": "Сделать эту генерацию приватной - она не появится на Dashboard"}, "blog": {"title": "Uma AI Blog", "subtitle": "Latest news, tips, and insights about AI-powered content generation", "readMore": "Read", "backToBlog": "Back to blog", "shareArticle": "Share", "moreArticles": "More articles", "articleRead": "Article read", "noArticles": "No articles yet", "noArticlesDesc": "Interesting articles about AI content generation will appear here soon", "author": "Author", "publishedOn": "Published on", "readTime": "min read", "tags": "Tags"}}