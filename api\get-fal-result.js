// Simple FAL result endpoint
// GET /api/get-fal-result?requestId=<request_id>&model=<model_id>&userId=<user_id>&prompt=<prompt>&visibility=<visibility>

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "./utils/database.js";

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    console.log(`[GET FAL RESULT] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility } = req.query;
    
    console.log('[GET FAL RESULT] Getting result for:', { requestId, model, userId });
    
    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Check API key
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[GET FAL RESULT] FAL_KEY not found');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Get status first
    const statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    console.log('[GET FAL RESULT] Getting status from:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[GET FAL RESULT] Status error:', errorText);
      return res.status(statusResponse.status).json({ 
        error: 'Status check failed', 
        details: errorText 
      });
    }

    const statusData = await statusResponse.json();
    console.log('[GET FAL RESULT] Status:', statusData.status);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({ 
        error: 'Generation not completed', 
        status: statusData.status 
      });
    }

    // Get result using correct endpoint
    const resultUrl = `https://queue.fal.run/${model}/requests/${requestId}/result`;
    console.log('[GET FAL RESULT] Getting result from:', resultUrl);

    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[GET FAL RESULT] Result error:', errorText);
      return res.status(falResponse.status).json({ 
        error: 'Failed to get result', 
        details: errorText 
      });
    }

    const result = await falResponse.json();
    console.log('[GET FAL RESULT] Got result:', !!result.video);

    // Save to database if we have all parameters
    if (result.video && result.video.url && userId && prompt) {
      try {
        console.log('[GET FAL RESULT] Saving to database...');
        await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility || 'public', requestId);
        console.log('[GET FAL RESULT] Saved successfully');
      } catch (saveError) {
        console.error('[GET FAL RESULT] Save error:', saveError);
        // Don't fail the request, video is still generated
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[GET FAL RESULT] Handler error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}

// Save video to database
async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[GET FAL RESULT] Saving video: ${videoUrl}`);

    // Download video
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[GET FAL RESULT] Downloaded ${videoBuffer.length} bytes`);

    // Generate unique filename
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    // Upload to Supabase
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[GET FAL RESULT] Uploaded to: ${supabaseVideoUrl}`);

    // Generate thumbnail
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[GET FAL RESULT] Thumbnail: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[GET FAL RESULT] Thumbnail error:`, thumbnailError);
    }

    // Determine cost
    let cost = 100;
    if (model.includes('standard')) {
      cost = 75;
    } else if (model.includes('pro')) {
      cost = 120;
    } else if (model.includes('master')) {
      cost = 320;
    }

    // Save to database
    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId,
      duration: 5,
      cost: cost,
      visibility: visibility
    });

    console.log(`[GET FAL RESULT] Saved with ID: ${savedGeneration.id}`);

    // Start GIF creation in background
    createGifAsync(supabaseVideoUrl, savedGeneration.id, userId).catch(error => {
      console.error(`[GET FAL RESULT] GIF error:`, error);
    });

  } catch (error) {
    console.error(`[GET FAL RESULT] Database error:`, error);
    throw error;
  }
}

// Create GIF asynchronously
async function createGifAsync(videoUrl, generationId, userId) {
  try {
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/video/create-gif`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ videoUrl, generationId, userId })
    });

    if (response.ok) {
      console.log(`[GET FAL RESULT] GIF creation started for ${generationId}`);
    } else {
      console.error(`[GET FAL RESULT] GIF API failed: ${response.status}`);
    }
  } catch (error) {
    console.error(`[GET FAL RESULT] GIF request error:`, error);
  }
}
