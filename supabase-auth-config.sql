-- Скрипт для проверки и обновления настроек аутентификации в Supabase

-- 1. Проверка текущих настроек аутентификации
SELECT 
  name, 
  value 
FROM auth.config 
WHERE name = 'DISABLE_CONFIRMATIONS' 
   OR name = 'MAILER_AUTOCONFIRM' 
   OR name = 'SMS_AUTOCONFIRM';

-- 2. Отключение требования подтверждения email
UPDATE auth.config 
SET value = 'true' 
WHERE name = 'MAILER_AUTOCONFIRM';

UPDATE auth.config 
SET value = 'true' 
WHERE name = 'DISABLE_CONFIRMATIONS';

-- 3. Автоматическое подтверждение всех существующих пользователей
UPDATE auth.users 
SET email_confirmed_at = COALESCE(email_confirmed_at, NOW())
WHERE email_confirmed_at IS NULL;

-- 4. Проверка обновленных настроек
SELECT 
  name, 
  value 
FROM auth.config 
WHERE name = 'DISABLE_CONFIRMATIONS' 
   OR name = 'MAILER_AUTOCONFIRM' 
   OR name = 'SMS_AUTOCONFIRM';

-- 5. Проверим пользователя по email
DO $$
DECLARE
  user_email TEXT := '<EMAIL>';
  found_user RECORD;
BEGIN
  -- Проверка существования пользователя
  SELECT 
    id, 
    email, 
    email_confirmed_at IS NOT NULL as is_confirmed,
    created_at
  INTO found_user
  FROM auth.users
  WHERE email = user_email;
  
  IF found_user IS NOT NULL THEN
    -- Выводим информацию о пользователе
    RAISE NOTICE 'Пользователь найден: ID = %, Email = %, Подтвержден = %, Создан = %', 
      found_user.id, found_user.email, found_user.is_confirmed, found_user.created_at;
    
    -- Если email не подтвержден, подтверждаем его
    IF NOT found_user.is_confirmed THEN
      UPDATE auth.users
      SET email_confirmed_at = NOW()
      WHERE email = user_email;
      
      RAISE NOTICE 'Email пользователя подтвержден принудительно';
    END IF;
    
    -- Проверяем наличие профиля
    IF EXISTS (SELECT 1 FROM profiles WHERE id = found_user.id) THEN
      RAISE NOTICE 'Профиль пользователя найден';
    ELSE
      -- Создаем профиль
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (found_user.id, user_email, 'User', 10, NOW(), NOW());
      
      RAISE NOTICE 'Создан профиль для пользователя';
    END IF;
  ELSE
    RAISE NOTICE 'Пользователь с email % не найден', user_email;
  END IF;
END $$; 