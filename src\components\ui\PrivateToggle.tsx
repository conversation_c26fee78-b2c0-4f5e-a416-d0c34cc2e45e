import React from 'react';
import { useTranslation } from 'react-i18next';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Lock, Unlock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PrivateToggleProps {
  isPrivate: boolean;
  onToggle: (isPrivate: boolean) => void;
  className?: string;
}

const PrivateToggle: React.FC<PrivateToggleProps> = ({
  isPrivate,
  onToggle,
  className
}) => {
  const { t, i18n } = useTranslation();
  const isRussian = i18n.language === 'ru';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn(
            "flex items-center gap-2",
            className
          )}>
            <div className="flex items-center gap-1.5">
              {isPrivate ? (
                <Lock className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Unlock className="h-4 w-4 text-muted-foreground" />
              )}
              <span className="text-sm font-medium text-foreground">
                {t('privacy.private')}
              </span>
            </div>
            <Switch
              checked={isPrivate}
              onCheckedChange={onToggle}
              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
            />
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <p className="text-sm">
            {isRussian ? t('privacy.privateTooltip') : t('privacy.privateTooltip')}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default PrivateToggle;
