// Google API Configuration
export const GOOGLE_API_KEY = 'AIzaSyAffLOBtmFzEzFWSbXDtnWCsQD0YIz5ESc';

export const API_CONFIG = {
  textGeneration: {
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta/',
    models: {
      geminiPro: 'models/gemini-2.5-pro-preview-05-06:generateContent',
      geminiFlash: 'models/gemini-2.5-flash-preview-05-20:generateContent',
      geminiImage: 'models/gemini-2.0-flash-preview-image-generation:generateContent',
      gemma: 'models/gemma-3-27b-it:generateContent',
    }
  },
  imageGeneration: {
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta/',
    model: 'models/gemini-2.0-flash-preview-image-generation:generateContent',
  },
  video: {
    baseUrl: 'https://umaai.site', // Vercel serverless functions
    endpoints: {
      combine: '/api/video-combine',
      save: '/api/video-save'
    }
  }
};

// Helper function to create API request URLs with API key
export const createApiUrl = (endpoint: string) => {
  return `${endpoint}?key=${GOOGLE_API_KEY}`;
};
