// Serverless endpoint for polling Replicate prediction status
// POST /api/replicate/status { predictionId: "..." }

export default async function handler(req, res) {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  try {
    let body = {};
    try {
      if (req.body && typeof req.body === "object" && Object.keys(req.body).length > 0) {
        body = req.body;
      } else {
        const raw = await new Promise((resolve, reject) => {
          let data = "";
          req.on("data", chunk => { data += chunk; });
          req.on("end", () => resolve(data));
          req.on("error", err => reject(err));
        });
        body = JSON.parse(raw || "{}");
      }
    } catch (e) {
      console.error("Failed to parse request body:", e);
      body = {};
    }

    const { predictionId } = body;
    if (!predictionId) {
      res.status(400).json({ error: "Missing required parameter: predictionId" });
      return;
    }

    const token = process.env.REPLICATE_API_TOKEN;
    console.log(
      "BACKEND: Read REPLICATE_API_TOKEN (status endpoint):",
      token ? `Token found (starts with ${token.substring(0, 5)}, ends with ${token.slice(-5)})` : "TOKEN NOT FOUND!"
    );

    const statusUrl = `https://api.replicate.com/v1/predictions/${predictionId}`;
    const replicateHeaders = {
      "Authorization": `Token ${token}`,
      "Content-Type": "application/json"
    };

    console.log("BACKEND: Replicate status fetch headers:", {
      ...replicateHeaders,
      Authorization: `Token ${token ? token.substring(0, 5) + "..." + token.slice(-5) : "MISSING"}`
    });
    console.log("BACKEND: Replicate status fetch url:", statusUrl);

    const replicateRes = await fetch(statusUrl, {
      method: "GET",
      headers: replicateHeaders
    });

    console.log("BACKEND: Replicate status response status:", replicateRes.status);

    const replicateData = await replicateRes.json();
    console.log("BACKEND: Replicate status response body:", replicateData);

    if (!replicateRes.ok) {
      res.status(replicateRes.status).json({
        error: "Replicate API error",
        details: replicateData
      });
      return;
    }

    res.status(200).json(replicateData);
  } catch (error) {
    let details = error.message;
    if (error.response && error.response.data) {
      details = error.response.data;
    }
    res.status(500).json({
      error: "Replicate API error",
      details
    });
  }
}
