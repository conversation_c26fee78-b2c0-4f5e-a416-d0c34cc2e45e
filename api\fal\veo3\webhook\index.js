// Webhook endpoint for Veo3 model results
// POST /api/fal/veo3/webhook

import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "../../../utils/database.js";

export default async function handler(req, res) {
  // Разрешаем только POST запросы
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('[VEO3 WEBHOOK] Received webhook:', JSON.stringify(req.body, null, 2));

    const { request_id, status, payload, error } = req.body;

    if (!request_id) {
      console.error('[VEO3 WEBHOOK] Missing request_id in webhook');
      return res.status(400).json({ error: 'Missing request_id' });
    }

    // Проверяем статус
    if (status === 'ERROR') {
      console.error('[VEO3 WEBHOOK] Request failed:', error, payload);
      return res.status(200).json({ message: 'Error acknowledged' });
    }

    if (status !== 'OK') {
      console.log('[VEO3 WEBHOOK] Request not completed yet:', status);
      return res.status(200).json({ message: 'Status acknowledged' });
    }

    // Проверяем наличие видео в результате
    if (!payload || !payload.video || !payload.video.url) {
      console.error('[VEO3 WEBHOOK] No video URL in payload');
      return res.status(400).json({ error: 'No video URL in payload' });
    }

    const videoUrl = payload.video.url;
    console.log('[VEO3 WEBHOOK] Video URL:', videoUrl);

    // Получаем контекст запроса из глобального хранилища
    const requestContext = global.veo3RequestContext?.[request_id];

    if (!requestContext) {
      console.error('[VEO3 WEBHOOK] No context found for request:', request_id);
      return res.status(400).json({ error: 'Request context not found' });
    }

    const { model, userId, prompt, visibility, cost, duration } = requestContext;
    const finalCost = cost || 1280;
    const finalDuration = duration || 8;

    console.log('[VEO3 WEBHOOK] Using context:', { model, userId, prompt, visibility, finalCost, finalDuration });

    // Очищаем контекст после использования
    delete global.veo3RequestContext[request_id];

    // Скачиваем видео
    console.log('[VEO3 WEBHOOK] Downloading video...');
    const videoBuffer = await downloadFile(videoUrl);
    
    // Загружаем видео в Supabase Storage
    const videoFileName = `veo3_${Date.now()}_${Math.random().toString(36).substring(7)}.mp4`;
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, videoFileName);
    console.log('[VEO3 WEBHOOK] Video uploaded to Supabase:', supabaseVideoUrl);

    // Генерируем миниатюру
    console.log('[VEO3 WEBHOOK] Generating thumbnail...');
    const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
    const thumbnailFileName = `thumb_${videoFileName.replace('.mp4', '.jpg')}`;
    const thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
    console.log('[VEO3 WEBHOOK] Thumbnail uploaded:', thumbnailUrl);

    // Сохраняем в базу данных
    console.log('[VEO3 WEBHOOK] Saving to database...');
    const savedGeneration = await saveVideoGeneration({
      userId: userId,
      model: model,
      prompt: prompt,
      videoUrl: supabaseVideoUrl,
      thumbnailUrl: thumbnailUrl,
      duration: finalDuration,
      aspectRatio: '16:9',
      cost: finalCost,
      visibility: visibility,
      originalVideoUrl: videoUrl
    });

    console.log('[VEO3 WEBHOOK] Successfully saved generation:', savedGeneration.id);

    return res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      generationId: savedGeneration.id
    });

  } catch (error) {
    console.error('[VEO3 WEBHOOK] Error processing webhook:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}
