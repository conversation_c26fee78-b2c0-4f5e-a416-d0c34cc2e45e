# Исправление сохранения видео генераций

## Проблема
Сейчас видео сохраняется только с прямой ссылкой из Replicate API, которая временная и со временем становится недоступной. Также не сохраняются параметры генерации (duration, quality, aspect_ratio, motion_mode, cost).

## Решение
Реализовано скачивание видео с Replicate и загрузка в Supabase Storage для постоянного хранения, а также сохранение всех параметров генерации.

## Внесенные изменения

### 1. Обновлена функция сохранения видео в `api/utils/database.js`:
- Добавлена функция `uploadVideoToSupabase()` для загрузки видео в Supabase Storage
- Добавлена функция `uploadThumbnailToSupabase()` для загрузки превью видео
- Добавлена функция `generateThumbnailFromVideo()` для генерации превью из видео с помощью FFmpeg
- Добавлена функция `downloadFile()` для скачивания файлов по URL
- Обновлена функция `saveVideoGeneration()` для:
  - Скачивания видео с Replicate URL
  - Загрузки видео в Supabase Storage (bucket: `generations/videos/`)
  - Генерации и загрузки превью видео (bucket: `generations/thumbnails/`)
  - Сохранения всех параметров генерации включая thumbnail_url

### 2. Обновлен webhook в `api/replicate/webhook.js`:
- Передача всех параметров генерации в функцию `saveVideoGeneration()`
- Включены: duration, quality, aspect_ratio, motion_mode, cost

### 3. Создан SQL скрипт для добавления столбцов

## Инструкция по применению

### Шаг 1: Выполните SQL скрипт
Откройте панель управления Supabase → SQL Editor и выполните содержимое файла `supabase-add-video-columns.sql`:

```sql
-- Добавляет отсутствующие столбцы в таблицу video_generations
-- duration, quality, aspect_ratio, motion_mode, cost
```

### Шаг 2: Проверьте bucket в Supabase Storage
Убедитесь, что в Supabase Storage существует bucket `generations`:
1. Откройте Supabase → Storage
2. Если bucket `generations` не существует, создайте его
3. Настройте публичный доступ для bucket'а

### Шаг 3: Деплой изменений
Задеплойте обновленный код на Vercel.

## Результат после применения

### Что изменится:
1. **Постоянное хранение видео**: Видео будут скачиваться с Replicate и загружаться в Supabase Storage
2. **Постоянные URL**: Вместо временных ссылок Replicate будут использоваться постоянные URL Supabase
3. **Полные данные**: Все параметры генерации будут сохраняться в БД
4. **Структура URL**: `https://[project].supabase.co/storage/v1/object/public/generations/videos/[uuid]/[timestamp]-video.mp4`

### Пример сохраненной записи:
```json
{
  "id": "6df668a1-9e81-419f-9d29-ff9ebe2fe3c5",
  "user_id": "ee5b0e39-383d-4ee9-aebc-b333ae733781",
  "prompt": "Wasteland style concept art...",
  "video_url": "https://ehamdaltpbuicmggxhbn.supabase.co/storage/v1/object/public/generations/videos/6df668a1-9e81-419f-9d29-ff9ebe2fe3c5/1748020161304-video.mp4",
  "thumbnail_url": "https://ehamdaltpbuicmggxhbn.supabase.co/storage/v1/object/public/generations/thumbnails/6df668a1-9e81-419f-9d29-ff9ebe2fe3c5/1748020161304-thumbnail.jpg",
  "model": "pixverse/pixverse-v4.5",
  "duration": 5,
  "quality": "540p",
  "aspect_ratio": "16:9",
  "motion_mode": "normal",
  "cost": 90,
  "replicate_prediction_id": "6a9cyrtntsrm80cq12pa977aw0",
  "created_at": "2025-05-26T00:20:25.207Z"
}
```

## Назначение thumbnail_url

Столбец `thumbnail_url` содержит URL превью (миниатюры) видео и нужен для:

1. **Улучшения UX**: Показ превью видео в интерфейсе вместо черного экрана
2. **Быстрая загрузка**: Маленькие изображения загружаются быстрее полного видео
3. **Предпросмотр**: Пользователь видит содержимое видео до его воспроизведения
4. **Галерея**: Красивое отображение списка видео в виде сетки с превью

Превью генерируется автоматически из первой секунды видео с разрешением 320x240 пикселей и сохраняется в формате JPEG.

## Логирование
Добавлено подробное логирование процесса:
- Скачивание видео с Replicate
- Загрузка в Supabase Storage
- Сохранение в БД

Логи будут показывать прогресс и помогут в отладке при необходимости.