-- Скрипт для исправления таблицы profiles в Supabase
-- Добавляем отсутствующую колонку updated_at, если она не существует

-- Проверяем существование колонки updated_at в таблице profiles
DO $$ 
BEGIN
  -- Проверяем существует ли колонка updated_at в таблице profiles
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
      AND column_name = 'updated_at'
  ) THEN
    -- Если колонка не существует, добавляем её
    EXECUTE 'ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()';
    RAISE NOTICE 'Колонка updated_at добавлена в таблицу profiles';
  ELSE
    RAISE NOTICE 'Колонка updated_at уже существует в таблице profiles';
  END IF;
END $$;

-- Обновляем все существующие записи, у которых updated_at равен NULL
UPDATE profiles
SET updated_at = created_at
WHERE updated_at IS NULL;

-- Добавляем триггер для автоматического обновления updated_at при изменении записи
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Добавляем триггер, если он еще не существует
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_trigger 
    WHERE tgname = 'set_updated_at' 
    AND tgrelid = 'profiles'::regclass
  ) THEN
    CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
    
    RAISE NOTICE 'Триггер set_updated_at добавлен к таблице profiles';
  ELSE
    RAISE NOTICE 'Триггер set_updated_at уже существует для таблицы profiles';
  END IF;
END $$;

-- Обновляем типы в Database.Tables.profiles
COMMENT ON TABLE profiles IS 'Таблица с профилями пользователей включает обязательное поле updated_at'; 