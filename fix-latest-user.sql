-- Скрипт для исправления последнего созданного пользователя

-- 1. Найти последнего созданного пользователя и информацию о нем
SELECT 
  id, 
  email, 
  email_confirmed_at,
  created_at,
  last_sign_in_at,
  raw_user_meta_data
FROM auth.users
ORDER BY created_at DESC
LIMIT 1;

-- 2. Исправить последнего пользователя (подтвердить email и установить новый пароль)
DO $$
DECLARE
  latest_user_id UUID;
  latest_user_email TEXT;
  new_password TEXT := 'Password123!'; -- Простой запоминающийся пароль
BEGIN
  -- Получаем ID последнего созданного пользователя
  SELECT id, email INTO latest_user_id, latest_user_email
  FROM auth.users
  ORDER BY created_at DESC
  LIMIT 1;
  
  IF latest_user_id IS NOT NULL THEN
    RAISE NOTICE 'Найден последний созданный пользователь: ID = %, Email = %', latest_user_id, latest_user_email;
    
    -- Подтвердить email пользователя
    UPDATE auth.users
    SET 
      email_confirmed_at = NOW(),
      -- Обновляем пароль с использованием crypt() функции
      encrypted_password = crypt(new_password, gen_salt('bf'))
    WHERE id = latest_user_id;
    
    RAISE NOTICE 'Email подтвержден и установлен новый пароль: %', new_password;
    
    -- Проверяем наличие профиля
    IF EXISTS (SELECT 1 FROM profiles WHERE id = latest_user_id) THEN
      RAISE NOTICE 'Профиль пользователя найден';
    ELSE
      -- Создаем профиль
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (latest_user_id, latest_user_email, 'User', 10, NOW(), NOW());
      
      RAISE NOTICE 'Создан профиль для пользователя: %', latest_user_id;
    END IF;
    
    -- Проверяем наличие записи в auth.identities
    IF NOT EXISTS (SELECT 1 FROM auth.identities WHERE user_id = latest_user_id) THEN
      -- Создаем запись в identities
      INSERT INTO auth.identities (
        id,
        user_id,
        identity_data,
        provider,
        last_sign_in_at,
        created_at,
        updated_at
      ) VALUES (
        latest_user_id,
        latest_user_id,
        jsonb_build_object('sub', latest_user_id, 'email', latest_user_email),
        'email',
        NOW(),
        NOW(),
        NOW()
      );
      
      RAISE NOTICE 'Создана запись в auth.identities';
    ELSE
      RAISE NOTICE 'Запись в auth.identities уже существует';
    END IF;
  ELSE
    RAISE NOTICE 'Не найдено ни одного пользователя';
  END IF;
END $$;

-- 3. Исправление для конкретного пользователя (если известен ID)
DO $$
DECLARE
  specific_user_id UUID := 'c42b0c42-d751-4b3c-938f-73328b07f26c'; -- ID из логов
  specific_user_email TEXT;
  new_password TEXT := 'Password123!'; -- Простой запоминающийся пароль
BEGIN
  -- Получаем email пользователя
  SELECT email INTO specific_user_email
  FROM auth.users
  WHERE id = specific_user_id;
  
  IF specific_user_email IS NOT NULL THEN
    RAISE NOTICE 'Найден указанный пользователь: ID = %, Email = %', specific_user_id, specific_user_email;
    
    -- Подтвердить email пользователя и обновить пароль
    UPDATE auth.users
    SET 
      email_confirmed_at = NOW(),
      encrypted_password = crypt(new_password, gen_salt('bf'))
    WHERE id = specific_user_id;
    
    RAISE NOTICE 'Email подтвержден и установлен новый пароль: %', new_password;
    
    -- Проверяем наличие профиля
    IF EXISTS (SELECT 1 FROM profiles WHERE id = specific_user_id) THEN
      RAISE NOTICE 'Профиль пользователя найден';
    ELSE
      -- Создаем профиль
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (specific_user_id, specific_user_email, 'User', 10, NOW(), NOW());
      
      RAISE NOTICE 'Создан профиль для пользователя: %', specific_user_id;
    END IF;
  ELSE
    RAISE NOTICE 'Указанный пользователь (ID: %) не найден', specific_user_id;
  END IF;
END $$;

-- 4. Показать результаты
SELECT 
  u.id, 
  u.email, 
  u.email_confirmed_at IS NOT NULL as is_confirmed,
  u.created_at,
  u.last_sign_in_at,
  EXISTS(SELECT 1 FROM profiles p WHERE p.id = u.id) as has_profile,
  EXISTS(SELECT 1 FROM auth.identities i WHERE i.user_id = u.id) as has_identity
FROM auth.users u
ORDER BY u.created_at DESC
LIMIT 5; 