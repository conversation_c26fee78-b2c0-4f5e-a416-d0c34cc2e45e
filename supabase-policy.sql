-- Сначала настроим RLS политики для таблицы profiles

-- Включаем RLS для таблицы profiles, если еще не включено
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Удаляем существующие политики, если они есть
DROP POLICY IF EXISTS "Пользователи могут создавать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут просматривать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут обновлять свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может просматривать все профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может создавать профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может обновлять профили" ON public.profiles;

-- Создаем политики для пользователей
-- 1. Политика для создания профиля (только свой профиль)
CREATE POLICY "Пользователи могут создавать свои профили"
ON public.profiles FOR INSERT TO authenticated
WITH CHECK (auth.uid() = id);

-- 2. Политика для чтения профиля (только свой профиль)
CREATE POLICY "Пользователи могут просматривать свои профили"
ON public.profiles FOR SELECT TO authenticated
USING (auth.uid() = id);

-- 3. Политика для обновления профиля (только свой профиль)
CREATE POLICY "Пользователи могут обновлять свои профили"
ON public.profiles FOR UPDATE TO authenticated
USING (auth.uid() = id);

-- Создаем политики для сервисной роли (для админских операций)
-- 1. Сервисная роль может видеть все профили
CREATE POLICY "Сервис может просматривать все профили"
ON public.profiles FOR SELECT TO service_role
USING (true);

-- 2. Сервисная роль может создавать любые профили
CREATE POLICY "Сервис может создавать профили"
ON public.profiles FOR INSERT TO service_role
WITH CHECK (true);

-- 3. Сервисная роль может обновлять любые профили
CREATE POLICY "Сервис может обновлять профили"
ON public.profiles FOR UPDATE TO service_role
USING (true);

-- Установка RLS для таблицы image_generations
ALTER TABLE image_generations ENABLE ROW LEVEL SECURITY;

-- Удаление существующих политик (если есть)
DROP POLICY IF EXISTS "Пользователи могут создавать свои генерации" ON image_generations;
DROP POLICY IF EXISTS "Пользователи могут просматривать свои генерации" ON image_generations;
DROP POLICY IF EXISTS "Service role может просматривать генерации" ON image_generations;
DROP POLICY IF EXISTS "Service role может создавать генерации" ON image_generations;

-- Политика для создания генераций
CREATE POLICY "Пользователи могут создавать свои генерации" 
ON image_generations FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid() = user_id);

-- Политика для просмотра генераций
CREATE POLICY "Пользователи могут просматривать свои генерации" 
ON image_generations FOR SELECT 
TO authenticated 
USING (auth.uid() = user_id);

-- Политики для service_role
CREATE POLICY "Service role может просматривать генерации" 
ON image_generations FOR SELECT 
TO service_role 
USING (true);

CREATE POLICY "Service role может создавать генерации" 
ON image_generations FOR INSERT 
TO service_role 
WITH CHECK (true);

-- Установка RLS для таблицы user_credits
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;

-- Удаление существующих политик (если есть)
DROP POLICY IF EXISTS "Пользователи могут просматривать свои кредиты" ON user_credits;
DROP POLICY IF EXISTS "Service role может просматривать кредиты" ON user_credits;
DROP POLICY IF EXISTS "Service role может создавать кредиты" ON user_credits;

-- Политика для просмотра кредитов
CREATE POLICY "Пользователи могут просматривать свои кредиты" 
ON user_credits FOR SELECT 
TO authenticated 
USING (auth.uid() = user_id);

-- Политики для service_role
CREATE POLICY "Service role может просматривать кредиты" 
ON user_credits FOR SELECT 
TO service_role 
USING (true);

CREATE POLICY "Service role может создавать кредиты" 
ON user_credits FOR INSERT 
TO service_role 
WITH CHECK (true);

-- Вывод информации о применении политик
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Теперь создаем функцию и триггер для автоматического создания профиля при регистрации

-- Удаляем существующую функцию и триггер, если они есть
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Проверка и добавление колонки updated_at в таблицу profiles
DO $$ 
BEGIN
  -- Проверяем существует ли колонка updated_at в таблице profiles
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
      AND column_name = 'updated_at'
  ) THEN
    -- Если колонка не существует, добавляем её
    EXECUTE 'ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()';
    RAISE NOTICE 'Колонка updated_at добавлена в таблицу profiles';
  ELSE
    RAISE NOTICE 'Колонка updated_at уже существует в таблице profiles';
  END IF;
END $$;

-- Создаем функцию для проверки существования колонки в таблице
CREATE OR REPLACE FUNCTION check_column_exists(table_name text, column_name text)
RETURNS BOOLEAN AS $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = check_column_exists.table_name 
      AND column_name = check_column_exists.column_name
  ) INTO column_exists;
  
  RETURN column_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Создаем функцию для выполнения произвольного SQL запроса
CREATE OR REPLACE FUNCTION execute_sql(sql text)
RETURNS VOID AS $$
BEGIN
  EXECUTE sql;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Обновляем триггер для автоматического создания профиля пользователя с колонкой updated_at
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name, credits, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name', ''),
    10,
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Проверяем существование триггера и создаем если не существует
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created'
  ) THEN
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW
      EXECUTE FUNCTION public.handle_new_user();
    RAISE NOTICE 'Триггер on_auth_user_created создан для автоматического создания профилей';
  ELSE
    RAISE NOTICE 'Триггер on_auth_user_created уже существует';
  END IF;
END $$; 