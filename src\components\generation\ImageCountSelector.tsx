import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ImageCountSelectorProps {
  count: number;
  onChange: (count: number) => void;
}

const ImageCountSelector = ({ count, onChange }: ImageCountSelectorProps) => {
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm text-foreground/70">Image count:</span>
      <Select value={count.toString()} onValueChange={(value) => onChange(parseInt(value))}>
        <SelectTrigger className="w-[100px]">
          <SelectValue placeholder="Select count" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">1 image</SelectItem>
          <SelectItem value="2">2 images</SelectItem>
          <SelectItem value="4">4 images</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default ImageCountSelector;