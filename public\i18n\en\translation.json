{"common": {"appName": "<PERSON><PERSON>", "loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "submit": "Submit", "close": "Close", "copied": "Copied to clipboard", "notFound": "Not found", "errorOccurred": "An error occurred"}, "nav": {"home": "Home", "dashboard": "Dashboard", "profile": "Profile", "history": "History", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "signUp": "Sign Up", "videoGen": "Video Generation"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "name": "Name", "forgotPassword": "Forgot password?", "resetPassword": "Reset Password", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "signIn": "Sign In", "signUp": "Sign Up", "signInWithGoogle": "Sign in with Google", "signInSuccess": "Successfully signed in!", "signUpSuccess": "Successfully signed up!", "signInError": "Failed to sign in", "signUpError": "Failed to sign up", "signOutSuccess": "Successfully signed out", "signOutError": "Failed to sign out", "resetEmailSent": "Password reset email sent!", "resetPasswordError": "Failed to send reset email", "confirmEmail": "Please check your email to confirm your account", "invalidCredentials": "Invalid email or password", "loginFailed": "<PERSON><PERSON> failed", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 6 characters", "generalError": "An error occurred. Please try again", "emailNotConfirmed": "<PERSON><PERSON> not confirmed", "checkEmailForConfirmation": "Please check your email and click the confirmation link", "userAlreadyExists": "User with this email already exists", "tryLoginInstead": "Try logging in or resetting your password", "userExistsOrInvalidPassword": "User exists but either email is not confirmed or password is incorrect", "registrationSuccessful": "Registration successful!", "emailAlreadyRegistered": "Email is already registered", "userBanned": "User is banned", "userLocked": "Account is temporarily locked due to too many login attempts", "passwordNotMatch": "Passwords do not match", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "namePlaceholder": "Enter your name (optional)", "orContinueWith": "or continue with", "sendResetLink": "Send reset link", "resetPasswordInstructions": "Enter your email and we'll send a link to reset your password", "backToLogin": "Back to login", "profileCreationFailed": "Failed to create user profile", "refreshingProfile": "Refreshing profile information", "unexpectedError": "An unexpected error occurred", "verifyingSession": "Verifying your session"}, "dashboard": {"welcome": "Welcome to <PERSON><PERSON>", "subtitle": "AI-powered content generation platform", "recentGenerations": "Recent Generations", "newGeneration": "New Generation", "noGenerations": "No generations yet. Create your first one!", "viewAll": "View All", "createNew": "Create New", "allTab": "All", "tabAll": "All", "tabImage": "Images", "tabVideo": "Videos", "imageTab": "Images", "videoTab": "Videos", "loadingGenerations": "Loading your creations", "noHistory": "Generation history is empty", "noExamples": "No examples yet.", "noImageExamples": "No image examples yet.", "noVideoExamples": "No video examples yet.", "history": "History", "historyDescription": "Here you can view your generation history.", "modal": {"copyPrompt": "Copy Prompt", "downloadImage": "Download", "prompt": "Prompt", "promptCopied": "Prompt copied to clipboard!", "promptCopyError": "Failed to copy prompt.", "downloadStarted": "Image download started!", "downloadError": "Failed to download image. The link might be broken or expired.", "generatedImageAlt": "Generated image", "seeLess": "See Less", "seeMore": "See More", "imageTitlePlaceholder": "Generated Image", "imageDescriptionPlaceholder": "Detailed view of a generated image and its options.", "usePrompt": "Use prompt"}}, "sidebar": {"home": "Home", "dashboard": "Dashboard", "history": "History", "text": "Text", "image": "Image", "video": "Video"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "updateProfile": "Update Profile", "credits": "Credits", "creditsBalance": "Credits Balance", "addCredits": "Add Credits", "accountSettings": "Account <PERSON><PERSON>", "changePassword": "Change Password", "deleteAccount": "Delete Account", "profileUpdated": "Profile updated successfully", "refreshBalance": "Refresh Balance", "loadingProfile": "Loading profile data", "tokenAdded": "Tokens successfully added", "failedToAddTokens": "Failed to add tokens"}, "credits": {"title": "Buy Credits", "description": "Choose the plan that works best for you", "action": "Buy Now", "creditsLeft": "credits left", "buyMore": "Buy more credits", "unlimitedCredits": "Unlimited credits", "plans": {"basic": {"name": "Basic"}, "standard": {"name": "Standard"}, "premium": {"name": "Premium"}}}, "navigation": {"buyCredits": "Buy Credits", "videoGen": "Video Generation"}, "textGen": {"pageTitle": "Text Generation", "initialGreeting": "Welcome! Start generating text with AI.", "inputPlaceholder": "Type your prompt here..."}, "generation": {"new": "New Generation", "image": "Image", "video": "Video", "text": "Text", "prompt": "Prompt", "style": "Style", "model": "Model", "generate": "Generate", "generating": "Generating...", "success": "Generation successful!", "error": "Generation failed", "history": "Generation History", "noHistory": "No generation history yet", "downloadImage": "Download Image", "copyPrompt": "Copy Prompt", "regenerate": "Regenerate", "share": "Share", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this generation?", "credits": "Credits", "creditsRequired": "Credits required", "insufficientCredits": "Insufficient credits", "aspectRatio": "Aspect Ratio", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "noMoreHistory": "No more items in history", "loadingMore": "Loading more items", "copyImage": "Copy Image", "generatedOn": "Generated on", "modelUsed": "Model used"}, "imageGen": {"image": "Image", "images": "Images", "image_one": "image", "image_other": "images", "emptyState": "No images generated yet.", "settings": {"selectStyle": "Select style", "selectModel": "Select model"}, "placeholders": {"imagePrompt": "Describe your image..."}, "actions": {"generating": "Generating", "download": "Download"}, "sections": {"generatedImages": "Generated Images"}, "models": {"together": "Together AI", "togetherDesc": "Fast image generation with fine details and artistic styles", "ideogram": "Ideogram 2", "ideogramDesc": "Universal model, supports img to img. Good at handling text", "hidream": "Hi-Dream", "hidreamDesc": "Advanced photorealistic model for premium quality", "ideogram3": "Ideogram 3", "ideogram3Desc": "Next-generation model with improved quality and detail, supports img to img. Excellent with text", "fluxpro": "Flux Pro", "fluxproDesc": "Model specialized for photo editing and enhancement", "minimaxDesc": "Highly detailed, hyperrealistic model", "imagen4Desc": "Advanced model for creating highly detailed images", "bagelDesc": "Versatile model with editing support and high quality. Perfect for image editing", "fluxKontextPro": "Flux Kontext Pro", "fluxKontextProDesc": "State-of-the-art image editing model with high-quality outputs and excellent prompt following", "fluxKontextMax": "Flux Kontext Max", "fluxKontextMaxDesc": "Premium model with maximum performance and improved typography generation"}, "steps": {"starting": "Initializing...", "creating": "Creating image...", "finalizing": "Processing results...", "saving": "Saving..."}, "success": {"generation": "Image successfully created!"}, "error": {"generation": "Failed to generate image", "noPrompt": "Please enter an image description", "downloadFailed": "Failed to download image", "promptTooLong": "Description is too long, maximum 500 characters", "invalidService": "Error: selected model is not supported or request parameters are invalid", "insufficientCredits": "Insufficient credits for this generation"}, "errors": {"promptTooLong": "Description is too long, maximum 500 characters"}}, "features": {"textGenTitle": "Text Generation", "textGenDescription": "Generate high-quality text using advanced AI models.", "imageGenTitle": "Image Generation", "imageGenDescription": "Create stunning images from your prompts.", "videoGenTitle": "Video Generation", "videoGenDescription": "Produce creative videos with AI.", "sectionTitle": "Features", "sectionSubtitle": "What you can do"}, "showcase": {"item1Title": "AI Art", "item1Prompt": "A futuristic cityscape at sunset", "item2Title": "Photo Realism", "item2Prompt": "A portrait of a young woman, photorealistic", "item3Title": "Creative Design", "item3Prompt": "A logo for a tech startup", "sectionTitle": "Showcase", "sectionSubtitle": "See what <PERSON><PERSON> can create"}, "pricing": {"freePlan": {"title": "Free", "price": "0 ₽", "description": "Get started with basic features for free", "features": ["Basic AI features", "Limited generations"], "button": "Start for Free"}, "testPlan": {"title": "Test Plan", "price": "1 ₽", "description": "Test plan for development only", "features": ["1000 credits"], "button": "Test"}, "basicPlan": {"title": "Basic", "price": "300 ₽", "description": "Starter credits package", "features": ["600 credits"], "button": "Choose Basic"}, "standardPlan": {"title": "Standard", "price": "750 ₽", "description": "Most popular choice", "features": ["1500 credits"], "button": "Choose Standard", "mostPopular": "Most Popular"}, "proPlan": {"title": "Professional", "price": "2000 ₽", "description": "For active users", "features": ["4000 credits"], "button": "<PERSON><PERSON>", "mostPopular": "Most Popular"}, "enterprisePlan": {"title": "Enterprise", "price": "Custom", "description": "For large teams and organizations. Custom limits and support.", "features": ["Unlimited generations", "Priority support", "Team management"], "button": "Contact Sales"}, "sectionTitle": "Pricing", "sectionSubtitle": "Choose your plan", "footer": "All plans include access to Uma Ai features"}, "history": {"loading": "Loading history...", "loadingMore": "Loading...", "loadMore": "Load More", "noMoreGenerations": "No more generations.", "noGenerations": "You have no generations yet.", "tryCreating": "Try creating something!", "downloadImage": "Download Image"}, "buyCredits": {"buttonText": "Buy Credits", "title": "Buy Credits", "description": "Choose a suitable plan to top up your credits", "credits": "credits", "testPlan": "Test Plan", "test": "Test", "pleaseSelectPlan": "Please select a plan", "creatingPayment": "Creating payment...", "redirecting": "Redirecting to payment page...", "loading": "Loading...", "proceedToPayment": "Proceed to Payment", "errors": {"invalidPaymentResponse": "Invalid payment API response", "noConfirmationUrl": "No confirmation URL received", "paymentCreation": "Error creating payment", "unknown": "Unknown error"}}, "language": {"switch": "Switch language", "select": "Select language"}, "header": {"toggleTheme": "Toggle theme", "loginButton": "<PERSON><PERSON>", "signUpButton": "Sign Up", "logout": "Logout", "imageGen": "Image Generation"}, "landing": {"welcomeTo": "", "appName": "", "introducing": "Introducing", "subtitle": "AI-powered content generation platform", "getStarted": "Get Started", "viewDemos": "View Demos", "ctaTitle": "Ready to try <PERSON><PERSON>?", "ctaSubtitle": "Sign up and start generating content with AI!"}, "login": {"title": "Sign in to your account", "subtitle": "Enter your credentials to access <PERSON><PERSON>", "footer": "Don't have an account? Register now."}, "home": {"title": "Home", "subtitle": "Generate with AI"}, "chats": "Chats", "videoGen": {"pixverse": {"modelName": "Pixverse v4.5", "modelDescription": "Advanced model with unique effects and styles", "quality": "Quality", "motionMode": "Motion Mode", "style": "Style", "effect": "Effect", "seed": "Seed", "negativePrompt": "Negative Prompt", "settings": "Pixverse Settings", "qualityDescription": "Video resolution", "motionModeDescription": "Video motion mode", "seedDescription": "Random seed for reproducible generation", "negativePromptDescription": "What to avoid in the video", "styles": {"None": "None", "anime": "Anime", "3d_animation": "3D Animation", "clay": "<PERSON>", "cyberpunk": "Cyberpunk", "comic": "Comic"}, "effects": {"None": "None", "Let_s_YMCA_": "Let's YMCA!", "Subject_3_Fever": "Subject 3 Fever", "Ghibli_Live_": "Ghibli Live!", "Suit_Swagger": "Suit Swagger", "Muscle_Surge": "<PERSON><PERSON><PERSON>ge", "360__Microwave": "360° Microwave", "Warmth_of_Jesus": "Warmth of Jesus", "Emergency_Beat": "Emergency Beat", "Anything__Robot": "Anything, Robot", "Kungfu_Club": "Kungfu Club", "Mint_in_Box": "Mint in Box", "Retro_Anime_Pop": "Retro Anime Pop", "Vogue_Walk": "Vogue Walk", "Mega_Dive": "Mega Dive", "Evil_Trigger": "Evil Trigger"}, "motionModes": {"normal": "Normal", "smooth": "Smooth"}}, "kling": {"cfgScaleLabel": "Prompt deviation allowance"}}}