import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { Navigate } from 'react-router-dom';
import { Plus, Edit, Trash2, Eye, EyeOff, Save, X } from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import SEOHead from '@/components/SEOHead';

// Типы для статей блога
interface BlogPost {
  id: string;
  title: string;
  titleEn: string;
  excerpt: string;
  excerptEn: string;
  content: string;
  contentEn: string;
  author: string;
  date: string;
  readTime: number;
  image?: string;
  tags: string[];
  published: boolean;
}

// Проверка на админа (замените на вашу логику)
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>']; // Админские email'ы

const BlogAdmin = () => {
  const { i18n } = useTranslation();
  const { user } = useAuth();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(true);

  // Проверка прав доступа
  const isAdmin = user?.email && ADMIN_EMAILS.includes(user.email);

  useEffect(() => {
    if (isAdmin) {
      loadPosts();
    }
  }, [isAdmin]);

  const loadPosts = async () => {
    // Здесь будет загрузка из базы данных
    // Пока используем localStorage для демонстрации
    try {
      const savedPosts = localStorage.getItem('blog_posts');
      if (savedPosts) {
        setPosts(JSON.parse(savedPosts));
      } else {
        // Если нет сохраненных постов, инициализируем демо-статьями
        const demoPosts = [
          {
            id: '1',
            title: 'Как создать максимально крутое видео с помощью Veo3',
            titleEn: 'How to Create Amazing Videos with Veo3',
            excerpt: 'Полное руководство по использованию Veo3 - самой продвинутой модели генерации видео от Google. Узнайте секреты создания профессиональных видео с аудио.',
            excerptEn: 'Complete guide to using Veo3 - Google\'s most advanced video generation model. Learn the secrets of creating professional videos with audio.',
            content: '<h2>Демо-статья</h2><p>Это демонстрационная статья. Отредактируйте её в админке блога.</p>',
            contentEn: '<h2>Demo Article</h2><p>This is a demo article. Edit it in the blog admin panel.</p>',
            author: 'Uma AI Team',
            date: '2024-12-15',
            readTime: 8,
            image: '/veo3-banner.webm',
            tags: ['Veo3', 'Видео', 'Руководство', 'Google'],
            published: true
          }
        ];
        localStorage.setItem('blog_posts', JSON.stringify(demoPosts));
        setPosts(demoPosts);
      }
    } catch (error) {
      console.error('Error loading posts:', error);
    }
    setLoading(false);
  };

  const savePosts = (newPosts: BlogPost[]) => {
    // Здесь будет сохранение в базу данных
    // Пока используем localStorage для демонстрации
    localStorage.setItem('blog_posts', JSON.stringify(newPosts));
    setPosts(newPosts);
  };

  const createNewPost = () => {
    const newPost: BlogPost = {
      id: Date.now().toString(),
      title: '',
      titleEn: '',
      excerpt: '',
      excerptEn: '',
      content: '',
      contentEn: '',
      author: 'Uma AI Team',
      date: new Date().toISOString().split('T')[0],
      readTime: 5,
      image: '',
      tags: [],
      published: false
    };
    setEditingPost(newPost);
    setIsCreating(true);
  };

  const savePost = () => {
    if (!editingPost) return;

    if (isCreating) {
      const newPosts = [...posts, editingPost];
      savePosts(newPosts);
    } else {
      const newPosts = posts.map(p => p.id === editingPost.id ? editingPost : p);
      savePosts(newPosts);
    }

    setEditingPost(null);
    setIsCreating(false);
  };

  const deletePost = (id: string) => {
    if (confirm('Удалить статью?')) {
      const newPosts = posts.filter(p => p.id !== id);
      savePosts(newPosts);
    }
  };

  const togglePublished = (id: string) => {
    const newPosts = posts.map(p => 
      p.id === id ? { ...p, published: !p.published } : p
    );
    savePosts(newPosts);
  };

  const updateEditingPost = (field: keyof BlogPost, value: any) => {
    if (!editingPost) return;
    setEditingPost({ ...editingPost, [field]: value });
  };

  const updateTags = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
    updateEditingPost('tags', tags);
  };

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <>
      <SEOHead 
        title="Администрирование блога - Uma AI"
        description="Панель управления блогом Uma AI"
      />
      
      <div className="min-h-screen bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-black dark:via-gray-900/50 dark:to-gray-800/30">
        <Header />
        
        <div className="pt-24 pb-20 px-6">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gradient mb-2">
                  Администрирование блога
                </h1>
                <p className="text-foreground/70 mb-2">
                  Управление статьями блога Uma AI
                </p>
                <div className="text-sm text-foreground/60 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                  💡 <strong>Совет:</strong> Используйте изображения из папки /public/ (например: /veo3.webm, /FluxKontextMaxLight.webp).
                  Контент пишите в HTML формате. Статьи сохраняются в localStorage.
                </div>
              </div>
              
              <Button onClick={createNewPost} className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Новая статья
              </Button>
            </div>

            {/* Форма редактирования */}
            {editingPost && (
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    {isCreating ? 'Создание статьи' : 'Редактирование статьи'}
                    <div className="flex gap-2">
                      <Button onClick={savePost} size="sm">
                        <Save className="w-4 h-4 mr-2" />
                        Сохранить
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          setEditingPost(null);
                          setIsCreating(false);
                        }}
                      >
                        <X className="w-4 h-4 mr-2" />
                        Отмена
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Заголовок (RU)</label>
                      <Input
                        value={editingPost.title}
                        onChange={(e) => updateEditingPost('title', e.target.value)}
                        placeholder="Как создать крутое видео с помощью Veo3"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Заголовок (EN)</label>
                      <Input
                        value={editingPost.titleEn}
                        onChange={(e) => updateEditingPost('titleEn', e.target.value)}
                        placeholder="How to Create Amazing Videos with Veo3"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Краткое описание (RU)</label>
                      <Textarea
                        value={editingPost.excerpt}
                        onChange={(e) => updateEditingPost('excerpt', e.target.value)}
                        placeholder="Полное руководство по использованию Veo3 - самой продвинутой модели генерации видео от Google..."
                        rows={3}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Краткое описание (EN)</label>
                      <Textarea
                        value={editingPost.excerptEn}
                        onChange={(e) => updateEditingPost('excerptEn', e.target.value)}
                        placeholder="Brief article description"
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Контент (RU)</label>
                      <Textarea
                        value={editingPost.content}
                        onChange={(e) => updateEditingPost('content', e.target.value)}
                        placeholder="<h2>Заголовок</h2><p>Текст статьи...</p><ul><li>Пункт списка</li></ul>"
                        rows={10}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Контент (EN)</label>
                      <Textarea
                        value={editingPost.contentEn}
                        onChange={(e) => updateEditingPost('contentEn', e.target.value)}
                        placeholder="HTML article content"
                        rows={10}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Автор</label>
                      <Input
                        value={editingPost.author}
                        onChange={(e) => updateEditingPost('author', e.target.value)}
                        placeholder="Автор статьи"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Время чтения (мин)</label>
                      <Input
                        type="number"
                        value={editingPost.readTime}
                        onChange={(e) => updateEditingPost('readTime', parseInt(e.target.value) || 5)}
                        placeholder="5"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Изображение (URL)</label>
                      <Input
                        value={editingPost.image || ''}
                        onChange={(e) => updateEditingPost('image', e.target.value)}
                        placeholder="/veo3-banner.webm, /umaicon.png, /FluxKontextMaxLight.webp"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Теги (через запятую)</label>
                    <Input
                      value={editingPost.tags.join(', ')}
                      onChange={(e) => updateTags(e.target.value)}
                      placeholder="Veo3, Видео, Руководство, Google"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Список статей */}
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-8">Загрузка...</div>
              ) : posts.length === 0 ? (
                <div className="text-center py-8 text-foreground/60">
                  Статей пока нет. Создайте первую статью!
                </div>
              ) : (
                posts.map((post) => (
                  <Card key={post.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">
                              {i18n.language === 'ru' ? post.title : post.titleEn}
                            </h3>
                            <Badge variant={post.published ? "default" : "secondary"}>
                              {post.published ? 'Опубликовано' : 'Черновик'}
                            </Badge>
                          </div>
                          <p className="text-foreground/70 mb-2">
                            {i18n.language === 'ru' ? post.excerpt : post.excerptEn}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-foreground/60">
                            <span>{post.author}</span>
                            <span>{post.date}</span>
                            <span>{post.readTime} мин</span>
                            {post.tags.length > 0 && (
                              <span>Теги: {post.tags.join(', ')}</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => togglePublished(post.id)}
                          >
                            {post.published ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingPost(post);
                              setIsCreating(false);
                            }}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deletePost(post.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </div>
        
        <Footer />
      </div>
    </>
  );
};

export default BlogAdmin;
