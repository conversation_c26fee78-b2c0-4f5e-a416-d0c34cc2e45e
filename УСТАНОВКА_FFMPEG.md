# Установка FFmpeg для генерации превью видео

## Проблема
Для генерации превью (thumbnail) из видео требуется библиотека FFmpeg.

## Решение

### Вариант 1: Использование fluent-ffmpeg (рекомендуется)
Добавьте зависимость в package.json:

```bash
npm install fluent-ffmpeg
npm install --save-dev @types/fluent-ffmpeg
```

### Вариант 2: Альтернативное решение без FFmpeg
Если установка FFmpeg вызывает проблемы на сервере, можно использовать альтернативный подход - генерировать статичное превью или использовать первый кадр видео через Canvas API.

## Настройка для Vercel

### Если используете FFmpeg:
1. Добавьте в `vercel.json`:
```json
{
  "functions": {
    "api/**/*.js": {
      "maxDuration": 30
    }
  }
}
```

2. FFmpeg должен быть доступен в runtime окружении Vercel.

### Альтернативный подход (без FFmpeg):
Если FFmpeg недоступен, можно модифицировать функцию `generateThumbnailFromVideo` для возврата `null` и использовать заглушку или первый кадр видео.

## Модификация для работы без FFmpeg

Если FFmpeg недоступен, замените функцию `generateThumbnailFromVideo` в `api/utils/database.js`:

```javascript
export const generateThumbnailFromVideo = async (videoBuffer) => {
  // Временно возвращаем null, пока не настроен FFmpeg
  console.log('[generateThumbnailFromVideo] FFmpeg не настроен, пропускаем генерацию thumbnail');
  return null;
};
```

Это позволит системе работать без превью, сохраняя `thumbnail_url` как `null`.

## Проверка работы

После установки FFmpeg проверьте логи Vercel при сохранении видео:
- Должны появиться сообщения о генерации thumbnail
- В БД должен сохраняться `thumbnail_url`
- В Supabase Storage должна появиться папка `thumbnails`