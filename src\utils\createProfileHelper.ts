/**
 * Утилита для создания профиля пользователя через серверный API
 * с расширенными правами доступа
 */

/**
 * Создает профиль пользователя через серверный API
 * @param userId ID пользователя
 * @param email Email пользователя
 * @param name Имя пользователя (опционально)
 * @returns Объект профиля или null в случае ошибки
 */
export const createProfileViaAPI = async (
  userId: string,
  email: string,
  name?: string
): Promise<any> => {
  try {
    console.log('Создание профиля через API для пользователя:', userId);
    
    // Используем фиксированный хост API для продакшена
    const apiHost = 'https://umaai-api.vercel.app';
    
    const apiUrl = `${apiHost}/api/create-profile`;
    console.log(`Отправляем запрос на создание профиля по адресу: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId,
        email,
        name: name || email.split('@')[0]
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Ошибка при создании профиля через API:', errorData);
      return null;
    }

    const data = await response.json();
    console.log('Профиль успешно создан через API:', data.profile);
    return data.profile;
  } catch (error) {
    console.error('Ошибка при обращении к API создания профиля:', error);
    return null;
  }
};

/**
 * Проверяет существование профиля через API
 * @param userId ID пользователя
 * @returns true если профиль существует, false в противном случае
 */
export const checkProfileExistsViaAPI = async (userId: string): Promise<boolean> => {
  try {
    // Используем фиксированный хост API для продакшена
    const apiHost = 'https://umaai-api.vercel.app';
    
    const apiUrl = `${apiHost}/api/check-profile`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ userId })
    });

    if (!response.ok) {
      console.error('Ошибка при проверке профиля через API');
      return false;
    }

    const data = await response.json();
    return data.exists === true;
  } catch (error) {
    console.error('Ошибка при обращении к API проверки профиля:', error);
    return false;
  }
}; 