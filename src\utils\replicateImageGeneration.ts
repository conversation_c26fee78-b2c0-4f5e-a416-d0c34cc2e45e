import { toast } from 'sonner';
import type { GeneratedImage } from './stabilityImageGeneration';

// Интерфейс для результата, включающий predictionId
export interface ReplicateGenerationResult {
  images: GeneratedImage[];
  replicatePredictionId: string | null;
}

export interface ReplicateImageGenerationOptions {
  prompt: string;
  negativePrompt?: string;
  width?: number;
  height?: number;
  numberOfImages?: number;
  model?: string;
  aspectRatio?: string;
  style_type?: string;
  magic_prompt_option?: string;
  resolution?: string;
  seed?: number;
  style?: string; // Общий стиль для других моделей
  userId?: string;
  reference_images?: string[]; // base64 images for reference
  control_image?: string; // Для fluxpro
  guidance?: number; // Для fluxpro
  steps?: number; // Для fluxpro
  output_format?: string; // Для fluxpro
  safety_tolerance?: number; // Для fluxpro
  prompt_upsampling?: boolean; // Для fluxpro

  // Новые параметры для новых моделей
  characterReferenceImages?: string[]; // Для minimax-image01
  promptOptimizer?: boolean; // Для minimax-image01
  safetyFilterLevel?: string; // Для imagen4
  bagelMode?: 'text2img' | 'edit'; // Для bagel
  editImage?: string; // Для bagel в режиме редактирования
  input_image?: string; // Для flux-kontext моделей
  visibility?: 'public' | 'private'; // Для приватности генераций
}

// Используем serverless функцию вместо прямого доступа к API
const API_ENDPOINT = '/api/replicate';

const DEFAULT_MODEL = "stability-ai/sdxl:c221b2b8ef527988fb59bf24a8b97c4561f1c671f73bd389f866bfb27c061316";

/**
 * Проверяет, является ли ошибка связанной с NSFW/безопасностью
 */
const isNSFWError = (errorMessage: string): boolean => {
  const lowerMessage = errorMessage.toLowerCase();
  return lowerMessage.includes('nsfw') ||
         lowerMessage.includes('safety') ||
         lowerMessage.includes('inappropriate') ||
         lowerMessage.includes('content policy') ||
         lowerMessage.includes('violates') ||
         lowerMessage.includes('blocked') ||
         lowerMessage.includes('фильтром безопасности') ||
         lowerMessage.includes('flagged as sensitive') ||
         lowerMessage.includes('(e005)') ||
         lowerMessage.includes('sensitive content');
};

/**
 * Проверяет, является ли ошибка связанной с входным изображением
 */
const isInputImageError = (errorMessage: string): boolean => {
  const lowerMessage = errorMessage.toLowerCase();
  return lowerMessage.includes('input_image') ||
         lowerMessage.includes('image format') ||
         lowerMessage.includes('invalid image') ||
         lowerMessage.includes('проблема с входным изображением');
};

/**
 * Generates images using the Replicate API
 */
export const generateImageWithReplicate = async (
  options: ReplicateImageGenerationOptions
): Promise<ReplicateGenerationResult> => { // Измененный тип возврата
  console.log('[generateImageWithReplicate] Received options:', JSON.stringify(options, null, 2)); // Added detailed logging
  try {
    const {
      prompt,
      negativePrompt = "",
      width = 1024,
      height = 1024,
      numberOfImages = 1,
      model = DEFAULT_MODEL,
      userId, // Извлекаем userId
      style_type, // Извлекаем style_type для Ideogram
      aspectRatio, // Извлекаем aspectRatio
      style, // Общий стиль для других моделей
      ...rest
    } = options;

    // Маппинг пользовательских названий на реальные модели Replicate
    const modelMap: Record<string, string> = {
      "ideogram": "ideogram-ai/ideogram-v2a-turbo",
      "hidream": "ideogram-ai/ideogram-v3-turbo", // Добавлено для обратной совместимости, если UI посылает 'hidream'
      "ideogram3": "ideogram-ai/ideogram-v3-turbo",
      "ray-flash": "luma/ray-flash-2-540p",
      "kling-pro": "kwaivgi/kling-v1.6-pro",
      "kling-standard": "kwaivgi/kling-v1.6-standard",
      "fluxpro": "black-forest-labs/flux-canny-pro",
      // Новые модели
      "minimax-image01": "minimax/image-01",
      "imagen4": "google/imagen-4",
      "bagel": "bytedance/bagel:7dd8def79e503990740db4704fa81af995d440fefe714958531d7044d2757c9c",
      "flux-kontext-pro": "black-forest-labs/flux-kontext-pro",
      "flux-kontext-max": "black-forest-labs/flux-kontext-max"
    };
    let replicateModel = modelMap[model] || model;

    // Для Replicate API нужен только version (owner/model:version)
    let modelVersion = replicateModel;

    // Безопасный лог: не выводим ключи и чувствительные данные
    console.log('Starting image generation with Replicate:', { prompt, width, height, numberOfImages, version: modelVersion });

    // Ограничиваем количество изображений до допустимого диапазона
    const targetCount = Math.max(1, Math.min(4, numberOfImages));

    // Новый механизм: возвращаем predictionId и ждем через polling, как для flux
    let predictionId: string | null = null; // Объявляем здесь, чтобы было доступно в обоих блоках if/else
    let pollStarted = false;
    const results: GeneratedImage[] = [];

    // Используем options.model для проверки, так как replicateModel уже преобразован
    if (options.model === "ideogram" || options.model === "ideogram3" || options.model === "hidream") {
      // Handle Ideogram: multiple calls if numberOfImages > 1
      const numImagesToGenerate = Math.max(1, numberOfImages || 1);
      console.log(`[generateImageWithReplicate] Ideogram-like model (${options.model} mapped to ${modelVersion}): Generating ${numImagesToGenerate} image(s) in separate calls.`);
      // Генерируем batch_id перед циклом
      const batchId = self.crypto.randomUUID();

      for (let i = 0; i < numImagesToGenerate; i++) {
        const allowedStyleTypes = ["None", "Auto", "General", "Realistic", "Design", "Render 3D", "Anime"];
        let currentStyleType = style_type;
        if (!allowedStyleTypes.includes(currentStyleType || "")) {
          currentStyleType = "None";
        }
        const singleImageInput: Record<string, any> = {
          prompt,
          aspect_ratio: aspectRatio && [
            "1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", "16:10", "10:16", "3:1", "1:3"
          ].includes(aspectRatio)
            ? aspectRatio
            : "1:1",
          style_type: currentStyleType,
          magic_prompt_option: options.magic_prompt_option || "Auto",
          resolution: options.resolution || "None",
          ...(options.seed !== undefined ? { seed: options.seed + i } : {}), // Vary seed for multiple images

          // Передача userId очень важна для вебхука, чтобы знать, кому принадлежит генерация
          ...(userId && { userId }), // обязательно для списания кредитов на сервере
          ...(userId && { user_id_for_webhook: userId }),

          // Новые поля для батчирования и информации для вебхука
          batch_id: batchId,
          is_batch_part: true,
          original_prompt: options.prompt, // Сохраняем исходный промпт
          resolved_model_identifier: modelVersion, // Полный ID модели Replicate
          batch_style: currentStyleType, // Стиль, использованный для этого батча
          batch_aspect_ratio: aspectRatio || "1:1", // AR, использованный для этого батча

          // Вернуть прежнюю логику: style_reference_images для стилизации, image только для inpainting
          ...(options.reference_images && options.reference_images.length > 0
            ? { style_reference_images: options.reference_images }
            : {}),

          // Метаданные для webhook
          metadata_visibility: options.visibility || 'public',
          metadata_style: style || currentStyleType,

          // num_outputs is NOT sent for Ideogram as it generates one image per call
        };

        const requestBody = {
          version: modelVersion,
          input: singleImageInput,
          ...rest
        };

        console.log(`DEBUG UMAAI: Sending to /api/replicate (Ideogram call ${i + 1}/${numImagesToGenerate}) for batch ${batchId}`, {
          version: modelVersion,
          input: { ...singleImageInput, prompt: prompt.substring(0, 40) + '...' }
        });

        const response = await fetch(API_ENDPOINT, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
          throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Error response from Replicate API (Ideogram):', errorData);
          throw new Error(errorData.detail || 'Failed to generate image with Ideogram');
        }

        const result = await response.json();
        predictionId = result.id; // Присваиваем значение ранее объявленной переменной
        console.log(`Image generation started (Ideogram call ${i + 1}), prediction ID:`, predictionId);

        if (predictionId) {
          const pollResult = await pollImageCompletion(predictionId);
          if (pollResult.status === "succeeded" && pollResult.output) {
            // Ideogram returns a single URL string in output
            const imageUrl = pollResult.output as string;
            results.push({
              url: imageUrl,
              // For Ideogram with aspect_ratio, width/height are determined by Replicate
              // We don't return fixed width/height from options to allow UI to use aspect_ratio string
              width: aspectRatio ? 0 : width, // Set to 0 or a placeholder if aspect_ratio is used
              height: aspectRatio ? 0 : height, // Set to 0 or a placeholder if aspect_ratio is used
              prompt,
              aspectRatio: aspectRatio, // Pass aspectRatio along
            });
          } else {
            console.error('Polling failed or no output for Ideogram prediction:', predictionId, pollResult);
            throw new Error(`Failed to get image from Ideogram after polling (ID: ${predictionId})`);
          }
        } else {
          throw new Error('No prediction ID received for Ideogram');
        }
      }
      // Для Ideogram, вебхук обрабатывает каждую картинку отдельно с ее prediction_id.
      // Клиентское первоначальное сохранение может не требовать prediction_id, если оно происходит
      // до того, как вебхук полностью обновит запись.
      // Возвращаем null для replicatePredictionId, так как их может быть много и они обрабатываются вебхуками.
      // Если проблема дублирования существует и для Ideogram, эту логику нужно будет пересмотреть.
      return { images: results, replicatePredictionId: null };
    }

    // Для Flux Pro (black-forest-labs/flux-canny-pro) — отдельная ветка, только нужные параметры
    if (options.model === "black-forest-labs/flux-canny-pro") {
        const input: Record<string, any> = {
            prompt,
            ...(options.control_image && { control_image: options.control_image }),
            ...(options.guidance !== undefined && { guidance: options.guidance }),
            ...(options.steps !== undefined && { steps: options.steps }),
            ...(options.output_format && { output_format: options.output_format }),
            ...(options.safety_tolerance !== undefined && { safety_tolerance: options.safety_tolerance }),
            ...(options.prompt_upsampling !== undefined && { prompt_upsampling: options.prompt_upsampling }),
            ...(options.seed !== undefined && { seed: options.seed }),
            ...(options.userId && { userId: options.userId }),
        };
        const requestBody = {
            version: modelVersion,
            input
        };
        // Важно: не добавлять ...rest для fluxpro!
        console.log("DEBUG UMAAI: Sending to /api/replicate (Flux Pro)", {
            version: modelVersion,
            input: { ...input, prompt: prompt.substring(0, 40) + '...' }
        });
        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });
        if (response.status === 429) {
            throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }
        if (!response.ok) {
            const errorData = await response.json();
            console.error('Error response from Replicate API (Flux Pro):', errorData);
            throw new Error(errorData.detail || 'Failed to generate image with Flux Pro');
        }
        const nonIdeogramApiResponse = await response.json();
        predictionId = nonIdeogramApiResponse.id;
        console.log('Image generation started (Flux Pro), prediction ID:', predictionId);
        if (!pollStarted && predictionId) {
            pollStarted = true;
            const pollResult = await pollImageCompletion(predictionId);
            if (pollResult.status === "succeeded" && pollResult.output) {
                let images: GeneratedImage[] = [];
                if (Array.isArray(pollResult.output)) {
                    images = pollResult.output.map((url: string) => ({
                        url,
                        prompt
                    }));
                } else {
                    images = [{
                        url: pollResult.output as string,
                        prompt,
                        width: 0,
                        height: 0
                    }];
                }
                return { images, replicatePredictionId: predictionId };
            } else {
                console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
                toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
                return { images: [], replicatePredictionId: predictionId };
            }
        }
        toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
        return { images: [], replicatePredictionId: predictionId };
    }

    // Обработка новых моделей
    if (options.model === "minimax-image01") {
        const input: Record<string, any> = {
            prompt,
            aspect_ratio: aspectRatio || "1:1",
            number_of_images: numberOfImages || 1,
            prompt_optimizer: options.promptOptimizer !== undefined ? options.promptOptimizer : true,
            ...(options.characterReferenceImages && options.characterReferenceImages.length > 0 && {
                subject_reference: options.characterReferenceImages[0] // MiniMax принимает только одно изображение
            }),
            ...(options.userId && { userId: options.userId }),
        };

        const requestBody = {
            version: modelVersion,
            input
        };

        console.log("DEBUG UMAAI: Sending to /api/replicate (MiniMax Image-01)", {
            version: modelVersion,
            input: { ...input, prompt: prompt.substring(0, 40) + '...' }
        });

        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
            throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Error response from Replicate API (MiniMax):', errorData);
            throw new Error(errorData.detail || 'Failed to generate image with MiniMax');
        }

        const apiResponse = await response.json();
        predictionId = apiResponse.id;
        console.log('Image generation started (MiniMax), prediction ID:', predictionId);

        if (!pollStarted && predictionId) {
            pollStarted = true;
            const pollResult = await pollImageCompletion(predictionId);
            if (pollResult.status === "succeeded" && pollResult.output) {
                let images: GeneratedImage[] = [];
                if (Array.isArray(pollResult.output)) {
                    images = pollResult.output.map((url: string) => ({
                        url,
                        prompt,
                        width: 0,
                        height: 0,
                        aspectRatio: aspectRatio
                    }));
                } else {
                    images = [{
                        url: pollResult.output as string,
                        prompt,
                        width: 0,
                        height: 0,
                        aspectRatio: aspectRatio
                    }];
                }
                return { images, replicatePredictionId: predictionId };
            } else {
                console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
                toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
                return { images: [], replicatePredictionId: predictionId };
            }
        }
        toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
        return { images: [], replicatePredictionId: predictionId };
    }

    if (options.model === "imagen4") {
        const input: Record<string, any> = {
            prompt,
            aspect_ratio: aspectRatio || "1:1",
            safety_filter_level: options.safetyFilterLevel || "block_medium_and_above",
            ...(options.userId && { userId: options.userId }),
        };

        const requestBody = {
            version: modelVersion,
            input
        };

        console.log("DEBUG UMAAI: Sending to /api/replicate (Google Imagen-4)", {
            version: modelVersion,
            input: { ...input, prompt: prompt.substring(0, 40) + '...' }
        });

        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
            throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Error response from Replicate API (Imagen-4):', errorData);
            throw new Error(errorData.detail || 'Failed to generate image with Imagen-4');
        }

        const apiResponse = await response.json();
        predictionId = apiResponse.id;
        console.log('Image generation started (Imagen-4), prediction ID:', predictionId);

        if (!pollStarted && predictionId) {
            pollStarted = true;
            const pollResult = await pollImageCompletion(predictionId);
            if (pollResult.status === "succeeded" && pollResult.output) {
                const images: GeneratedImage[] = [{
                    url: pollResult.output as string,
                    prompt,
                    width: 0,
                    height: 0,
                    aspectRatio: aspectRatio
                }];
                return { images, replicatePredictionId: predictionId };
            } else {
                console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
                // Проверяем, есть ли специфическая ошибка в pollResult
                const errorMessage = pollResult?.error || '';
                if (isNSFWError(errorMessage)) {
                  toast.error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
                } else {
                  toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
                }
                return { images: [], replicatePredictionId: predictionId };
            }
        }
        toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
        return { images: [], replicatePredictionId: predictionId };
    }

    if (options.model === "bagel") {
        const input: Record<string, any> = {
            prompt,
            task: options.bagelMode === 'edit' ? 'image-editing' : 'text-to-image',
            ...(options.bagelMode === 'edit' && options.editImage && { image: options.editImage }),
            enable_thinking: false, // Не включаем в интерфейс
            cfg_text_scale: 4,
            cfg_img_scale: options.bagelMode === 'edit' ? 1.5 : 2,
            num_inference_steps: 50,
            timestep_shift: 3,
            cfg_renorm_type: "global",
            cfg_renorm_min: 1,
            output_format: "webp",
            output_quality: 90,
            ...(options.userId && { userId: options.userId }),
        };

        const requestBody = {
            version: modelVersion,
            input
        };

        console.log("DEBUG UMAAI: Sending to /api/replicate (ByteDance BAGEL)", {
            version: modelVersion,
            input: { ...input, prompt: prompt.substring(0, 40) + '...' }
        });

        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
            throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Error response from Replicate API (BAGEL):', errorData);

            // Проверяем на специфические ошибки
            if (errorData.error === "NSFW_BLOCKED") {
              throw new Error(errorData.message || 'Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
            }

            const errorMessage = errorData.message || errorData.detail || 'Failed to generate image with BAGEL';
            throw new Error(errorMessage);
        }

        const apiResponse = await response.json();
        predictionId = apiResponse.id;
        console.log('Image generation started (BAGEL), prediction ID:', predictionId);

        if (!pollStarted && predictionId) {
            pollStarted = true;
            const pollResult = await pollImageCompletion(predictionId);
            if (pollResult.status === "succeeded" && pollResult.output) {
                const images: GeneratedImage[] = [{
                    url: pollResult.output.image as string,
                    prompt,
                    width: 0,
                    height: 0,
                }];
                return { images, replicatePredictionId: predictionId };
            } else {
                console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
                // Проверяем, есть ли специфическая ошибка в pollResult
                const errorMessage = pollResult?.error || '';
                if (isNSFWError(errorMessage)) {
                  toast.error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
                } else {
                  toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
                }
                return { images: [], replicatePredictionId: predictionId };
            }
        }
        toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
        return { images: [], replicatePredictionId: predictionId };
    }

    // Обработка новых моделей Flux Kontext
    if (options.model === "flux-kontext-pro" || options.model === "flux-kontext-max") {
        // Поддерживаемые aspect_ratio для Flux Kontext
        const supportedAspectRatios = [
            "match_input_image", "1:1", "16:9", "9:16", "4:3", "3:4",
            "3:2", "2:3", "4:5", "5:4", "21:9", "9:21", "2:1", "1:2"
        ];

        const aspectRatio = supportedAspectRatios.includes(options.aspectRatio || "")
            ? options.aspectRatio
            : "1:1";

        const input: Record<string, any> = {
            prompt,
            ...(options.input_image && { input_image: options.input_image }),
            aspect_ratio: aspectRatio,
            ...(options.seed !== undefined && { seed: options.seed }),
            ...(options.userId && { userId: options.userId }),
        };

        const requestBody = {
            version: modelVersion,
            input
        };

        console.log(`DEBUG UMAAI: Sending to /api/replicate (${options.model})`, {
            version: modelVersion,
            input: { ...input, prompt: prompt.substring(0, 40) + '...' }
        });

        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
            throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
        }

        if (!response.ok) {
            const errorData = await response.json();
            console.error(`Error response from Replicate API (${options.model}):`, errorData);

            // Проверяем на специфические ошибки
            if (errorData.error === "NSFW_BLOCKED") {
              throw new Error(errorData.message || 'Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
            }

            const errorMessage = errorData.message || errorData.detail || `Failed to generate image with ${options.model}`;
            throw new Error(errorMessage);
        }

        const apiResponse = await response.json();
        predictionId = apiResponse.id;
        console.log(`Image generation started (${options.model}), prediction ID:`, predictionId);

        if (!pollStarted && predictionId) {
            pollStarted = true;
            const pollResult = await pollImageCompletion(predictionId);
            if (pollResult.status === "succeeded" && pollResult.output) {
                const images: GeneratedImage[] = [{
                    url: pollResult.output as string,
                    prompt,
                    width: 0,
                    height: 0,
                    aspectRatio: options.aspectRatio
                }];
                return { images, replicatePredictionId: predictionId };
            } else {
                console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
                // Проверяем, есть ли специфическая ошибка в pollResult
                const errorMessage = pollResult?.error || '';
                if (isNSFWError(errorMessage)) {
                  toast.error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
                } else {
                  toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
                }
                return { images: [], replicatePredictionId: predictionId };
            }
        }
        toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
        return { images: [], replicatePredictionId: predictionId };
    }

    // For non-Ideogram models (e.g., SDXL и др.)
    let input: Record<string, any>;
    let calculatedWidth = options.width || 1024; // Start with provided or default
    let calculatedHeight = options.height || 1024; // Start with provided or default

    // List of common and generally supported resolutions for SD-based models.
    // These are often multiples of 64 or 8.
    // Sorted by area (largest first) to attempt to match user's intent for larger images.
    const supportedResolutions: { width: number; height: number; aspectRatio: string }[] = [
        // Common high-res
        { width: 1024, height: 1024, aspectRatio: "1:1" },
        { width: 1280, height: 768, aspectRatio: "16:9" }, // ~16:9
        { width: 768, height: 1280, aspectRatio: "9:16" }, // ~9:16
        { width: 1024, height: 768, aspectRatio: "4:3" },
        { width: 768, height: 1024, aspectRatio: "3:4" },
        { width: 1536, height: 640, aspectRatio: "12:5" }, // Wide, e.g., for adsgfd/hidream if it's not Ideogram
        { width: 640, height: 1536, aspectRatio: "5:12" }, // Tall

        // Mid-res
        { width: 768, height: 768, aspectRatio: "1:1" },
        { width: 1024, height: 576, aspectRatio: "16:9" }, // Common 16:9
        { width: 576, height: 1024, aspectRatio: "9:16" }, // Common 9:16
        { width: 768, height: 512, aspectRatio: "3:2" },
        { width: 512, height: 768, aspectRatio: "2:3" },

        // Low-res / more compatible
        { width: 512, height: 512, aspectRatio: "1:1" },
        { width: 640, height: 384, aspectRatio: "16:9" }, // ~16:9
        { width: 384, height: 640, aspectRatio: "9:16" }, // ~9:16

        // Specific problematic examples from logs, trying to find closest valid
        // For 1792x1024 (16:9) -> 1024x576 is a good match if 1792x1024 is not supported
        // For 720x1080 (2:3) -> 704x1024. Closest standard could be 768x1024 or 512x768.
        // Let's ensure we have some values around those.
        { width: 704, height: 1024, aspectRatio: "7:10" }, // From log example, if this is valid for some
        { width: 1024, height: 576, aspectRatio: "16:9" }, // Already exists, good for 16:9
    ].sort((a, b) => (b.width * b.height) - (a.width * a.height)); // Ensure sorted by area

    // Helper function to find the closest resolution
    const findClosestResolution = (targetWidth: number, targetHeight: number, targetAspectRatio?: string) => {
        let bestMatch = supportedResolutions[0]; // Default to largest if no good match
        let minDiff = Infinity;

        // Calculate target aspect ratio if not provided directly
        const numericTargetAr = targetAspectRatio
            ? parseFloat(targetAspectRatio.split(':')[0]) / parseFloat(targetAspectRatio.split(':')[1])
            : targetWidth / targetHeight;

        for (const res of supportedResolutions) {
            const resAr = res.width / res.height;
            // Prioritize aspect ratio match, then area.
            // Weight aspect ratio difference more heavily.
            const aspectRatioDifference = Math.abs(resAr - numericTargetAr);
            const areaDifference = Math.abs((res.width * res.height) - (targetWidth * targetHeight));

            // Simple scoring: lower is better.
            // Preferring resolutions that are multiples of 64.
            // Smaller resolutions might also be less prone to errors.
            let score = aspectRatioDifference * 1000000 + areaDifference;
            if (res.width % 64 !== 0 || res.height % 64 !== 0) {
                score += 50000; // Penalize non-64 multiples slightly less than huge AR diff
            }


            if (score < minDiff) {
                minDiff = score;
                bestMatch = res;
            }
        }
        console.log(`[findClosestResolution] Target: ${targetWidth}x${targetHeight} (AR: ${targetAspectRatio || 'N/A'}), Chosen: ${bestMatch.width}x${bestMatch.height} (AR: ${bestMatch.aspectRatio})`);
        return bestMatch;
    };

    if (options.aspectRatio) {
        console.log(`[generateImageWithReplicate] Non-Ideogram model with aspectRatio: ${options.aspectRatio}. Original options W/H: ${options.width}/${options.height}.`);
        // Attempt to parse aspect ratio to guide selection
        const parts = options.aspectRatio.split(':');
        let tempTargetWidth = calculatedWidth; // Use default/original as a starting point for area
        let tempTargetHeight = calculatedHeight;

        if (parts.length === 2) {
            const arW = parseInt(parts[0], 10);
            const arH = parseInt(parts[1], 10);
            if (!isNaN(arW) && !isNaN(arH) && arW > 0 && arH > 0) {
                // Use a base dimension to estimate target W/H while preserving aspect ratio for search
                const baseDimensionForSearch = 1024;
                if (arW >= arH) { // Landscape or square
                    tempTargetWidth = baseDimensionForSearch;
                    tempTargetHeight = Math.round((baseDimensionForSearch * arH) / arW);
                } else { // Portrait
                    tempTargetHeight = baseDimensionForSearch;
                    tempTargetWidth = Math.round((baseDimensionForSearch * arW) / arH);
                }
                 const chosenResolution = findClosestResolution(tempTargetWidth, tempTargetHeight, options.aspectRatio);
                 calculatedWidth = chosenResolution.width;
                 calculatedHeight = chosenResolution.height;
            } else {
                console.warn(`[generateImageWithReplicate] Invalid aspectRatio format: ${options.aspectRatio}. Falling back to default/provided width/height for selection.`);
                 const chosenResolution = findClosestResolution(calculatedWidth, calculatedHeight); // Use original W/H
                 calculatedWidth = chosenResolution.width;
                 calculatedHeight = chosenResolution.height;
            }
        } else {
             console.warn(`[generateImageWithReplicate] Invalid aspectRatio format: ${options.aspectRatio}. Falling back to default/provided width/height for selection.`);
             const chosenResolution = findClosestResolution(calculatedWidth, calculatedHeight); // Use original W/H
             calculatedWidth = chosenResolution.width;
             calculatedHeight = chosenResolution.height;
        }
    } else {
        // If no aspect ratio, but width/height are provided, find closest supported resolution to those.
        // This handles cases where user might input non-standard dimensions directly.
        console.log(`[generateImageWithReplicate] Non-Ideogram model without aspectRatio. Using provided W/H: ${calculatedWidth}/${calculatedHeight} to find closest match.`);
        const chosenResolution = findClosestResolution(calculatedWidth, calculatedHeight);
        calculatedWidth = chosenResolution.width;
        calculatedHeight = chosenResolution.height;
    }

    // Final check to ensure dimensions are at least a minimum, e.g., 64x64, though list should prevent this.
    calculatedWidth = Math.max(64, calculatedWidth);
    calculatedHeight = Math.max(64, calculatedHeight);

    console.log(`[generateImageWithReplicate] Final dimensions for non-Ideogram model: width=${calculatedWidth}, height=${calculatedHeight}`);

    input = {
        prompt,
        negative_prompt: negativePrompt,
        width: calculatedWidth,
        height: calculatedHeight,
        num_outputs: targetCount,
        ...(options.style_type && { style: options.style_type }), // Pass style if provided
        // Do NOT pass aspect_ratio directly to these models if width/height are explicitly set
        ...(options.userId && { userId: options.userId }),
    };
    // ... rest of the code for sending request remains the same
    const requestBody = {
        version: modelVersion,
        input,
        ...rest
    };
    console.log("DEBUG UMAAI: Sending to /api/replicate (Non-Ideogram)", {
        version: modelVersion,
        input: { ...input, prompt: prompt.substring(0, 40) + '...' }
    });
    console.log("DEBUG UMAAI: userId in input:", input.userId);

    const response = await fetch(API_ENDPOINT, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
    });

    // Обработка ошибки превышения лимита запросов (429)
    if (response.status === 429) {
      throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
    }

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error response from Replicate API:', errorData);

      // Проверяем на специфические ошибки
      if (errorData.error === "NSFW_BLOCKED") {
        throw new Error(errorData.message || 'Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
      }

      const errorMessage = errorData.message || errorData.detail || 'Failed to generate image';
      throw new Error(errorMessage);
    }

    const nonIdeogramApiResponse = await response.json();
    predictionId = nonIdeogramApiResponse.id;
    console.log('Image generation started (Non-Ideogram), prediction ID:', predictionId);

    // Всегда ждем результат через polling
    if (!pollStarted && predictionId) {
      pollStarted = true;
      const pollResult = await pollImageCompletion(predictionId);
      if (pollResult.status === "succeeded" && pollResult.output) {
        let images: GeneratedImage[] = [];
        if (Array.isArray(pollResult.output)) {
          images = pollResult.output.map((url: string) => ({
            url,
            width: calculatedWidth, // Используем calculatedWidth/Height
            height: calculatedHeight,
            prompt,
            // aspectRatio опционально, если нужно его передавать обратно
          }));
        } else {
          images = [{
            url: pollResult.output as string, // Явное приведение типа
            width: calculatedWidth,
            height: calculatedHeight,
            prompt,
          }];
        }
        return { images, replicatePredictionId: predictionId }; // <--- ВОЗВРАЩАЕМ predictionId
      } else {
        console.error(`Polling failed for prediction ${predictionId}. Status: ${pollResult?.status}, Output: ${pollResult?.output}`);
        // Проверяем, есть ли специфическая ошибка в pollResult
        const errorMessage = pollResult?.error || '';
        if (isNSFWError(errorMessage)) {
          toast.error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
        } else {
          toast.error(`Генерация изображения не удалась (ID: ${predictionId}). Статус: ${pollResult?.status || 'неизвестен'}`);
        }
        // Возвращаем пустой массив и ID, чтобы вызывающий код мог обработать ошибку и, возможно, сохранить информацию о неудаче
        return { images: [], replicatePredictionId: predictionId };
        // throw new Error(`Генерация изображения не удалась или не завершилась успешно (ID: ${predictionId}). Статус: ${pollResult?.status}`);
      }
    }
    // Если не удалось получить predictionId или начать polling
    toast.error("Не удалось запустить генерацию изображения или получить ID предсказания.");
    return { images: [], replicatePredictionId: predictionId }; // predictionId может быть null здесь
    // throw new Error("Генерация изображения не удалось запустить или получить prediction ID.");
  } catch (error) {
    console.error('Error in generateImageWithReplicate:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Проверяем на специфические ошибки NSFW/безопасности
    if (isNSFWError(errorMessage)) {
      throw new Error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
    }

    // Проверяем на проблемы с входным изображением
    if (isInputImageError(errorMessage)) {
      throw new Error('Проблема с входным изображением. Убедитесь, что изображение корректно и попробуйте снова.');
    }

    // Проверяем на превышение лимитов
    if (errorMessage.toLowerCase().includes('превышен лимит') ||
        errorMessage.toLowerCase().includes('rate limit') ||
        errorMessage.toLowerCase().includes('429')) {
      throw new Error('Превышен лимит запросов к API. Пожалуйста, попробуйте позже.');
    }

    // Общая ошибка с сохранением оригинального сообщения
    const finalError = new Error(`Ошибка генерации изображения: ${errorMessage}`);
    throw finalError;
  }
};

/**
 * Опрашивает API Replicate для получения результата генерации изображения
 */
const pollImageCompletion = async (predictionId: string, maxAttempts = 120): Promise<any> => {
  console.log(`Starting to poll for image generation result (ID: ${predictionId})`);
  toast.info("Генерация изображения в процессе. Это может занять несколько минут...");

  let attempt = 0;
  let retryDelay = 3000; // Начальная задержка 3 секунды
  const maxRetryDelay = 20000; // Максимальная задержка в 20 секунд

  while (attempt < maxAttempts) {
    try {
      // Используем прокси-сервер для проверки статуса
      const response = await fetch(`${API_ENDPOINT}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
      apiToken: "",
          predictionId
        })
      });

      // Обработка ошибки превышения лимита запросов (429)
      if (response.status === 429) {
        console.log(`Rate limit exceeded (429) while polling. Retrying in ${retryDelay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));

        // Увеличиваем задержку для следующей попытки
        retryDelay = Math.min(retryDelay * 2, maxRetryDelay);
        attempt++;
        continue;
      }

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error checking prediction status:', errorData);
        throw new Error(errorData.detail || 'Failed to check prediction status');
      }

      const prediction = await response.json();
      const status = prediction.status;

      console.log(`Prediction status: ${status}, check ${attempt + 1}/${maxAttempts}`);

      if (status === "succeeded") {
        return prediction;
      } else if (status === "failed") {
        // Проверяем причину сбоя для более информативной ошибки
        const errorMessage = prediction.error || '';
        console.log('Prediction failed with error:', errorMessage);

        // Проверяем на NSFW контент
        if (isNSFWError(errorMessage)) {
          throw new Error('Изображение заблокировано фильтром безопасности. Попробуйте изменить описание или загрузить другое изображение.');
        }

        // Проверяем на проблемы с входным изображением
        if (isInputImageError(errorMessage)) {
          throw new Error('Проблема с входным изображением. Убедитесь, что изображение корректно и попробуйте снова.');
        }

        // Общая ошибка с деталями, если они есть
        const detailedError = errorMessage ? `Генерация изображения не удалась: ${errorMessage}` : 'Генерация изображения не удалась';
        throw new Error(detailedError);
      }

      // Ждем перед следующей проверкой
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;
    } catch (error) {
      console.error(`Error polling image completion (attempt ${attempt + 1}):`, error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      // Если это ошибка NSFW/безопасности, не пытаемся повторить
      if (isNSFWError(errorMessage)) {
        throw error; // Прокидываем ошибку дальше без повторных попыток
      }

      // Если это сетевая ошибка, увеличиваем задержку
      if (errorMessage.includes('Failed to fetch') || errorMessage.includes('NetworkError')) {
        retryDelay = Math.min(retryDelay * 1.5, maxRetryDelay);
        console.log(`Network error detected, increasing retry delay to ${retryDelay}ms`);
      }

      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;
    }
  }

  throw new Error('Превышено максимальное количество попыток проверки статуса изображения');
};

export default generateImageWithReplicate;
