// Эндпоинт для получения результата речевых моделей
// GET/POST /api/fal/speech-result

import fetch from "node-fetch";
import { saveSpeechGeneration, downloadFile, uploadAudioToSupabase } from "../../utils/database.js";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  console.log(`[FAL SPEECH RESULT] Received ${req.method} request`);

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    console.log(`[FAL SPEECH RESULT] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Поддерживаем параметры как в query (GET), так и в body (POST)
    let requestId, model, userId, text, voice, visibility;
    
    if (req.method === 'GET') {
      ({ requestId, model, userId, text, voice, visibility } = req.query);
    } else if (req.method === 'POST') {
      ({ requestId, model, userId, text, voice, visibility } = req.body);
    }

    console.log('[FAL SPEECH RESULT] Getting result for:', { requestId, model, userId, method: req.method });

    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL SPEECH RESULT] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Сначала проверяем статус
    const statusUrl = `https://queue.fal.run/${model}/requests/${requestId}/status`;
    console.log('[FAL SPEECH RESULT] Checking status at:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL SPEECH RESULT] Status check failed:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Status check failed',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL SPEECH RESULT] Status data:', statusData);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({
        error: 'Generation not completed',
        status: statusData.status
      });
    }

    // Получаем результат
    const resultUrl = `https://queue.fal.run/${model}/requests/${requestId}`;
    console.log('[FAL SPEECH RESULT] Requesting result from:', resultUrl);

    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL SPEECH RESULT] Result request failed:', errorText);
      return res.status(falResponse.status).json({
        error: 'Result request failed',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL SPEECH RESULT] Result response:', result);

    // Если есть аудио и параметры для сохранения, сохраняем в БД
    if (result.audio && result.audio.url && userId && text) {
      try {
        console.log('[FAL SPEECH RESULT] Saving audio to database...');
        const savedGeneration = await saveSpeechToDatabase(
          result.audio.url, 
          model, 
          userId, 
          text, 
          voice || 'Rachel',
          visibility || 'public', 
          requestId
        );
        console.log('[FAL SPEECH RESULT] Audio saved to database successfully');
        
        // Возвращаем результат с URL из нашей БД
        return res.status(200).json({
          ...result,
          audio: {
            ...result.audio,
            url: savedGeneration.audio_url
          },
          saved: true,
          generationId: savedGeneration.id
        });
      } catch (saveError) {
        console.error('[FAL SPEECH RESULT] Error saving audio to database:', saveError);
        // Не возвращаем ошибку, так как аудио все равно сгенерировано
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL SPEECH RESULT] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Сохраняет аудио в базу данных
 */
async function saveSpeechToDatabase(audioUrl, model, userId, text, voice, visibility, falRequestId) {
  try {
    console.log(`[FAL SPEECH RESULT] Saving audio to database: ${audioUrl}`);

    // Скачиваем аудио
    const audioBuffer = await downloadFile(audioUrl);
    console.log(`[FAL SPEECH RESULT] Downloaded audio, size: ${audioBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const audioId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${audioId}/${timestamp}-audio.mp3`;

    // Загружаем аудио в Supabase Storage
    const supabaseAudioUrl = await uploadAudioToSupabase(audioBuffer, fileName);
    console.log(`[FAL SPEECH RESULT] Audio uploaded to Storage: ${supabaseAudioUrl}`);

    // Рассчитываем стоимость
    let cost = 5; // Минимум
    if (model.includes('turbo-v2.5')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 20));
    } else if (model.includes('multilingual-v2')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 40));
    }

    // Сохраняем в БД
    const savedGeneration = await saveSpeechGeneration({
      user_id: userId,
      text: text || '',
      audio_url: supabaseAudioUrl,
      voice_id: voice,
      voice_name: voice,
      model: model,
      parameters: {
        voice: voice,
        fal_request_id: falRequestId
      },
      cost: cost,
      public: visibility === 'public'
    });

    console.log(`[FAL SPEECH RESULT] Speech generation saved to database with ID: ${audioId}`);

    return savedGeneration;

  } catch (error) {
    console.error(`[FAL SPEECH RESULT] Error saving audio to database:`, error);
    throw error;
  }
}
