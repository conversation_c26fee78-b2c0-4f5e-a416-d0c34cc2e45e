# Тест новых параметров Veo3

## Что было добавлено:

### 1. Переводы
- `enhancePrompt` / `enhancePromptDesc` - на русском и английском
- `generateAudio` / `generateAudioDesc` - на русском и английском

### 2. Интерфейс (VideoGeneration.tsx)
- Новые состояния: `enhancePrompt` (по умолчанию true), `generateAudio` (по умолчанию true)
- Секция с чекбоксами для Veo3 модели
- Обновленная функция расчета стоимости: 1280 кредитов с аудио, 1000 без аудио
- Передача новых параметров в `generateVideoWithFal`

### 3. API обработчик (api/fal/veo3/index.js)
- Обновленная функция `getVeo3GenerationCost` с параметром `generateAudio`
- Добавление `enhance_prompt` и `generate_audio` в input для Veo3 API
- Динамический расчет стоимости

### 4. Утилита генерации (src/utils/falVideoGeneration.ts)
- Обновленные интерфейсы с новыми параметрами
- Передача параметров в API для Veo3
- Обновленные inputKeys для Veo3
- Правильная передача стоимости в result endpoint

## Схема работы:
1. Пользователь выбирает модель Veo3
2. Появляются чекбоксы "Улучшить промпт" и "Генерация аудио"
3. При изменении "Генерация аудио" обновляется стоимость (1280 → 1000 кредитов)
4. Параметры передаются через всю цепочку: UI → falVideoGeneration → API → FAL API
5. Результат сохраняется с правильной стоимостью

## Для тестирования:
1. Выбрать модель Veo3 в интерфейсе
2. Проверить отображение новых параметров
3. Изменить "Генерация аудио" и убедиться, что стоимость изменилась
4. Запустить генерацию и проверить логи API
