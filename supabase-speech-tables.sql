-- Создание таблиц для функциональности Speech Generation

-- Таблица для сгенерированных речевых записей
CREATE TABLE IF NOT EXISTS speech_generations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  text TEXT NOT NULL,
  audio_url TEXT NOT NULL,
  voice_id TEXT NOT NULL,
  voice_name TEXT,
  model TEXT NOT NULL,
  parameters JSONB,
  cost INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  public BOOLEAN DEFAULT false
);

-- Таблица для клонированных голосов
CREATE TABLE IF NOT EXISTS voice_clones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  voice_id TEXT UNIQUE NOT NULL,
  audio_url TEXT NOT NULL,
  model TEXT NOT NULL,
  accuracy FLOAT DEFAULT 0.7,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  public BOOLEAN DEFAULT true
);

-- Таблица для библиотеки голосов (включая системные и пользовательские)
CREATE TABLE IF NOT EXISTS voice_library (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voice_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('system', 'user', 'community')),
  creator_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  avatar_gradient TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_speech_generations_user_id ON speech_generations(user_id);
CREATE INDEX IF NOT EXISTS idx_speech_generations_created_at ON speech_generations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_voice_clones_user_id ON voice_clones(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_clones_public ON voice_clones(public);
CREATE INDEX IF NOT EXISTS idx_voice_library_type ON voice_library(type);
CREATE INDEX IF NOT EXISTS idx_voice_library_usage_count ON voice_library(usage_count DESC);

-- RLS (Row Level Security) политики
ALTER TABLE speech_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_clones ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_library ENABLE ROW LEVEL SECURITY;

-- Политики для speech_generations
CREATE POLICY "Users can view their own speech generations" ON speech_generations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own speech generations" ON speech_generations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own speech generations" ON speech_generations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own speech generations" ON speech_generations
  FOR DELETE USING (auth.uid() = user_id);

-- Политики для voice_clones
CREATE POLICY "Users can view their own voice clones" ON voice_clones
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view public voice clones" ON voice_clones
  FOR SELECT USING (public = true);

CREATE POLICY "Users can insert their own voice clones" ON voice_clones
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own voice clones" ON voice_clones
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own voice clones" ON voice_clones
  FOR DELETE USING (auth.uid() = user_id);

-- Политики для voice_library
CREATE POLICY "Everyone can view voice library" ON voice_library
  FOR SELECT USING (true);

CREATE POLICY "Users can insert into voice library" ON voice_library
  FOR INSERT WITH CHECK (auth.uid() = creator_id OR type = 'system');

CREATE POLICY "Users can update their own voice library entries" ON voice_library
  FOR UPDATE USING (auth.uid() = creator_id OR type = 'system');

-- Функция для автоматического добавления клонированного голоса в библиотеку
CREATE OR REPLACE FUNCTION add_voice_to_library()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO voice_library (voice_id, name, type, creator_id, avatar_gradient)
  VALUES (
    NEW.voice_id,
    NEW.name,
    CASE WHEN NEW.public THEN 'community' ELSE 'user' END,
    NEW.user_id,
    -- Генерируем простой градиент на основе имени
    'linear-gradient(135deg, #' || 
    substr(md5(NEW.name), 1, 6) || ' 0%, #' || 
    substr(md5(NEW.name || 'salt'), 1, 6) || ' 100%)'
  )
  ON CONFLICT (voice_id) DO UPDATE SET
    name = EXCLUDED.name,
    type = EXCLUDED.type,
    avatar_gradient = EXCLUDED.avatar_gradient;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического добавления в библиотеку
CREATE TRIGGER trigger_add_voice_to_library
  AFTER INSERT ON voice_clones
  FOR EACH ROW
  EXECUTE FUNCTION add_voice_to_library();

-- Функция для обновления счетчика использования голоса
CREATE OR REPLACE FUNCTION increment_voice_usage(voice_id_param TEXT)
RETURNS void AS $$
BEGIN
  UPDATE voice_library 
  SET usage_count = usage_count + 1 
  WHERE voice_id = voice_id_param;
END;
$$ LANGUAGE plpgsql;

-- Вставляем системные голоса в библиотеку
INSERT INTO voice_library (voice_id, name, type, avatar_gradient) VALUES
  ('Wise_Woman', 'Wise Woman', 'system', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'),
  ('Friendly_Person', 'Friendly Person', 'system', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'),
  ('Inspirational_girl', 'Inspirational Girl', 'system', 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'),
  ('Deep_Voice_Man', 'Deep Voice Man', 'system', 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'),
  ('Calm_Woman', 'Calm Woman', 'system', 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'),
  ('Casual_Guy', 'Casual Guy', 'system', 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'),
  ('Lively_Girl', 'Lively Girl', 'system', 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'),
  ('Patient_Man', 'Patient Man', 'system', 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'),
  ('Young_Knight', 'Young Knight', 'system', 'linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%)'),
  ('Determined_Man', 'Determined Man', 'system', 'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)'),
  ('Lovely_Girl', 'Lovely Girl', 'system', 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)'),
  ('Decent_Boy', 'Decent Boy', 'system', 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)'),
  ('Imposing_Manner', 'Imposing Manner', 'system', 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)'),
  ('Elegant_Man', 'Elegant Man', 'system', 'linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%)'),
  ('Abbess', 'Abbess', 'system', 'linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%)'),
  ('Sweet_Girl_2', 'Sweet Girl 2', 'system', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'),
  ('Exuberant_Girl', 'Exuberant Girl', 'system', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)')
ON CONFLICT (voice_id) DO NOTHING;

-- Комментарии к таблицам
COMMENT ON TABLE speech_generations IS 'Таблица для хранения сгенерированных речевых записей';
COMMENT ON TABLE voice_clones IS 'Таблица для хранения клонированных голосов пользователей';
COMMENT ON TABLE voice_library IS 'Библиотека всех доступных голосов (системные и пользовательские)';

COMMENT ON COLUMN speech_generations.parameters IS 'JSON с параметрами генерации (speed, pitch, volume, emotion, etc.)';
COMMENT ON COLUMN voice_clones.accuracy IS 'Точность клонирования голоса (0.0 - 1.0)';
COMMENT ON COLUMN voice_library.avatar_gradient IS 'CSS градиент для аватара голоса';
COMMENT ON COLUMN voice_library.usage_count IS 'Количество использований голоса';