// Fal API status endpoint for Veo3 model
// GET /api/fal/veo3/status?requestId=<request_id>&model=<model_id>

import { fal } from "@fal-ai/client";

export default async function handler(req, res) {
  // Разрешаем только GET запросы
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model } = req.query;

    console.log('[VEO3 STATUS] Checking status for request:', requestId);

    // Проверяем обязательные параметры
    if (!requestId) {
      return res.status(400).json({ error: 'Request ID is required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[VEO3 STATUS] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Настраиваем FAL клиент
    fal.config({
      credentials: falKey
    });

    console.log('[VEO3 STATUS] Checking status for request:', requestId);

    // Получаем статус через FAL клиент
    const result = await fal.queue.status("fal-ai/veo3", {
      requestId: requestId,
      logs: true
    });

    console.log('[VEO3 STATUS] Fal API response:', result);

    // Возвращаем статус клиенту
    return res.status(200).json(result);

  } catch (error) {
    console.error('[VEO3 STATUS] Unexpected error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    });
  }
}
