-- Исправление таблицы credit_transactions - добавление отсутствующих колонок

-- Добавляем колонку balance_after если её нет
ALTER TABLE credit_transactions
ADD COLUMN IF NOT EXISTS balance_after INTEGER;

-- Добавляем колонку type если её нет
ALTER TABLE credit_transactions
ADD COLUMN IF NOT EXISTS type VARCHAR(50);

-- Обновляем существующие записи, рассчитывая balance_after
UPDATE credit_transactions
SET balance_after = (
  SELECT
    COALESCE(
      (SELECT credits FROM profiles WHERE id = credit_transactions.user_id),
      0
    )
)
WHERE balance_after IS NULL;

-- Обновляем существующие записи, устанавливая type
UPDATE credit_transactions
SET type = CASE
  WHEN amount < 0 THEN 'debit'
  WHEN amount > 0 THEN 'credit'
  ELSE 'unknown'
END
WHERE type IS NULL;

-- Добавляем NOT NULL constraint после заполнения данных
ALTER TABLE credit_transactions
ALTER COLUMN balance_after SET NOT NULL;

ALTER TABLE credit_transactions
ALTER COLUMN type SET NOT NULL;

-- Добавляем комментарии к колонкам
COMMENT ON COLUMN credit_transactions.balance_after IS 'Баланс пользователя после транзакции';
COMMENT ON COLUMN credit_transactions.type IS 'Тип транзакции: credit, debit, refund';

-- Проверяем структуру таблицы
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'credit_transactions'
ORDER BY ordinal_position;