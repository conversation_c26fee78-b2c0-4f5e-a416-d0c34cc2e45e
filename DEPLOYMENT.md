# Инструкция по деплою проекта на Vercel

## Настройка переменных окружения в Vercel

Для корректной работы сайта на Vercel необходимо настроить переменные окружения. Добавьте следующие переменные в раздел Environment Variables в панели управления проектом:

```
VITE_SUPABASE_URL=https://ehamdaltpbuicmggxhbn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4ODI4NzIsImV4cCI6MjA1OTQ1ODg3Mn0.E-Q4-HfKege8ipj2JFGsl8sCFlUbbDhJqTue7A6Uhgo
VITE_APP_ENV=production
VITE_APP_URL=https://umaai.vercel.app
```

## Настройка Supabase для работы с Vercel

1. Войдите в панель управления Supabase
2. Перейдите в раздел Authentication -> URL Configuration
3. Добавьте домен `https://umaai.vercel.app` в поле "Site URL"
4. В разделе "Redirect URLs" добавьте:
   - `https://umaai.vercel.app/auth/callback`
   - `https://umaai.vercel.app/login`
   - `https://umaai.vercel.app`
5. Нажмите "Save"

## Команды для деплоя

Используйте следующие команды для ручного обновления проекта:

```bash
# Сборка проекта
npm run build

# Проверка сборки
npm run preview

# Деплой на Vercel
vercel --prod
```

## Диагностика общих проблем

### Ошибка аутентификации (400 Bad Request)

Если вы получаете ошибку 400 при попытке входа, проверьте:

1. Правильно ли настроены переменные окружения в Vercel
2. Добавлен ли URL сайта в список разрешенных URL в настройках Supabase
3. Корректная версия API ключа используется для доступа к Supabase

### Ошибка создания профиля пользователя

Если вы видите ошибки при создании профиля:

1. Убедитесь, что таблица `profiles` существует в базе данных Supabase
2. Проверьте наличие всех необходимых столбцов в таблице (id, email, name, credits, created_at, updated_at)
3. Проверьте права доступа к таблице для анонимного пользователя

### Отсутствующие строки в переводе

Если на сайте отображаются коды переводов вместо текста:

1. Проверьте файлы локализации в `src/locales`
2. Добавьте недостающие ключи в файлы перевода
3. Перезапустите сборку проекта

## Обновление домена

Если вы меняете домен сайта, не забудьте:

1. Обновить переменную `VITE_APP_URL` в настройках проекта Vercel
2. Обновить список разрешенных URL в настройках Supabase
3. Пересобрать и перезапустить проект 