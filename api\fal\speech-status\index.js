// Эндпоинт для проверки статуса речевых моделей
// GET/POST /api/fal/speech-status

import fetch from "node-fetch";

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  console.log(`[FAL SPEECH STATUS] Received ${req.method} request`);

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    console.log(`[FAL SPEECH STATUS] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Поддерживаем параметры как в query (GET), так и в body (POST)
    let requestId, model, getResult;
    
    if (req.method === 'GET') {
      ({ requestId, model, getResult } = req.query);
    } else if (req.method === 'POST') {
      ({ requestId, model, getResult } = req.body);
    }

    console.log('[FAL SPEECH STATUS] Checking status for:', { requestId, model, getResult, method: req.method });

    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL SPEECH STATUS] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Формируем URL для проверки статуса речевых моделей
    const statusUrl = `https://queue.fal.run/${model}/requests/${requestId}/status`;

    console.log('[FAL SPEECH STATUS] Requesting status from:', statusUrl);

    // Отправляем запрос к Fal API
    const falResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL SPEECH STATUS] API error:', errorText);
      return res.status(falResponse.status).json({
        error: 'Fal API status error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL SPEECH STATUS] Status response:', result);

    // Если запрошен результат и статус COMPLETED, получаем результат
    if (getResult === 'true' && result.status === 'COMPLETED' && result.response_url) {
      console.log('[FAL SPEECH STATUS] Getting result from response_url:', result.response_url);

      try {
        const resultResponse = await fetch(result.response_url, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (resultResponse.ok) {
          const resultData = await resultResponse.json();
          console.log('[FAL SPEECH STATUS] Got result data');
          return res.status(200).json(resultData);
        } else {
          console.error('[FAL SPEECH STATUS] Failed to get result:', resultResponse.status);
        }
      } catch (resultError) {
        console.error('[FAL SPEECH STATUS] Error getting result:', resultError);
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL SPEECH STATUS] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
