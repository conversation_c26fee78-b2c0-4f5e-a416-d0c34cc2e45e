-- Скрипт для очистки дублирующихся политик RLS

-- 1. Удаляем ВСЕ существующие политики для таблицы profiles
DROP POLICY IF EXISTS "Пользователи могут создавать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут просматривать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут обновлять свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может просматривать все профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может создавать профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может обновлять профили" ON public.profiles;
DROP POLICY IF EXISTS "Service role может создавать профили" ON public.profiles;
DROP POLICY IF EXISTS "Service role может просматривать профил" ON public.profiles;
DROP POLICY IF EXISTS "Service role может обновлять профили" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profiles" ON public.profiles;
DROP POLICY IF EXISTS "Сервисная роль имеет полный досту" ON public.profiles;

-- 2. Создаем единый набор политик
-- Для аутентифицированных пользователей 
CREATE POLICY "users_insert_own"
ON public.profiles FOR INSERT 
TO authenticated, anon
WITH CHECK (auth.uid() = id);

CREATE POLICY "users_select_own"
ON public.profiles FOR SELECT 
TO authenticated, anon
USING (auth.uid() = id);

CREATE POLICY "users_update_own"
ON public.profiles FOR UPDATE 
TO authenticated
USING (auth.uid() = id);

-- Для сервисной роли (упрощаем до одной политики ALL)
CREATE POLICY "service_role_all"
ON public.profiles FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 3. Проверяем результат
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public' AND tablename = 'profiles'
ORDER BY tablename, policyname; 