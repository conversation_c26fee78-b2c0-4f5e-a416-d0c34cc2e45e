// Утилита для работы с Replicate API
interface ReplicateInput {
  prompt: string;
  start_image?: string;
  aspect_ratio?: string;
  seed?: number;
  speed_mode?: string;
  output_quality?: number;
  model?: string;
  width?: number;
  height?: number;
  numberOfImages?: number;
}

interface ReplicateConfig {
  apiKey?: string;
  baseUrl?: string;
}

class ReplicateClient {
  private baseUrl: string;
  private apiKey: string | null;

  constructor(config?: ReplicateConfig) {
    this.baseUrl = config?.baseUrl || '/api/replicate';
    this.apiKey = config?.apiKey || null;
  }

  /**
   * Запускает модель Replicate и возвращает результат
   */
  async run(model: string, options: { input: ReplicateInput }): Promise<string | string[]> {
    console.log(`Starting Replicate run for model: ${model}`);
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    if (this.apiKey) {
      headers['Authorization'] = `Token ${this.apiKey}`;
    }

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          model,
          input: options.input,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Replicate API Error (${response.status}): ${errorText}`);
        throw new Error(`Replicate API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Replicate API response:', data);
      
      if (Array.isArray(data.output)) {
        return data.output;
      }
      
      return data.output || '';
    } catch (error) {
      console.error('Error calling Replicate API:', error);
      throw error;
    }
  }
}

/**
 * Функция для генерации изображений через API Replicate
 */
export async function generateImageWithReplicate({
  prompt,
  model = 'ideogram-ai/ideogram-v2a-turbo',
  width = 1024,
  height = 1024,
  numberOfImages = 1,
  aspect_ratio
}: ReplicateInput): Promise<string[]> {
  console.log('Starting image generation with Replicate:', { prompt, width, height, numberOfImages, model });
  
  // Создаем экземпляр клиента
  const replicate = new ReplicateClient();
  
  // Подготавливаем входные данные в зависимости от модели
  let input: any = { prompt };
  
  if (aspect_ratio) {
    input.aspect_ratio = aspect_ratio;
  } else {
    // Если aspect_ratio не указан, используем width и height
    input.width = width;
    input.height = height;
  }
  
  // Запускаем модель и получаем результат
  try {
    const output = await replicate.run(model, { input });
    console.log('Received output from Replicate:', Array.isArray(output) ? `${output.length} images` : 'Single image');
    
    // Преобразуем к массиву, если это не массив
    const outputArray = Array.isArray(output) ? output : [output];
    return outputArray as string[];
  } catch (error) {
    console.error('Error generating image with Replicate:', error);
    throw error;
  }
}

/**
 * Функция для генерации видео через API Replicate
 */
export async function generateVideoWithReplicate({
  prompt,
  start_image,
  model = 'luma/ray-flash-2-540p'
}: ReplicateInput): Promise<string> {
  console.log('Starting video generation with Replicate:', { prompt, start_image, model });
  
  // Создаем экземпляр клиента
  const replicate = new ReplicateClient();
  
  // Подготавливаем входные данные
  const input: any = { prompt };
  
  if (start_image) {
    input.start_image = start_image;
  }
  
  // Запускаем модель и получаем результат
  try {
    const output = await replicate.run(model, { input });
    console.log('Received video output from Replicate');
    
    // Возвращаем URL видео
    return Array.isArray(output) ? output[0] : output as string;
  } catch (error) {
    console.error('Error generating video with Replicate:', error);
    throw error;
  }
}

export default ReplicateClient; 