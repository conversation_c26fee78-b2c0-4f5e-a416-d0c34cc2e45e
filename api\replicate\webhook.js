// Replicate webhook endpoint: POST /api/replicate/webhook
// Replicate будет отправлять сюда POST-запросы при завершении генерации

export default async function handler(req, res) {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  try {
    console.log("[REPLICATE WEBHOOK] Вызван endpoint /api/replicate/webhook");
    // Replicate отправляет prediction объект в теле запроса
    const body = req.body && typeof req.body === "object" ? req.body : (() => {
      try {
        return JSON.parse(req.body || "{}");
      } catch {
        return {};
      }
    })();

    // Логируем входящий prediction
    console.log("[REPLICATE WEBHOOK] Получен prediction:", body);

    // Здесь можно реализовать сохранение результата в БД или отправку через WebSocket клиенту
    // Например, сохранить prediction.id и prediction.output в базу

    // Если output есть и статус succeeded — сохраняем в базу
    if (body.status === "succeeded" && body.output) {
      try {
        // Импортируем функции для сохранения генераций
        const module = await import("../utils/database.js");
        const saveVideoGeneration = module.saveVideoGeneration; // Для видео
        const saveImageGeneration = module.saveImageGeneration; // Для изображений

        const userId = (body.input && (body.input.userId || body.input.user_id || body.input.user_id_for_webhook));
        const replicatePredictionId = body.id; // <--- ПОЛУЧАЕМ ID ПРЕДСКАЗАНИЯ REPLICATE

        console.log(`[REPLICATE WEBHOOK] Processing prediction ID: ${replicatePredictionId}, User ID: ${userId}`);

        if (!userId) {
          console.error(`[REPLICATE WEBHOOK] Ошибка: user_id отсутствует для prediction ${replicatePredictionId}! Сохранение невозможно.`);
          // Не продолжаем без user_id
        } else if (!replicatePredictionId) {
          console.error(`[REPLICATE WEBHOOK] Ошибка: prediction.id отсутствует в теле вебхука! Сохранение невозможно.`);
          // Не продолжаем без replicatePredictionId
        }
        else {
          let modelName = (body.model || "").toLowerCase();
          console.log("[REPLICATE WEBHOOK] Model name detected:", modelName);
          console.log("[REPLICATE WEBHOOK] Full body.model:", body.model);

          // Определяем модель для сохранения, чтобы не было слишком длинных названий версий
          // Правильно определяем модель для сохранения
          let saveModel = body.model || undefined;
          if (modelName.includes("ideogram-v2a-turbo")) {
            saveModel = "ideogram-ai/ideogram-v2a-turbo";
          } else if (modelName.includes("ideogram-v3-turbo") || modelName.includes("hidream")) {
            saveModel = "ideogram-ai/ideogram-v3-turbo";
          } else if (modelName.includes("ideogram")) {
            saveModel = "replicate"; // Для старых ideogram моделей
          } else if (modelName.includes("minimax/image-01")) {
            saveModel = "minimax/image-01";
          } else if (modelName.includes("google/imagen-4")) {
            saveModel = "google/imagen-4";
          } else if (modelName.includes("bytedance/bagel")) {
            saveModel = "bytedance/bagel";
          } else if (modelName.includes("black-forest-labs/flux-canny-pro")) {
            saveModel = "black-forest-labs/flux-canny-pro";
          } else if (modelName.includes("black-forest-labs/flux-kontext-pro")) {
            saveModel = "black-forest-labs/flux-kontext-pro";
          } else if (modelName.includes("black-forest-labs/flux-kontext-max")) {
            saveModel = "black-forest-labs/flux-kontext-max";
          } else if (modelName.includes("pixverse/pixverse-v4.5")) {
            saveModel = "pixverse/pixverse-v4.5";
          } else if (modelName.includes("luma/ray-flash")) {
            saveModel = "luma/ray-flash";
          } else if (modelName.includes("kwaivgi/kling")) {
            saveModel = "kwaivgi/kling";
          }
          console.log("[REPLICATE WEBHOOK] Save model:", saveModel);

          const outputs = Array.isArray(body.output) ? body.output.filter(url => url) : (body.output ? [body.output] : []);

          const allImages = outputs.length > 0 && outputs.every(url =>
            typeof url === "string" &&
            !(url.endsWith(".mp4") || url.endsWith(".webm") || url.endsWith(".mp3") || url.endsWith(".wav") ||
              modelName.includes("video") || modelName.includes("kling") || modelName.includes("ray-flash") ||
              modelName.includes("speech"))
          );

          if (allImages) {
            const currentPrompt = body.input?.prompt || "";
            const currentStyle = body.input?.style || undefined;
            // Проверяем оба варианта ключа для aspect_ratio, так как Replicate может использовать оба
            const currentAspectRatio = body.input?.aspect_ratio || body.input?.aspectRatio || undefined;

            console.log("[REPLICATE WEBHOOK] upsertImageGeneration params:", {
              user_id: userId,
              prompt: currentPrompt,
              image_urls_from_replicate: outputs,
              style: currentStyle,
              aspect_ratio: currentAspectRatio,
              model: saveModel,
              replicate_prediction_id: replicatePredictionId // <--- ПЕРЕДАЕМ ID
            });

            // Сохраняем изображения в базу данных и списываем кредиты
            try {
              // Определяем стоимость в зависимости от модели
              let costPerImage = 2; // По умолчанию для together
              if (saveModel === 'replicate') costPerImage = 10; // ideogram модели (старые)
              else if (saveModel === 'ideogram-ai/ideogram-v2a-turbo') costPerImage = 10; // Ideogram 2
              else if (saveModel === 'ideogram-ai/ideogram-v3-turbo') costPerImage = 15; // Ideogram 3 (hidream)
              else if (saveModel === 'black-forest-labs/flux-canny-pro') costPerImage = 15; // Flux Pro
              else if (saveModel === 'black-forest-labs/flux-kontext-pro') costPerImage = 10; // Flux Kontext Pro
              else if (saveModel === 'black-forest-labs/flux-kontext-max') costPerImage = 25; // Flux Kontext Max
              else if (saveModel === 'minimax/image-01') costPerImage = 5; // Image-01
              else if (saveModel === 'google/imagen-4') costPerImage = 15; // Imagen-4
              else if (saveModel === 'bytedance/bagel') costPerImage = 25;

              const totalCost = costPerImage * outputs.length;

              // Сохраняем каждое изображение
              for (const imageUrl of outputs) {
                await saveImageGeneration({
                  user_id: userId,
                  prompt: body.input?.prompt || "",
                  image_url: imageUrl,
                  model: saveModel,
                  replicate_prediction_id: replicatePredictionId,
                  cost: costPerImage,
                  style: body.input?.metadata_style || body.input?.style || null,
                  aspect_ratio: body.input?.aspect_ratio || body.input?.aspectRatio || null,
                  visibility: body.input?.metadata_visibility || 'public'
                });
              }

              // Списываем кредиты
              const { updateUserCredits } = await import("../utils/database.js");
              await updateUserCredits(userId, -totalCost, 'generation', `Генерация ${outputs.length} изображений (${saveModel})`);

              console.log("[REPLICATE WEBHOOK] Сохранены ИЗОБРАЖЕНИЯ для user:", userId, "model:", saveModel, "urls:", outputs, "cost:", totalCost);
            } catch (error) {
              console.error("[REPLICATE WEBHOOK] Ошибка при сохранении изображений:", error);
            }

          } else if (modelName.includes("speech-02")) {
            // Обработка speech генераций
            console.log("[REPLICATE WEBHOOK] ✅ НАЙДЕНА SPEECH ГЕНЕРАЦИЯ!");
            console.log("[REPLICATE WEBHOOK] Обрабатываем SPEECH генерацию");
            console.log("[REPLICATE WEBHOOK] Model name:", modelName);
            console.log("[REPLICATE WEBHOOK] Body output:", body.output);
            console.log("[REPLICATE WEBHOOK] Body input:", body.input);

            const audioUrl = body.output; // Для speech output - это строка с URL аудио
            if (audioUrl && typeof audioUrl === "string") {
              try {
                console.log("[REPLICATE WEBHOOK] Найден аудио URL:", audioUrl);

                // Сначала списываем кредиты
                const text = body.input?.text || "";
                console.log("[REPLICATE WEBHOOK] Текст для расчета стоимости:", text.length, "символов");

                const { updateUserCredits } = await import("../utils/database.js");

                // Рассчитываем стоимость
                let cost = 0;
                if (saveModel.includes("speech-02-turbo")) {
                  cost = Math.ceil(text.length * 0.1);
                  console.log("[REPLICATE WEBHOOK] Стоимость turbo:", cost);
                } else if (saveModel.includes("speech-02-hd")) {
                  cost = Math.ceil(text.length * 0.2);
                  console.log("[REPLICATE WEBHOOK] Стоимость HD:", cost);
                }
                cost = Math.max(5, cost); // Минимум 5 кредитов
                console.log("[REPLICATE WEBHOOK] Финальная стоимость:", cost);

                // Списываем кредиты
                console.log("[REPLICATE WEBHOOK] Списываем кредиты...");
                await updateUserCredits(userId, -cost, 'debit', `Speech generation: ${saveModel}`);
                console.log(`[WEBHOOK CREDITS] Списано ${cost} кредитов за speech генерацию для user: ${userId}`);

                // Сохраняем генерацию
                console.log("[REPLICATE WEBHOOK] Сохраняем генерацию в БД...");
                const { saveSpeechGeneration } = await import("../utils/database.js");
                await saveSpeechGeneration({
                  user_id: userId,
                  text: text,
                  audio_url: audioUrl,
                  voice_id: body.input?.voice_id || "",
                  model: saveModel,
                  replicate_prediction_id: replicatePredictionId,
                  cost: cost
                });
                console.log("[REPLICATE WEBHOOK] Сохранена РЕЧЬ для user:", userId, "model:", saveModel, "url:", audioUrl);
              } catch (speechError) {
                console.error("[REPLICATE WEBHOOK] Ошибка обработки речи:", speechError);
                console.error("[REPLICATE WEBHOOK] Stack trace:", speechError.stack);
              }
            } else {
              console.log("[REPLICATE WEBHOOK] Аудио URL не найден или неверный тип:", typeof audioUrl, audioUrl);
            }
          } else if (outputs.length > 0) { // Если есть output, но не все картинки (значит, может быть видео)
            // Для видео — по одному
            for (const url of outputs) {
              if (!url) continue;
              const isVideo = typeof url === "string" && (url.endsWith(".mp4") || url.endsWith(".webm") || modelName.includes("video") || modelName.includes("kling") || modelName.includes("ray-flash") || modelName.includes("pixverse"));
              if (isVideo) {
                // ВРЕМЕННО ОТКЛЮЧАЕМ ПРОВЕРКУ СУЩЕСТВОВАНИЯ ДЛЯ ОТЛАДКИ
                console.log(`[REPLICATE WEBHOOK] Обрабатываем видео с prediction ID: ${replicatePredictionId}`);

                console.log("[REPLICATE WEBHOOK] saveVideoGeneration params:", {
                  user_id: userId,
                  prompt: body.input?.prompt || "",
                  video_url: url,
                  model: saveModel,
                  replicate_prediction_id: replicatePredictionId
                });
                // Определяем стоимость видео в зависимости от модели и параметров
                let videoCost = 100; // По умолчанию

                if (saveModel === 'pixverse/pixverse-v4.5') {
                  // Для pixverse нужно определить стоимость по параметрам
                  const duration = body.input?.duration || 5;
                  const quality = body.input?.quality || '540p';
                  const motionMode = body.input?.motion_mode || 'normal';
                  const costKey = `${duration}s_${quality}_${motionMode}`;

                  const pixverseCosts = {
                    '5s_360p_normal': 90,
                    '5s_360p_smooth': 180,
                    '5s_540p_normal': 90,
                    '5s_540p_smooth': 180,
                    '5s_720p_normal': 120,
                    '5s_720p_smooth': 240,
                    '5s_1080p_normal': 240,
                    '8s_360p_normal': 180,
                    '8s_540p_normal': 180,
                    '8s_720p_normal': 240
                  };

                  videoCost = pixverseCosts[costKey] || 90;
                } else if (saveModel === 'luma/ray-flash') {
                  const duration = body.input?.duration || 5;
                  videoCost = duration === 5 ? 100 : 160; // 5s = 100, 9s = 160
                } else if (saveModel === 'kwaivgi/kling') {
                  const duration = body.input?.duration || 5;
                  // Определяем тип Kling по полному названию модели
                  if (body.model && body.model.includes('standard')) {
                    videoCost = duration === 5 ? 150 : 300; // Standard: 5s = 150, 10s = 300
                  } else if (body.model && body.model.includes('pro')) {
                    videoCost = duration === 5 ? 300 : 600; // Pro: 5s = 300, 10s = 600
                  } else {
                    videoCost = duration === 5 ? 150 : 300; // По умолчанию Standard
                  }
                }

                await saveVideoGeneration({
                  user_id: userId,
                  prompt: body.input?.prompt || "",
                  video_url: url,
                  model: saveModel,
                  replicate_prediction_id: replicatePredictionId,
                  duration: body.input?.duration || null,
                  quality: body.input?.quality || null,
                  aspect_ratio: body.input?.aspect_ratio || null,
                  motion_mode: body.input?.motion_mode || null,
                  cost: videoCost,
                  visibility: body.input?.metadata_visibility || 'public' // Используем метаданные для приватности
                });

                // Списываем кредиты за видео
                const { updateUserCredits } = await import("../utils/database.js");
                await updateUserCredits(userId, -videoCost, 'generation', `Генерация видео (${saveModel})`);

                console.log("[REPLICATE WEBHOOK] Сохранено ВИДЕО для user:", userId, "model:", saveModel, "url:", url, "cost:", videoCost);
              }
            }
          }
        }
      } catch (err) {
        console.error("[REPLICATE WEBHOOK] Ошибка при сохранении в БД:", err);
      }
    }

    // Для теста просто возвращаем OK
    res.status(200).json({ received: true, id: body.id, status: body.status, output: body.output });
  } catch (error) {
    console.error("[REPLICATE WEBHOOK] Ошибка обработки:", error);
    res.status(500).json({ error: "Webhook error", details: error.message });
  }
}
