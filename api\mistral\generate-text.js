// Прокси для Mistral API (чат/инференс с поддержкой изображений)
import fetch from 'node-fetch';
import AbortController from 'abort-controller';

const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY;

export default async (req, res) => {
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  if (!MISTRAL_API_KEY) {
    res.status(500).json({ error: 'MISTRAL_API_KEY not set in environment' });
    return;
  }

  try {
    const { model, messages, temperature, max_tokens } = req.body;

    // Логируем тело запроса для диагностики
    console.log('Запрос к Mistral:', JSON.stringify({ model, messages, temperature, max_tokens }));

    // Преобразуем сообщения в формат, ожидаемый Mistral API
    const formattedMessages = messages.map(msg => {
      if (msg.images && msg.images.length > 0) {
        // Если есть изображения, content должен быть массивом
        const contentParts = [{ type: "text", text: msg.content }];
        msg.images.forEach(imageUrl => {
          contentParts.push({ type: "image_url", image_url: { url: imageUrl } });
        });
        return {
          role: msg.role,
          content: contentParts,
        };
      }
      return {
        role: msg.role,
        content: msg.content,
      };
    });

    // Таймаут для fetch (30 секунд)
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000);

    let mistralRes;
    try {
      mistralRes = await fetch('https://api.mistral.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${MISTRAL_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages: formattedMessages, // Используем отформатированные сообщения
          temperature,
          max_tokens,
        }),
        signal: controller.signal,
      });
    } finally {
      clearTimeout(timeout);
    }

    // Логируем статус и часть ответа
    console.log('Ответ Mistral status:', mistralRes.status);

    if (!mistralRes.ok) {
      const errorText = await mistralRes.text();
      console.error('Ошибка ответа Mistral:', errorText);

      // Специальная обработка ошибки 429 (превышение пропускной способности)
      if (mistralRes.status === 429) {
        try {
          const errorData = JSON.parse(errorText);
          let userMessage = 'Модель Mistral перегружена. Попробуйте позже или используйте другую модель.';

          // Определяем тип ошибки по сообщению
          if (errorData.message && errorData.message.includes('Service tier capacity exceeded')) {
            userMessage = 'Пропускная способность модели Mistral превышена. Система автоматически попробует другую модель.';
          } else if (errorData.message && errorData.message.includes('rate limit')) {
            userMessage = 'Превышен лимит запросов к Mistral. Попробуйте через несколько секунд.';
          }

          const userFriendlyMessage = {
            error: 'Mistral API перегружен',
            message: userMessage,
            code: 'CAPACITY_EXCEEDED',
            originalError: errorData.message,
            retryAfter: mistralRes.headers.get('retry-after') || '30'
          };
          res.status(429).json(userFriendlyMessage);
          return;
        } catch {
          // Если не удалось распарсить JSON, отправляем стандартное сообщение
          res.status(429).json({
            error: 'Mistral API перегружен',
            message: 'Модель Mistral временно недоступна. Система попробует другую модель.',
            code: 'CAPACITY_EXCEEDED'
          });
          return;
        }
      }

      res.status(mistralRes.status).send(errorText);
      return;
    }

    const data = await mistralRes.json();
    // Логируем часть успешного ответа
    console.log('Успешный ответ Mistral:', JSON.stringify(data).slice(0, 500));
    res.status(mistralRes.status).json(data);
  } catch (error) {
    console.error('Error in Mistral API proxy:', error);
    res.status(500).json({ error: 'Failed to communicate with Mistral API', details: error.message });
  }
};