import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { creditPlans, CreditPlan } from '@/utils/yukassa';
import { BuyCreditsDialog } from '@/components/ui/BuyCreditsDialog';
import { useAuth } from '@/context/AuthContext';
import { useTranslation } from 'react-i18next';

const PlanCard = ({
  plan,
  index,
  onBuyClick,
  t,
  lang
}: {
  plan: CreditPlan;
  index: number;
  onBuyClick: (plan: CreditPlan) => void;
  t: Function;
  lang: string;
}) => {
  const isPopular = plan.id === 'standard';
  // Определяем ключ для перевода
  let planKey = '';
  if (plan.id === 'basic') planKey = 'proPlan';
  else if (plan.id === 'standard') planKey = 'standardPlan';
  else if (plan.id === 'premium') planKey = 'enterprisePlan';
  else if (plan.id === 'custom') planKey = 'customPlan';

  // Цена: если en — доллары, если ru — рубли
  const price = plan.isCustomPlan
    ? (lang === 'en' ? 'from $20' : 'от 2000 ₽')
    : (lang === 'en' ? `$${Math.ceil(plan.amount / 100)}` : `${plan.amount} ₽`);

  // Название, описание, features, кнопка
  const title = t(`pricing.${planKey}.title`, plan.name);
  const description = t(`pricing.${planKey}.description`, plan.description);
  // Формируем features для всех тарифов
  const creditsStr = plan.isCustomPlan
    ? (lang === 'en' ? 'credits at rate $1 = 2 credits' : 'кредитов по курсу 1₽ = 2 кредита')
    : (lang === 'en' ? `${plan.credits} credits` : `${plan.credits} кредитов`);
  const genFeatures =
    lang === 'en'
      ? [
          'text2img (5 cr.)',
          'img2img (10 cr.)',
          'video (5s) (100 cr.)',
          'video (9s) (150 cr.)'
        ]
      : [
          'text2img (5 кр.)',
          'img2img (10 кр.)',
          'video (5с) (100 кр.)',
          'video (9с) (150 кр.)'
        ];
  let features: string[] = [creditsStr, ...genFeatures];
  const button = t(`pricing.${planKey}.button`, lang === 'en' ? 'Buy Now' : 'Купить');

  return (
    <div
      className={cn(
        "neo-glass rounded-xl p-6 animate-scale-in relative",
        index === 0
          ? "animation-delay-200"
          : index === 1
          ? "animation-delay-400"
          : index === 2
          ? "animation-delay-600"
          : "animation-delay-800",
        isPopular && "border-black"
      )}
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary px-4 py-1 rounded-full text-xs font-medium text-primary-foreground">
          {lang === 'en' ? 'Most Popular' : 'Самый популярный'}
        </div>
      )}

      <div className="text-center mb-6">
        <h3 className="text-xl font-bold mb-2 text-foreground">
          {title}
        </h3>
        <div className="text-3xl font-bold mb-2 text-foreground">
          {price}
        </div>
        <p className="text-foreground/70 text-sm">{description}</p>
      </div>

      <ul className="space-y-3 mb-6">
        {features.map((feature, i) => (
          <li key={i} className="flex items-start">
            <Check size={18} className="text-foreground mr-2 mt-0.5" />
            <span className="text-foreground/80 text-sm">{feature}</span>
          </li>
        ))}
      </ul>

      <button
        className={cn(
          "w-full py-2 rounded-md font-semibold transition-colors",
          isPopular
            ? "bg-primary hover:bg-primary/80 text-primary-foreground"
            : "bg-foreground/5 hover:bg-foreground/10 text-foreground"
        )}
        onClick={() => onBuyClick(plan)}
        type="button"
      >
        {button}
      </button>
    </div>
  );
};

const PricingSection = () => {
  const [selectedPlan, setSelectedPlan] = useState<CreditPlan | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const lang = i18n.language;

  const handleBuyClick = (plan: CreditPlan) => {
    if (!user) {
      navigate('/login');
      return;
    }

    // Для произвольного плана перенаправляем на дашборд с открытым sidebar
    if (plan.isCustomPlan) {
      navigate('/dashboard');
      // Небольшая задержка для загрузки страницы, затем открываем sidebar
      setTimeout(() => {
        // Эмулируем клик по кнопке покупки кредитов в sidebar
        const buyCreditsButton = document.querySelector('[data-testid="buy-credits-sidebar"]') as HTMLElement;
        if (buyCreditsButton) {
          buyCreditsButton.click();
        }
      }, 500);
      return;
    }

    setSelectedPlan(plan);
  };

  return (
    <section className="py-20 px-6 relative">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gradient">
            {t('pricing.sectionTitle', 'Тарифы и кредиты')}
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            {t('pricing.sectionSubtitle', 'Выберите подходящий тариф и получите нужное количество кредитов для генерации изображений и видео.')}
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-4 justify-center">
          {creditPlans.map((plan, index) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              index={index}
              onBuyClick={handleBuyClick}
              t={t}
              lang={lang}
            />
          ))}
        </div>

        <div className="text-center mt-10 text-black/60 text-sm">
          {t('pricing.footer', 'Оплата производится через ЮKassa. После покупки кредиты будут начислены на ваш аккаунт.')}
        </div>
      </div>
      {selectedPlan && user && (
        <BuyCreditsDialog
          triggerButtonClassName="hidden"
          variant="default"
          onSuccessfulPurchase={() => setSelectedPlan(null)}
        />
      )}
    </section>
  );
};

export default PricingSection;
