import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Wand2, Mic, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { getUserCredits, updateUserCredits } from '@/utils/database';
import AppLayout from '@/components/layout/AppLayout';
import DottedBackground from '@/components/ui/dotted-background';
import VoiceSelector from '@/components/speech/VoiceSelector';
import VoiceRecorder from '@/components/speech/VoiceRecorder';
import AudioPlayer from '@/components/speech/AudioPlayer';
import SpeechHistory from '@/components/speech/SpeechHistory';
import {
  generateSpeech,
  generateSpeechWithFal,
  cloneVoice,
  calculateSpeechCost,
  saveVoiceClone,
  getSpeechGenerations,
  getPredictionStatus,
  VOICE_CLONE_COST,
  EMOTIONS,
  LANGUAGE_BOOST_OPTIONS,
  SYSTEM_VOICES,
  ELEVENLABS_VOICES,
  SpeechParams,
  VoiceCloneParams
} from '@/utils/speechApi';
import { uploadImageToSupabaseStorage } from '@/utils/supabase';

interface Voice {
  id: string;
  name: string;
  type: 'system' | 'user' | 'community';
  category?: string;
}

interface SpeechHistoryItem {
  id: string;
  text: string;
  audio_url: string;
  voice_name: string;
  model: string;
  created_at: string;
  cost: number;
}

const SpeechGeneration = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const location = useLocation();

  // Основные состояния
  const [activeTab, setActiveTab] = useState<'text2speech' | 'voicecloning'>('text2speech');
  const [isGenerating, setIsGenerating] = useState(false);
  const [userCredits, setUserCredits] = useState(0);

  // Text to Speech состояния
  const [text, setText] = useState('');
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [model, setModel] = useState<'speech-02-turbo' | 'speech-02-hd' | 'elevenlabs-turbo-v2.5' | 'elevenlabs-multilingual-v2'>('speech-02-turbo');

  // Подхватываем model из query-параметра
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const modelFromQuery = params.get('model');

    if (modelFromQuery) {
      // Маппинг FAL API ID на внутренние ID
      if (modelFromQuery === 'fal-ai/elevenlabs/tts/turbo-v2.5') {
        setModel('elevenlabs-turbo-v2.5');
      } else if (modelFromQuery === 'fal-ai/elevenlabs/tts/multilingual-v2') {
        setModel('elevenlabs-multilingual-v2');
      }
    }
  }, [location.search]);

  // Автоматически выбираем подходящий голос при смене модели
  useEffect(() => {
    const isElevenLabsModel = model.includes('elevenlabs');

    if (isElevenLabsModel) {
      // Если выбрана ElevenLabs модель, но голос не из ElevenLabs - выбираем Rachel
      if (!selectedVoice || !ELEVENLABS_VOICES.find(v => v.id === selectedVoice.id)) {
        const defaultVoice = {
          id: 'Rachel',
          name: 'Rachel',
          type: 'system' as const,
          category: 'ElevenLabs'
        };
        setSelectedVoice(defaultVoice);
      }
    } else {
      // Если выбрана Minimax модель, но голос не из системных - выбираем Wise_Woman
      if (!selectedVoice || !SYSTEM_VOICES.find(v => v.id === selectedVoice.id)) {
        const defaultVoice = {
          id: 'Wise_Woman',
          name: 'Wise Woman',
          type: 'system' as const,
          category: 'female'
        };
        setSelectedVoice(defaultVoice);
      }
    }
  }, [model, selectedVoice]);
  const [speed, setSpeed] = useState([1]);
  const [pitch, setPitch] = useState([0]);
  const [volume, setVolume] = useState([1]);
  const [emotion, setEmotion] = useState('auto');
  const [languageBoost, setLanguageBoost] = useState('None');
  const [englishNormalization, setEnglishNormalization] = useState(false);
  const [generatedAudio, setGeneratedAudio] = useState<string | null>(null);

  // ElevenLabs specific parameters
  const [stability, setStability] = useState([0.5]);
  const [similarityBoost, setSimilarityBoost] = useState([0.75]);
  const [style, setStyle] = useState([0]);
  const [timestamps, setTimestamps] = useState(false);

  // Progress tracking for ElevenLabs generation
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStep, setGenerationStep] = useState(0);

  // Voice Cloning состояния
  const [voiceName, setVoiceName] = useState('');
  const [cloneModel, setCloneModel] = useState<'speech-02-turbo' | 'speech-02-hd'>('speech-02-turbo');
  const [accuracy, setAccuracy] = useState([0.7]);
  const [needNoiseReduction, setNeedNoiseReduction] = useState(false);
  const [needVolumeNormalization, setNeedVolumeNormalization] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);

  // История генераций
  const [speechHistory, setSpeechHistory] = useState<SpeechHistoryItem[]>([]);

  // Подхватываем текст из query-параметра
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const textFromQuery = params.get('text');
    if (textFromQuery) setText(textFromQuery);
  }, [location.search]);

  // Загрузка баланса пользователя
  useEffect(() => {
    const loadUserCredits = async () => {
      if (user?.id) {
        try {
          const credits = await getUserCredits(user.id);
          setUserCredits(Number(credits));
        } catch (error) {
          console.error('Ошибка загрузки баланса:', error);
          if (!(error instanceof Error && error.message.includes('Network error'))) {
            setUserCredits(0);
          }
        }
      }
    };

    loadUserCredits();

    // Слушатель события обновления кредитов
    const handleCreditsUpdated = (event: CustomEvent) => {
      const { userId, newBalance } = event.detail;
      if (user?.id === userId) {
        console.log(`Обновление баланса на странице речи: ${newBalance} кредитов`);
        setUserCredits(Number(newBalance));
      }
    };

    window.addEventListener('creditsUpdated', handleCreditsUpdated as EventListener);

    return () => {
      window.removeEventListener('creditsUpdated', handleCreditsUpdated as EventListener);
    };
  }, [user?.id]);

  // Загрузка истории генераций
  useEffect(() => {
    const loadSpeechHistory = async () => {
      if (user?.id) {
        try {
          console.log('Loading speech history for user:', user.id);
          const history = await getSpeechGenerations(user.id);
          console.log('Loaded speech history:', history);
          setSpeechHistory(history);
        } catch (error) {
          console.error('Error loading speech history:', error);
        }
      }
    };
    loadSpeechHistory();
  }, [user?.id]);

  // Расчет стоимости генерации речи
  const getGenerationCost = () => {
    if (activeTab === 'text2speech') {
      return calculateSpeechCost(text, model);
    } else {
      return VOICE_CLONE_COST;
    }
  };

  // Проверка достаточности кредитов
  const hasEnoughCredits = () => {
    const cost = getGenerationCost();
    return userCredits >= cost;
  };

  // Генерация речи
  const handleGenerateSpeech = async () => {
    if (!text.trim()) {
      toast.error(t('speechGen.errors.noText'));
      return;
    }

    if (!selectedVoice) {
      toast.error(t('speechGen.errors.noVoice'));
      return;
    }

    if (!user) {
      toast.error('Необходимо войти в аккаунт');
      return;
    }

    const cost = getGenerationCost();
    if (!hasEnoughCredits()) {
      toast.error(`Недостаточно токенов. Требуется: ${cost}, доступно: ${userCredits}`);
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStep(1);

    try {
      // Проверяем, является ли модель ElevenLabs
      const isElevenLabsModel = model.includes('elevenlabs');
      console.log('=== SPEECH GENERATION DEBUG ===');
      console.log('Model:', model);
      console.log('Is ElevenLabs model:', isElevenLabsModel);

      if (isElevenLabsModel) {
        console.log('=== CALLING generateSpeechWithFal ===');
        // Используем FAL API для ElevenLabs моделей
        const falModel = model === 'elevenlabs-turbo-v2.5'
          ? 'fal-ai/elevenlabs/tts/turbo-v2.5'
          : 'fal-ai/elevenlabs/tts/multilingual-v2';

        const audioUrl = await generateSpeechWithFal({
          text: text.trim(),
          voice: selectedVoice.id,
          model: falModel,
          userId: user.id,
          stability: stability[0],
          similarity_boost: similarityBoost[0],
          style: style[0],
          speed: speed[0],
          timestamps: timestamps,
          visibility: 'public', // TODO: добавить приватность
          onProgress: (progress: number, step: number) => {
            console.log(`Speech generation progress: ${progress}% (step ${step})`);
            setGenerationProgress(progress);
            setGenerationStep(step);
          }
        });

        setGeneratedAudio(audioUrl);
        toast.success('Генерация речи завершена!');

        // Обновляем историю и кредиты
        if (user?.id) {
          const updatedHistory = await getSpeechGenerations(user.id);
          setSpeechHistory(updatedHistory);
          const credits = await getUserCredits(user.id);
          setUserCredits(credits);
        }

      } else {
        // Используем Replicate API для старых моделей
        const params: SpeechParams = {
          text: text.trim(),
          voice_id: selectedVoice.id,
          model,
          userId: user.id,
          speed: speed[0],
          pitch: pitch[0],
          volume: volume[0],
          emotion,
          language_boost: languageBoost,
          english_normalization: englishNormalization
        };

        const response = await generateSpeech(params);

        if (response.status === 'starting') {
        // Показываем сообщение о том, что генерация запущена
        toast.success('Генерация речи запущена! Результат появится через несколько секунд.');
        
        // Если есть готовый аудио URL, показываем его
        if (response.audio_url) {
          setGeneratedAudio(response.audio_url);
        }
        
        // Периодически проверяем статус prediction
        const checkStatus = async () => {
          try {
            const statusResponse = await getPredictionStatus(response.prediction_id);
            console.log('Prediction status:', statusResponse);
            
            if (statusResponse.status === 'succeeded' && statusResponse.output) {
              setGeneratedAudio(statusResponse.output);
              toast.success('Генерация речи завершена!');
              
              // Даем webhook время обработать результат, затем обновляем историю и кредиты
              setTimeout(async () => {
                if (user?.id) {
                  const updatedHistory = await getSpeechGenerations(user.id);
                  setSpeechHistory(updatedHistory);
                  const credits = await getUserCredits(user.id);
                  setUserCredits(credits);
                }
              }, 1000); // Задержка 1 секунда для обработки webhook
              
              return true; // Останавливаем проверку
            } else if (statusResponse.status === 'failed') {
              toast.error('Ошибка генерации речи');
              return true; // Останавливаем проверку
            }
            return false; // Продолжаем проверку
          } catch (error) {
            console.error('Error checking prediction status:', error);
            return false;
          }
        };
        
        // Проверяем статус каждые 2 секунды, максимум 30 секунд
        let attempts = 0;
        const maxAttempts = 15;
        const interval = setInterval(async () => {
          attempts++;
          const shouldStop = await checkStatus();
          
          if (shouldStop || attempts >= maxAttempts) {
            clearInterval(interval);
            if (attempts >= maxAttempts) {
              // Финальная попытка обновить историю
              if (user?.id) {
                const updatedHistory = await getSpeechGenerations(user.id);
                setSpeechHistory(updatedHistory);
                const credits = await getUserCredits(user.id);
                setUserCredits(credits);
              }
            }
          }
        }, 2000);
        
      } else if (response.audio_url) {
        // Если сразу получили результат
        setGeneratedAudio(response.audio_url);
        if (user?.id) {
          const updatedHistory = await getSpeechGenerations(user.id);
          setSpeechHistory(updatedHistory);
          const credits = await getUserCredits(user.id);
          setUserCredits(credits);
        }
        toast.success(t('speechGen.success.generated'));
        }
      }

    } catch (error) {
      console.error('Speech generation error:', error);
      toast.error(error instanceof Error ? error.message : t('speechGen.errors.generation'));
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setGenerationStep(0);
    }
  };

  // Клонирование голоса
  const handleCloneVoice = async () => {
    if (!voiceName.trim()) {
      toast.error('Введите название голоса');
      return;
    }

    if (!recordedBlob) {
      toast.error(t('speechGen.errors.noRecording'));
      return;
    }

    if (!user) {
      toast.error('Необходимо войти в аккаунт');
      return;
    }

    const cost = VOICE_CLONE_COST;
    if (!hasEnoughCredits()) {
      toast.error(`Недостаточно токенов. Требуется: ${cost}, доступно: ${userCredits}`);
      return;
    }

    setIsGenerating(true);

    try {
      // Загружаем аудиофайл в Supabase Storage
      const audioUrl = await uploadImageToSupabaseStorage(
        user.id,
        recordedBlob,
        `voice-${Date.now()}.wav`
      );

      const params: VoiceCloneParams = {
        voice_file: audioUrl,
        model: cloneModel,
        accuracy: accuracy[0],
        need_noise_reduction: needNoiseReduction,
        need_volume_normalization: needVolumeNormalization
      };

      const response = await cloneVoice(params);

      // Сохраняем клонированный голос в базу данных
      await saveVoiceClone({
        user_id: user.id,
        name: voiceName.trim(),
        voice_id: response.voice_id,
        audio_url: audioUrl,
        model: cloneModel,
        accuracy: accuracy[0],
        public: true
      });

      // Списываем кредиты
      await updateUserCredits(
        user.id,
        -cost,
        'voice_cloning',
        `Клонирование голоса: ${voiceName}`
      );

      // Очищаем форму
      setVoiceName('');
      setRecordedBlob(null);

      toast.success(t('speechGen.success.cloned'));

    } catch (error) {
      console.error('Voice cloning error:', error);
      toast.error(error instanceof Error ? error.message : t('speechGen.errors.cloning'));
    } finally {
      setIsGenerating(false);
    }
  };

  // Обработка записи голоса
  const handleRecordingComplete = (blob: Blob, _duration: number) => {
    setRecordedBlob(blob);
  };

  const handleRecordingClear = () => {
    setRecordedBlob(null);
  };

  return (
    <AppLayout>
      <DottedBackground />
      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="flex flex-col xl:flex-row gap-6">
          {/* Основной контент */}
          <div className="flex-1">
            <div className="rounded-xl p-4 sm:p-6 mb-10 border bg-card text-card-foreground shadow-sm">
              <h1 className="text-2xl font-bold mb-6">{t('speechGen.title')}</h1>

              <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
                <TabsList className="mb-6">
                  <TabsTrigger value="text2speech" className="flex items-center gap-2">
                    <MessageSquare size={16} />
                    {t('speechGen.textToSpeech')}
                  </TabsTrigger>
                  <TabsTrigger value="voicecloning" className="flex items-center gap-2">
                    <Mic size={16} />
                    {t('speechGen.voiceCloningTab')}
                  </TabsTrigger>
                </TabsList>

                {/* Text to Speech */}
                <TabsContent value="text2speech" className="space-y-6">
                  {/* Текстовое поле */}
                  <div className="space-y-2">
                    <Label htmlFor="speech-text">{t('speechGen.textPlaceholder')}</Label>
                    <Textarea
                      id="speech-text"
                      placeholder={t('speechGen.textPlaceholder')}
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      className="min-h-[120px] resize-none"
                      maxLength={5000}
                    />
                    <div className="flex justify-between text-sm text-foreground/60">
                      <span>{text.length}/5000 символов</span>
                      <span>{t('speechGen.cost', { cost: getGenerationCost() })}</span>
                    </div>
                  </div>

                  {/* Выбор голоса */}
                  <div className="space-y-2">
                    <Label>{t('speechGen.selectVoice')}</Label>
                    <VoiceSelector
                      selectedVoice={selectedVoice}
                      onVoiceSelect={setSelectedVoice}
                      model={model}
                    />
                  </div>

                  {/* Параметры генерации */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Модель */}
                    <div className="space-y-2">
                      <Label>{t('speechGen.parameters.model')}</Label>
                      <Select value={model} onValueChange={(value: any) => setModel(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="speech-02-turbo">Minimax Speech 02 Turbo</SelectItem>
                          <SelectItem value="speech-02-hd">Minimax Speech 02 HD</SelectItem>
                          <SelectItem value="elevenlabs-turbo-v2.5">ElevenLabs Turbo v2.5</SelectItem>
                          <SelectItem value="elevenlabs-multilingual-v2">ElevenLabs Multilingual v2</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Скорость */}
                    <div className="space-y-2">
                      <Label>
                        {model.includes('elevenlabs') ? 'Скорость речи' : t('speechGen.parameters.speed')}: {speed[0]}
                      </Label>
                      <Slider
                        value={speed}
                        onValueChange={setSpeed}
                        min={model.includes('elevenlabs') ? 0.7 : 0.5}
                        max={model.includes('elevenlabs') ? 1.2 : 2}
                        step={0.1}
                        className="w-full"
                      />
                    </div>

                    {/* Высота тона / Стабильность */}
                    {model.includes('elevenlabs') ? (
                      <div className="space-y-2">
                        <Label>Стабильность голоса: {stability[0]}</Label>
                        <Slider
                          value={stability}
                          onValueChange={setStability}
                          min={0}
                          max={1}
                          step={0.1}
                          className="w-full"
                        />
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Label>{t('speechGen.parameters.pitch')}: {pitch[0]}</Label>
                        <Slider
                          value={pitch}
                          onValueChange={setPitch}
                          min={-12}
                          max={12}
                          step={1}
                          className="w-full"
                        />
                      </div>
                    )}

                    {/* Громкость / Similarity Boost */}
                    {model.includes('elevenlabs') ? (
                      <div className="space-y-2">
                        <Label>Similarity Boost: {similarityBoost[0]}</Label>
                        <Slider
                          value={similarityBoost}
                          onValueChange={setSimilarityBoost}
                          min={0}
                          max={1}
                          step={0.05}
                          className="w-full"
                        />
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Label>{t('speechGen.parameters.volume')}: {volume[0]}</Label>
                        <Slider
                          value={volume}
                          onValueChange={setVolume}
                          min={0}
                          max={10}
                          step={0.1}
                          className="w-full"
                        />
                      </div>
                    )}

                    {/* Эмоция / Style */}
                    {model.includes('elevenlabs') ? (
                      <div className="space-y-2">
                        <Label>Стиль (Style): {style[0]}</Label>
                        <Slider
                          value={style}
                          onValueChange={setStyle}
                          min={0}
                          max={1}
                          step={0.1}
                          className="w-full"
                        />
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Label>{t('speechGen.parameters.emotion')}</Label>
                        <Select value={emotion} onValueChange={setEmotion}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {EMOTIONS.map((emo) => (
                              <SelectItem key={emo.value} value={emo.value}>
                                {emo.label[i18n.language as 'ru' | 'en']}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Усиление языка - только для не-ElevenLabs моделей */}
                    {!model.includes('elevenlabs') && (
                      <div className="space-y-2">
                        <Label>{t('speechGen.parameters.languageBoost')}</Label>
                        <Select value={languageBoost} onValueChange={setLanguageBoost}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {LANGUAGE_BOOST_OPTIONS.map((lang) => (
                              <SelectItem key={lang.value} value={lang.value}>
                                {lang.label[i18n.language as 'ru' | 'en']}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  {/* Дополнительные настройки */}
                  <div className="space-y-3">
                    {/* English Normalization - только для не-ElevenLabs моделей */}
                    {!model.includes('elevenlabs') && (
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="english-normalization"
                          checked={englishNormalization}
                          onCheckedChange={setEnglishNormalization}
                        />
                        <Label htmlFor="english-normalization">
                          {t('speechGen.parameters.englishNormalization')}
                        </Label>
                      </div>
                    )}

                    {/* Timestamps - только для ElevenLabs моделей */}
                    {model.includes('elevenlabs') && (
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="timestamps"
                          checked={timestamps}
                          onCheckedChange={setTimestamps}
                        />
                        <Label htmlFor="timestamps">
                          Временные метки слов
                        </Label>
                      </div>
                    )}
                  </div>

                  {/* Кнопка генерации */}
                  <Button
                    onClick={handleGenerateSpeech}
                    disabled={isGenerating || !text.trim() || !selectedVoice || !hasEnoughCredits()}
                    className="w-full"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        {model.includes('elevenlabs') ? `Генерация... ${generationProgress}%` : 'Генерация...'}
                      </>
                    ) : (
                      <>
                        <Wand2 size={20} className="mr-2" />
                        {t('speechGen.generateSpeech')}
                      </>
                    )}
                  </Button>

                  {/* Прогресс-бар для ElevenLabs */}
                  {isGenerating && model.includes('elevenlabs') && (
                    <div className="space-y-2">
                      <div className="w-full bg-secondary rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${generationProgress}%` }}
                        />
                      </div>
                      <div className="text-sm text-muted-foreground text-center">
                        {generationStep === 1 && 'Отправка запроса...'}
                        {generationStep === 2 && 'Обработка текста...'}
                        {generationStep === 3 && 'Генерация аудио...'}
                        {generationStep === 4 && 'Финализация...'}
                        {generationStep === 5 && 'Сохранение результата...'}
                      </div>
                    </div>
                  )}

                  {/* Результат */}
                  {generatedAudio && (
                    <div className="mt-6">
                      <Label className="text-base font-semibold">Результат:</Label>
                      <div className="mt-2">
                        <AudioPlayer
                          src={generatedAudio}
                          title="Сгенерированная речь"
                          showDownload={true}
                        />
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Voice Cloning */}
                <TabsContent value="voicecloning" className="space-y-6">
                  {/* Название голоса */}
                  <div className="space-y-2">
                    <Label htmlFor="voice-name">{t('speechGen.voiceCloning.voiceName')}</Label>
                    <Input
                      id="voice-name"
                      placeholder={t('speechGen.voiceCloning.voiceNamePlaceholder')}
                      value={voiceName}
                      onChange={(e) => setVoiceName(e.target.value)}
                      maxLength={50}
                    />
                  </div>

                  {/* Запись голоса */}
                  <div className="space-y-2">
                    <Label>{t('speechGen.voiceCloning.recordingTitle')}</Label>
                    <VoiceRecorder
                      onRecordingComplete={handleRecordingComplete}
                      onRecordingClear={handleRecordingClear}
                    />
                  </div>

                  {/* Параметры клонирования */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Модель */}
                    <div className="space-y-2">
                      <Label>{t('speechGen.parameters.model')}</Label>
                      <Select value={cloneModel} onValueChange={(value: any) => setCloneModel(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="speech-02-turbo">Minimax Speech 02 Turbo</SelectItem>
                          <SelectItem value="speech-02-hd">Minimax Speech 02 HD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Точность */}
                    <div className="space-y-2">
                      <Label>Точность: {accuracy[0]}</Label>
                      <Slider
                        value={accuracy}
                        onValueChange={setAccuracy}
                        min={0}
                        max={1}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  </div>

                  {/* Дополнительные настройки */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="noise-reduction"
                        checked={needNoiseReduction}
                        onCheckedChange={setNeedNoiseReduction}
                      />
                      <Label htmlFor="noise-reduction">Шумоподавление</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="volume-normalization"
                        checked={needVolumeNormalization}
                        onCheckedChange={setNeedVolumeNormalization}
                      />
                      <Label htmlFor="volume-normalization">Нормализация громкости</Label>
                    </div>
                  </div>

                  {/* Стоимость */}
                  <div className="p-3 bg-muted rounded-lg">
                    <div className="text-sm text-foreground/70">
                      {t('speechGen.cloneCost')}
                    </div>
                  </div>

                  {/* Кнопка клонирования */}
                  <Button
                    onClick={handleCloneVoice}
                    disabled={isGenerating || !voiceName.trim() || !recordedBlob || !hasEnoughCredits()}
                    className="w-full"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        Клонирование...
                      </>
                    ) : (
                      <>
                        <Mic size={20} className="mr-2" />
                        {t('speechGen.cloneVoice')}
                      </>
                    )}
                  </Button>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Боковая панель с историей */}
          <div className="w-full xl:w-64 xl:ml-6 mb-6 xl:mb-0 xl:hidden">
            <SpeechHistory generations={speechHistory} />
            {/* Debug info */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                <strong>Debug:</strong> Speech history items: {speechHistory.length}
              </div>
            )}
          </div>
          <div className="w-64 ml-6 hidden xl:block">
            <SpeechHistory generations={speechHistory} />
            {/* Debug info */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                <strong>Debug:</strong> Speech history items: {speechHistory.length}
              </div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default SpeechGeneration;