import { toast } from 'sonner';

export interface FalVideoGenerationOptions {
  prompt: string;
  imageUrl?: string;
  duration?: number;
  aspectRatio?: string;
  negativePrompt?: string;
  cfgScale?: number;
  userId?: string;
  visibility?: 'public' | 'private';
  enhancePrompt?: boolean;
  generateAudio?: boolean;
  onProgress?: (progress: number, step: number) => void;
}

interface FalOptions {
  prompt: string;
  model: string;
  duration?: number;
  aspectRatio?: string;
  negativePrompt?: string;
  cfgScale?: number;
  imageUrl?: string;
  userId: string;
  visibility?: 'public' | 'private';
  enhancePrompt?: boolean;
  generateAudio?: boolean;
  onProgress?: (progress: number, step: number) => void;
}

// Используем наш бэкенд для работы с Fal API
const API_URL = (import.meta.env.VITE_API_URL?.split(',')[0] || 'http://localhost:3000').trim();

// Функция для получения базового URL
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return API_URL;
};

/**
 * Генерирует видео используя Fal API с моделями Kling 2.1
 */
export const generateVideoWithFal = async (options: FalOptions): Promise<string> => {
  console.log('Starting video generation with Fal:', options);

  try {
    const {
      prompt,
      model,
      duration = 5,
      aspectRatio = "16:9",
      negativePrompt = "blur, distort, and low quality",
      cfgScale = 0.5,
      imageUrl,
      userId,
      visibility = 'public',
      enhancePrompt = true,
      generateAudio = true
    } = options;

    // Ограничиваем промпт до 2500 символов только для Standard модели FAL API
    const isStandardModel = model.includes('standard');
    const truncatedPrompt = (isStandardModel && prompt.length > 2500) ? prompt.substring(0, 2500) : prompt;

    if (isStandardModel && prompt.length > 2500) {
      console.warn(`[FAL STANDARD] Prompt truncated from ${prompt.length} to 2500 characters`);
      toast.warning('Промпт слишком длинный и был сокращен до 2500 символов для Kling 2.1 Standard');
    }

    // Подготавливаем input для Fal API
    const input: any = {
      prompt: truncatedPrompt,
      duration: duration.toString(),
      aspect_ratio: aspectRatio
    };

    // Добавляем параметры только для моделей, которые их поддерживают
    if (model !== 'fal-ai/veo3') {
      input.negative_prompt = negativePrompt;
      input.cfg_scale = cfgScale;

      // Добавляем image_url только если он есть и модель поддерживает
      if (imageUrl) {
        input.image_url = imageUrl;
      }
    } else {
      // Для Veo3 добавляем специфичные параметры
      input.enhancePrompt = enhancePrompt;
      input.generateAudio = generateAudio;
    }

    console.log('Sending request to Fal API:', { model, input });

    // Определяем inputKeys в зависимости от модели
    const inputKeys = model === 'fal-ai/veo3'
      ? ['prompt', 'duration', 'aspect_ratio', 'enhancePrompt', 'generateAudio']
      : ['prompt', 'duration', 'aspect_ratio', 'negative_prompt', 'cfg_scale', 'image_url'];

    const requestBody = {
      model,
      userId,
      inputKeys,
      ...input
    };

    console.log('Full request body:', JSON.stringify(requestBody, null, 2));

    // Определяем правильный эндпоинт в зависимости от модели
    const apiEndpoint = model === 'fal-ai/veo3'
      ? `${getBaseUrl()}/api/fal/veo3`
      : `${getBaseUrl()}/api/fal-correct`;

    console.log('Using API endpoint:', apiEndpoint);

    // Отправляем запрос на правильный FAL эндпоинт
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (parseError) {
        const errorText = await response.text();
        console.error('Fal API error (text response):', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
      }

      console.error('Fal API error:', errorData);
      console.error('Response status:', response.status);
      console.error('Response headers:', Object.fromEntries(response.headers.entries()));

      // Проверяем на специфические ошибки обработки изображения
      if (errorData.error === 'Image processing failed') {
        throw new Error(errorData.message || 'Не удалось обработать входное изображение');
      }

      // Более детальная информация об ошибке
      const errorMessage = errorData.details || errorData.message || errorData.error || 'Failed to start video generation';
      throw new Error(`${errorMessage} (Status: ${response.status})`);
    }

    const result = await response.json();
    console.log('Fal API response:', result);

    if (!result.request_id) {
      throw new Error('No request ID received from Fal API');
    }

    // Запускаем клиентский поллинг для получения результата
    toast.info('Генерация видео запущена. Ожидайте результат...');

    try {
      const videoUrl = await pollFalStatus(result.request_id, model, userId, truncatedPrompt, visibility, options.onProgress, generateAudio);
      console.log('FAL generation completed, final URL:', videoUrl);
      toast.success('Видео успешно сгенерировано!');
      return videoUrl;
    } catch (pollError) {
      console.error('Error during FAL polling:', pollError);
      toast.error('Ошибка при генерации видео: ' + (pollError instanceof Error ? pollError.message : String(pollError)));
      throw pollError;
    }

  } catch (error) {
    console.error('Error in generateVideoWithFal:', error);
    console.error('Error name:', error instanceof Error ? error.name : 'Unknown');
    console.error('Error message:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    throw error;
  }
};

/**
 * Опрашивает статус генерации и возвращает URL готового видео
 */
async function pollFalStatus(
  requestId: string,
  model: string,
  userId?: string,
  prompt?: string,
  visibility?: string,
  onProgress?: (progress: number, step: number) => void,
  generateAudio?: boolean
): Promise<string> {
  const maxAttempts = 60; // 10 минут максимум (60 * 10 секунд)
  let attempt = 0;
  let retryDelay = 10000; // Начинаем с 10 секунд (как в фоновом поллинге)
  const maxRetryDelay = 15000; // Максимум 15 секунд

  while (attempt < maxAttempts) {
    try {
      console.log(`Polling Fal status (attempt ${attempt + 1}/${maxAttempts})`);

      // Обновляем прогресс на основе времени
      const progressPercent = Math.min(20 + (attempt / maxAttempts) * 70, 90); // От 20% до 90%
      const currentStep = attempt < 5 ? 2 : attempt < 15 ? 3 : attempt < 30 ? 4 : 5;

      if (onProgress) {
        onProgress(progressPercent, currentStep);
      }

      // Определяем правильный эндпоинт для проверки статуса
      const statusUrl = model === 'fal-ai/veo3'
        ? `${getBaseUrl()}/api/fal/veo3/status?requestId=${requestId}&model=${encodeURIComponent(model)}`
        : `${getBaseUrl()}/api/fal/status?requestId=${requestId}&model=${encodeURIComponent(model)}`;

      console.log(`FAL Status request URL: ${statusUrl}`);
      console.log(`FAL Status attempt ${attempt + 1}/${maxAttempts} for request ${requestId}`);

      // Проверяем статус
      const statusResponse = await fetch(statusUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!statusResponse.ok) {
        const errorText = await statusResponse.text();
        console.error(`FAL Status API error: ${statusResponse.status} ${statusResponse.statusText}`, errorText);

        if (statusResponse.status === 405) {
          throw new Error('API эндпоинт недоступен. Попробуйте позже или обратитесь в поддержку.');
        }

        throw new Error(`Status check failed: ${statusResponse.status} ${statusResponse.statusText}`);
      }

      const statusData = await statusResponse.json();
      console.log('Fal status:', statusData);

      if (statusData.status === 'COMPLETED') {
        console.log('Generation completed! Getting result...');

        // Устанавливаем прогресс на 95% при завершении генерации
        if (onProgress) {
          onProgress(95, 5);
        }

        // Для Veo3 вызываем result endpoint напрямую
        if (model === 'fal-ai/veo3') {
          console.log('Veo3 generation completed, getting result...');

          try {
            // Вызываем result endpoint для получения и сохранения результата
            // Стоимость зависит от параметра generateAudio: 1400 с аудио, 1000 без аудио
            const cost = generateAudio !== false ? 1400 : 1000;
            const resultUrl = `${getBaseUrl()}/api/fal/veo3/result?requestId=${requestId}&model=${encodeURIComponent(model)}&userId=${encodeURIComponent(userId || '')}&prompt=${encodeURIComponent(prompt || '')}&visibility=${encodeURIComponent(visibility || 'public')}&cost=${cost}&duration=8s`;

            console.log('Calling Veo3 result endpoint:', resultUrl);

            const resultResponse = await fetch(resultUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              }
            });

            if (!resultResponse.ok) {
              const errorText = await resultResponse.text();
              console.error('Veo3 result endpoint error:', errorText);
              throw new Error(`Result endpoint failed: ${resultResponse.status}`);
            }

            const resultData = await resultResponse.json();
            console.log('Veo3 result data:', resultData);

            // Устанавливаем прогресс на 100%
            if (onProgress) {
              onProgress(100, 5);
            }

            // Возвращаем URL видео
            return resultData.video_url;
          } catch (error) {
            console.error('Error getting Veo3 result:', error);
            throw error;
          }
        }

        // Получаем результат через наш API и сохраняем в БД (для других моделей)
        try {
          console.log('Getting result and saving to database...');

          const saveUrl = `${getBaseUrl()}/api/fal-save`;

          const saveResponse = await fetch(saveUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              requestId,
              model,
              userId,
              prompt,
              visibility
            })
          });

          if (!saveResponse.ok) {
            const errorData = await saveResponse.json();
            throw new Error(errorData.error || 'Failed to save video');
          }

          const saveData = await saveResponse.json();
          console.log('Video saved successfully:', saveData);
          console.log('Returning video URL:', saveData.videoUrl || saveData.url);

          // Устанавливаем прогресс на 100% при сохранении
          if (onProgress) {
            onProgress(100, 5);
          }

          // Возвращаем URL видео из нашей БД (Supabase Storage)
          const finalUrl = saveData.videoUrl || saveData.video_url || saveData.url;
          if (!finalUrl) {
            throw new Error('No video URL in save response');
          }
          return finalUrl;
        } catch (resultError) {
          console.error('Error getting result:', resultError);
          throw new Error('Failed to get generation result');
        }
      }

      if (statusData.status === 'FAILED') {
        const errorMessage = statusData.error || statusData.logs || 'Video generation failed';
        console.error('FAL generation failed:', errorMessage);
        console.error('Full status data:', statusData);
        throw new Error(`Генерация видео не удалась: ${errorMessage}`);
      }

      // Статус все еще "IN_QUEUE" или "IN_PROGRESS", ждем и опрашиваем снова
      console.log(`Status: ${statusData.status}, waiting ${retryDelay}ms before next check...`);

      // Если статус неизвестный, логируем полную информацию
      if (!['IN_QUEUE', 'IN_PROGRESS'].includes(statusData.status)) {
        console.warn('Unknown status received:', statusData);
      }

      await new Promise(resolve => setTimeout(resolve, retryDelay));

      // Увеличиваем задержку для следующей попытки
      retryDelay = Math.min(retryDelay * 1.2, maxRetryDelay);
      attempt++;

    } catch (error) {
      console.error(`Error during polling (attempt ${attempt + 1}):`, error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      // Если это ошибка 404 (запрос не найден), прекращаем попытки
      if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
        console.error('FAL request not found, stopping polling');
        throw new Error('Запрос на генерацию видео не найден. Возможно, он был отменен или истек.');
      }

      // Если это не последняя попытка, пробуем снова
      if (attempt < maxAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay = Math.min(retryDelay * 2, maxRetryDelay);
        attempt++;
      } else {
        throw new Error(`Превышено максимальное время ожидания генерации видео: ${errorMessage}`);
      }
    }
  }

  toast.error("Время ожидания генерации видео истекло");
  throw new Error('Timeout waiting for video generation');
}

/**
 * Определяет, какую модель Fal использовать на основе параметров
 */
export function getFalModelId(modelName: string, hasImage: boolean): string {
  switch (modelName) {
    case 'fal-ai/kling-video/v2.1/master':
      return hasImage
        ? 'fal-ai/kling-video/v2.1/master/image-to-video'
        : 'fal-ai/kling-video/v2.1/master/text-to-video';

    case 'fal-ai/kling-video/v2.1/pro':
      return 'fal-ai/kling-video/v2.1/pro/image-to-video';

    case 'fal-ai/kling-video/v2.1/standard':
      return 'fal-ai/kling-video/v2.1/standard/image-to-video';

    case 'fal-ai/veo3':
      return 'fal-ai/veo3'; // Veo3 использует одну модель для text-to-video

    default:
      throw new Error(`Unknown Fal model: ${modelName}`);
  }
}
