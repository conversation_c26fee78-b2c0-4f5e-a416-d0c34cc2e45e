import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import AppLayout from '@/components/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Wand2, Video, Download, Sparkles, Image, Film, Upload, X } from 'lucide-react';
import { cn, cropBase64ToAspectRatio, fileToBase64 } from '@/lib/utils';
import { Slider } from '@/components/ui/slider';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
import RecentGenerations from '@/components/generation/RecentGenerations';
import DottedBackground from '@/components/ui/dotted-background';
import generateVideoWithReplicate from '@/utils/replicateVideoGeneration';
import { generateVideoWithFal, getFalModelId } from '@/utils/falVideoGeneration';
import { uploadImageToSupabaseStorage, dataUriToBlob } from '@/utils/supabase';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { saveVideoGeneration, getVideoGenerations, getUserCredits } from '@/utils/database';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent } from '@/components/ui/dialog'; // Импорт Dialog и DialogContent
import { Checkbox } from '@/components/ui/checkbox';
import PrivateToggle from '@/components/ui/PrivateToggle';
import ShimmerEffect from '@/components/ui/ShimmerEffect';
import SEOHead from '@/components/SEOHead';

// Опции для pixverse модели
const pixverseStyles = [
  { value: 'None', label: { ru: 'Без стиля', en: 'None' } },
  { value: 'anime', label: { ru: 'Аниме', en: 'Anime' } },
  { value: '3d_animation', label: { ru: '3D Анимация', en: '3D Animation' } },
  { value: 'clay', label: { ru: 'Глина', en: 'Clay' } },
  { value: 'cyberpunk', label: { ru: 'Киберпанк', en: 'Cyberpunk' } },
  { value: 'comic', label: { ru: 'Комикс', en: 'Comic' } }
];

const pixverseEffects = [
  { value: 'None', label: { ru: 'Без эффекта', en: 'None' }, image: null },
  { value: "Let's YMCA!", label: { ru: 'YMCA танец', en: "Let's YMCA!" }, image: '/ymca.webp' },
  { value: 'Subject 3 Fever', label: { ru: 'Лихорадка субъекта 3', en: 'Subject 3 Fever' }, image: '/subject3fever.webp' },
  { value: 'Ghibli Live!', label: { ru: 'Живая студия Гибли', en: 'Ghibli Live!' }, image: '/GhibliLive.webp' },
  { value: 'Suit Swagger', label: { ru: 'Деловой стиль', en: 'Suit Swagger' }, image: '/suitswagger.webp' },
  { value: 'Muscle Surge', label: { ru: 'Мышечный всплеск', en: 'Muscle Surge' }, image: '/musclesurge.webp' },
  { value: '360° Microwave', label: { ru: '360° микроволновка', en: '360° Microwave' }, image: '/360microwave.webp' },
  { value: 'Warmth of Jesus', label: { ru: 'Тепло Иисуса', en: 'Warmth of Jesus' }, image: '/warmthofjesus.webp' },
  { value: 'Emergency Beat', label: { ru: 'Экстренный ритм', en: 'Emergency Beat' }, image: '/emergencybeat.webp' },
  { value: 'Anything, Robot', label: { ru: 'Что угодно, робот', en: 'Anything, Robot' }, image: '/anythingrobot.webp' },
  { value: 'Kungfu Club', label: { ru: 'Клуб кунг-фу', en: 'Kungfu Club' }, image: '/kungfuclub.webp' },
  { value: 'Mint in Box', label: { ru: 'Мята в коробке', en: 'Mint in Box' }, image: '/mintinbox.webp' },
  { value: 'Retro Anime Pop', label: { ru: 'Ретро аниме поп', en: 'Retro Anime Pop' }, image: '/retroanimepop.webp' },
  { value: 'Vogue Walk', label: { ru: 'Модная походка', en: 'Vogue Walk' }, image: '/voguewalk.webp' },
  { value: 'Mega Dive', label: { ru: 'Мега погружение', en: 'Mega Dive' }, image: '/megadive.webp' },
  { value: 'Evil Trigger', label: { ru: 'Злой триггер', en: 'Evil Trigger' }, image: '/eviltragger.webp' }
];

const qualityOptions = [
  { value: '360p', label: '360p' },
  { value: '540p', label: '540p' },
  { value: '720p', label: '720p' },
  { value: '1080p', label: '1080p' }
];

const motionModeOptions = [
  { value: 'normal', label: { ru: 'Обычный', en: 'Normal' } },
  { value: 'smooth', label: { ru: 'Плавный', en: 'Smooth' } }
];

// Функция для получения видео-превью модели
const getModelVideo = (modelId: string): string => {
  switch (modelId) {
    case 'luma/ray-flash-2-540p':
      return '/Ray2flash.webm';
    case 'kwaivgi/kling-v1.6-standard':
      return '/Klign1.6.webm';
    case 'kwaivgi/kling-v1.6-pro':
      return '/Kling1.6Pro.webm';
    case 'kwaivgi/kling-v2.0':
      return '/Kling2.webm';
    case 'pixverse/pixverse-v4.5':
      return '/Pixverse.webm';
    // Новые модели Fal Kling 2.1
    case 'Kling2.1Master':
      return '/Kling2.1Master.mp4';
    case 'Kling2.1Pro':
      return '/Kling2.1Pro.mp4';
    case 'Kling2.1Standart':
      return '/Kling2.1Standart.mp4';
    // Veo3 модель
    case 'veo3-banner':
      return '/veo3-banner.webm';
    default:
      return '/Ray2flash.webm';
  }
};

// Доступные модели для генерации видео
const videoModels = [
  {
    id: 'luma/ray-flash-2-540p',
    name: 'Ray Flash 2 (540p)',
    description: 'rayFlash2',
    supportedDurations: [5, 9],
    tokenCost: { 5: 100, 9: 160 },
    aspectRatios: ['1:1', '3:4', '4:3', '9:16', '16:9', '9:21', '21:9'],
    supportsEndImage: true,
    video: getModelVideo('luma/ray-flash-2-540p')
  },
  {
    id: 'kwaivgi/kling-v1.6-standard',
    name: 'Kling v1.6 Standard',
    description: 'kling16Standard',
    supportedDurations: [5, 10],
    tokenCost: { 5: 150, 10: 300 },
    aspectRatios: ['1:1', '9:16', '16:9'],
    supportsEndImage: false,
    video: getModelVideo('kwaivgi/kling-v1.6-standard')
  },
  {
    id: 'kwaivgi/kling-v1.6-pro',
    name: 'Kling v1.6 Pro',
    description: 'kling16Pro',
    supportedDurations: [5, 10],
    tokenCost: { 5: 300, 10: 600 },
    aspectRatios: ['1:1', '9:16', '16:9'],
    supportsEndImage: true,
    video: getModelVideo('kwaivgi/kling-v1.6-pro')
  },
  {
    id: 'kwaivgi/kling-v2.0',
    name: 'Kling v2.0',
    description: 'kling20',
    supportedDurations: [5, 10],
    tokenCost: { 5: 400, 10: 800 },
    aspectRatios: ['1:1', '9:16', '16:9'],
    supportsEndImage: false,
    supportsCfgScale: true,
    supportsNegativePrompt: true,
    video: getModelVideo('kwaivgi/kling-v2.0')
  },
  {
    id: 'pixverse/pixverse-v4.5',
    name: 'Pixverse v4.5',
    description: 'pixverse',
    supportedDurations: [5, 8],
    supportedQualities: ['360p', '540p', '720p', '1080p'],
    supportedMotionModes: ['normal', 'smooth'],
    aspectRatios: ['16:9', '9:16', '1:1'],
    supportsEndImage: true,
    supportsStyle: true,
    supportsEffect: true,
    supportsNegativePrompt: true,
    supportsSeed: true,
    video: getModelVideo('pixverse/pixverse-v4.5'),
    // Готовая таблица ценообразования для максимальной скорости
    tokenCost: {
      '5s_360p_normal': 90,
      '5s_360p_smooth': 180,
      '5s_540p_normal': 90,
      '5s_540p_smooth': 180,
      '5s_720p_normal': 120,
      '5s_720p_smooth': 240,
      '5s_1080p_normal': 240,
      '8s_360p_normal': 180,
      '8s_540p_normal': 180,
      '8s_720p_normal': 240
    }
  },
  // Новые модели Fal Kling 2.1
  {
    id: 'fal-ai/kling-video/v2.1/standard',
    name: 'Kling v2.1 Standard',
    description: 'kling21Standard',
    supportedDurations: [5, 10],
    tokenCost: { 5: 75, 10: 150 },
    aspectRatios: ['16:9', '9:16', '1:1'],
    supportsEndImage: false,
    supportsCfgScale: true,
    supportsNegativePrompt: true,
    supportsTextToVideo: false,
    supportsImageToVideo: true,
    requiresImage: true,
    provider: 'fal',
    video: getModelVideo('Kling2.1Standart'),
    isNew: true
  },
  {
    id: 'fal-ai/kling-video/v2.1/pro',
    name: 'Kling v2.1 Pro',
    description: 'kling21Pro',
    supportedDurations: [5, 10],
    tokenCost: { 5: 120, 10: 240 },
    aspectRatios: ['16:9', '9:16', '1:1'],
    supportsEndImage: false,
    supportsCfgScale: true,
    supportsNegativePrompt: true,
    supportsTextToVideo: false,
    supportsImageToVideo: true,
    requiresImage: true,
    provider: 'fal',
    video: getModelVideo('Kling2.1Pro'),
    isNew: true
  },
  {
    id: 'fal-ai/kling-video/v2.1/master',
    name: 'Kling v2.1 Master',
    description: 'kling21Master',
    supportedDurations: [5, 10],
    tokenCost: { 5: 320, 10: 640 },
    aspectRatios: ['16:9', '9:16', '1:1'],
    supportsEndImage: false,
    supportsCfgScale: true,
    supportsNegativePrompt: true,
    supportsTextToVideo: true,
    supportsImageToVideo: true,
    provider: 'fal',
    video: getModelVideo('Kling2.1Master'),
    isNew: true
  },
  // Новая модель Veo3
  {
    id: 'fal-ai/veo3',
    name: 'Veo3',
    description: 'veo3',
    supportedDurations: [8], // Veo3 поддерживает только 8 секунд
    tokenCost: { 8: 1400 }, // Фиксированная стоимость 1400 кредитов за 8 секунд с аудио
    aspectRatios: ['16:9'], // Veo3 поддерживает только 16:9
    supportsEndImage: false,
    supportsTextToVideo: true,
    supportsImageToVideo: false, // Veo3 пока только text-to-video
    provider: 'fal',
    video: getModelVideo('veo3-banner'),
    isNew: true,
    hasAudio: true // Указываем что модель генерирует звук
  },
];

interface CommunityVideo {
  id: string;
  videoUrl: string;
  thumbnailUrl?: string;
  prompt: string;
  author: string;
  timestamp: Date;
}

const CAMERA_CONCEPTS = [
  { value: "truck_left", ru: "Сдвиг влево", en: "Truck Left" },
  { value: "pan_right", ru: "Панорама вправо", en: "Pan Right" },
  { value: "pedestal_down", ru: "Опускание камеры", en: "Pedestal Down" },
  { value: "low_angle", ru: "Низкий ракурс", en: "Low Angle" },
  { value: "pedestal_up", ru: "Подъем камеры", en: "Pedestal Up" },
  { value: "selfie", ru: "Селфи", en: "Selfie" },
  { value: "pan_left", ru: "Панорама влево", en: "Pan Left" },
  { value: "roll_right", ru: "Поворот вправо", en: "Roll Right" },
  { value: "zoom_in", ru: "Приближение", en: "Zoom In" },
  { value: "over_the_shoulder", ru: "Через плечо", en: "Over the Shoulder" },
  { value: "orbit_right", ru: "Орбита вправо", en: "Orbit Right" },
  { value: "orbit_left", ru: "Орбита влево", en: "Orbit Left" },
  { value: "static", ru: "Статичная камера", en: "Static" },
  { value: "tiny_planet", ru: "Маленькая планета", en: "Tiny Planet" },
  { value: "high_angle", ru: "Высокий ракурс", en: "High Angle" },
  { value: "bolt_cam", ru: "Болт-камера", en: "Bolt Cam" },
  { value: "dolly_zoom", ru: "Долли-зум", en: "Dolly Zoom" },
  { value: "overhead", ru: "Вид сверху", en: "Overhead" },
  { value: "zoom_out", ru: "Отдаление", en: "Zoom Out" },
  { value: "handheld", ru: "С рук", en: "Handheld" },
  { value: "roll_left", ru: "Поворот влево", en: "Roll Left" },
  { value: "pov", ru: "От первого лица", en: "POV" },
  { value: "aerial_drone", ru: "Дрон", en: "Aerial Drone" },
  { value: "push_in", ru: "Въезд", en: "Push In" },
  { value: "crane_down", ru: "Кран вниз", en: "Crane Down" },
  { value: "truck_right", ru: "Сдвиг вправо", en: "Truck Right" },
  { value: "tilt_down", ru: "Наклон вниз", en: "Tilt Down" },
  { value: "elevator_doors", ru: "Двери лифта", en: "Elevator Doors" },
  { value: "tilt_up", ru: "Наклон вверх", en: "Tilt Up" },
  { value: "ground_level", ru: "Уровень земли", en: "Ground Level" },
  { value: "pull_out", ru: "Отъезд", en: "Pull Out" },
  { value: "aerial", ru: "Аэро", en: "Aerial" },
  { value: "crane_up", ru: "Кран вверх", en: "Crane Up" },
  { value: "eye_level", ru: "На уровне глаз", en: "Eye Level" }
];

const VideoGeneration = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const [prompt, setPrompt] = useState("");
  const location = useLocation();

  // Подхватываем prompt и model из query-параметров
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const promptFromQuery = params.get('prompt');
    const modelFromQuery = params.get('model');

    if (promptFromQuery) setPrompt(promptFromQuery);

    if (modelFromQuery) {
      // Проверяем, что модель существует в списке доступных моделей
      const validModel = videoModels.find(m => m.id === modelFromQuery);
      if (validModel) {
        setSelectedModel(modelFromQuery);
        // Автоматически переключаем на img2video для моделей Standard и Pro
        if (modelFromQuery === 'fal-ai/kling-video/v2.1/standard' || modelFromQuery === 'fal-ai/kling-video/v2.1/pro') {
          setGenerationMode('img2video');
        }

        // Автоматически переключаем на text2video для Veo3
        if (modelFromQuery === 'fal-ai/veo3') {
          setGenerationMode('text2video');
        }
      }
    }
  }, [location.search]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStep, setGenerationStep] = useState(0);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStartTime, setGenerationStartTime] = useState<number | null>(null);
  const [selectedCommunityVideo] = useState<CommunityVideo | null>(null);
  const [generatedVideos, setGeneratedVideos] = useState<string[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [duration, setDuration] = useState(5);
  const [aspectRatio, setAspectRatio] = useState("16:9");
  const [selectedModel, setSelectedModel] = useState(videoModels[0].id);
  const [generationMode, setGenerationMode] = useState("text2video");
  const [startImage, setStartImage] = useState<File | null>(null);
  const [endImage, setEndImage] = useState<File | null>(null);

  // Новые состояния для ray flash 2
  const [loop, setLoop] = useState(false);
  const [concepts, setConcepts] = useState<string[]>([]);

  // Новые состояния для pixverse модели
  const [quality, setQuality] = useState("540p");
  const [motionMode, setMotionMode] = useState("normal");
  const [pixverseStyle, setPixverseStyle] = useState("None");
  const [effect, setEffect] = useState("None");
  const [negativePrompt, setNegativePrompt] = useState("");

  // Новые состояния для Kling v2.0
  const [cfgScale, setCfgScale] = useState(7.5);
  const [startImageUrl, setStartImageUrl] = useState<string | null>(null);
  const [endImageUrl, setEndImageUrl] = useState<string | null>(null);
  const [userCredits, setUserCredits] = useState(0);
  const [isLoadingCredits, setIsLoadingCredits] = useState(false);

  // Состояние для приватности генерации
  const [isPrivate, setIsPrivate] = useState<boolean>(false);

  // Новые состояния для Veo3 параметров
  const [enhancePrompt, setEnhancePrompt] = useState<boolean>(true);
  const [generateAudio, setGenerateAudio] = useState<boolean>(true);
  const startImageInputRef = useRef<HTMLInputElement>(null);
  const endImageInputRef = useRef<HTMLInputElement>(null);
  const [recentGenerations, setRecentGenerations] = useState<Array<{
    id: string;
    type: "video";
    prompt: string;
    result: string;
    timestamp: Date;
    local: boolean;
    thumbnail_url?: string;
  }>>([]);
  const [communityVideos, setCommunityVideos] = useState<CommunityVideo[]>([]);
  // Состояние для модального окна Community Showcase
  const [isCommunityModalOpen, setIsCommunityModalOpen] = useState(false);
  // удалено: selectedCommunityVideo, setSelectedCommunityVideo

  // Предопределенные стили видео
  const videoStyleOptions = [
    { id: 'cinematic', name: i18n.language === 'ru' ? 'Кинематографичный' : 'Cinematic', prompt: 'cinematic film quality, professional cinematography, perfect dynamic lighting, shallow depth of field, anamorphic lens flare, Arri Alexa camera, dramatic composition, Hollywood-grade production' },
    { id: 'animation', name: i18n.language === 'ru' ? 'Анимация' : 'Animation', prompt: 'high-quality 3D animation, Pixar/Dreamworks style, fluid motion, vibrant colors, detailed texturing, expressive characters, polished rendering, subtle ambient occlusion, professional character rigging' },
    { id: 'drone', name: i18n.language === 'ru' ? 'Дрон' : 'Drone', prompt: 'breathtaking aerial drone footage, stabilized gimbal movement, birds-eye perspective, sweeping landscape views, 4K resolution, perfect exposure, majestic scale, smooth tracking shots, professional color grading' },
    { id: 'timelapse', name: i18n.language === 'ru' ? 'Таймлапс' : 'Timelapse', prompt: 'stunning time-lapse photography, dramatic day-to-night transition, cloud movement, light trails, hypersmooth frame blending, high dynamic range, accelerated motion, seamless transitions, professional intervalometer work' },
    { id: 'vintage', name: i18n.language === 'ru' ? 'Винтаж' : 'Vintage', prompt: 'nostalgic vintage film aesthetic, authentic 8mm/16mm film grain, slightly faded colors, light leaks, subtle scratches, rounded corners, warm color temperature, retro lens characteristics, analog projection feeling' },
    { id: 'futuristic', name: i18n.language === 'ru' ? 'Футуристичный' : 'Futuristic', prompt: 'cutting-edge sci-fi aesthetics, neon cyberpunk lighting, holographic interfaces, advanced technology, futuristic cityscape, sleek design, vibrant blue and purple highlights, lens flares, high-tech environment, digital particles' },
    { id: 'nature', name: i18n.language === 'ru' ? 'Природа' : 'Nature', prompt: 'stunning natural world footage, pristine wilderness, breathtaking landscapes, lush forests, flowing waters, dramatic natural lighting, wildlife in natural habitats, vibrant ecosystem, detailed macro photography, natural color grading' },
    { id: 'documentary', name: i18n.language === 'ru' ? 'Документальный' : 'Documentary', prompt: 'professional documentary film style, observational camera work, journalistic approach, realistic lighting conditions, natural sound design, interview framing, authentic real-world settings, narrative storytelling, informative visual language' },
    { id: 'noir', name: i18n.language === 'ru' ? 'Нуар' : 'Noir', prompt: 'classic film noir style, high-contrast black and white, dramatic shadows, low-key lighting, venetian blinds shadow patterns, mysterious atmosphere, dramatic camera angles, smoky environment, rain-slick streets, psychological tension' },
    { id: 'comedy', name: i18n.language === 'ru' ? 'Комедия' : 'Comedy', prompt: 'lighthearted comedy film style, bright high-key lighting, vibrant colors, dynamic camera movement, slightly exaggerated expressions, humorous visual cues, playful composition, warm color palette, energetic pacing, whimsical elements' },
    { id: 'horror', name: i18n.language === 'ru' ? 'Хоррор' : 'Horror', prompt: 'atmospheric horror film aesthetic, dark shadowy lighting, unsettling composition, eerie color grading, suspenseful framing, creepy environment details, subtle disturbing elements, tension-building camera work, atmospheric fog, disorienting perspectives' },
    { id: 'scifi', name: i18n.language === 'ru' ? 'Научная фантастика' : 'Science Fiction', prompt: 'high-tech science fiction aesthetic, futuristic technology, advanced spacecraft, alien worlds, exotic planetary landscapes, advanced civilizations, innovative interfaces, sleek design language, dramatic space lighting, speculative future vision' },
    { id: 'anime', name: i18n.language === 'ru' ? 'Аниме' : 'Anime', prompt: 'Japanese anime style animation, bold colors, stylized characters, dynamic motion lines, expressive emotions, detailed backgrounds, characteristic visual effects, dramatic lighting, exaggerated action sequences, distinctive art style' },
  ];

  // Получаем выбранную модель из списка
  const selectedModelDetails = videoModels.find(m => m.id === selectedModel) || videoModels[0];

  // Функция для получения переведенного описания модели
  const getModelDescription = (descriptionKey: string) => {
    return t(`videoGen.models.${descriptionKey}`);
  };

  // Обработчик изменения модели
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);

    // Проверяем и обновляем длительность, если текущая не поддерживается новой моделью
    const newModelDetails = videoModels.find(m => m.id === modelId);
    if (newModelDetails) {
      if (!newModelDetails.supportedDurations.includes(duration)) {
        setDuration(newModelDetails.supportedDurations[0]); // Устанавливаем первую поддерживаемую длительность
      }

      // Проверяем соотношение сторон
      if (!newModelDetails.aspectRatios.includes(aspectRatio)) {
        setAspectRatio(newModelDetails.aspectRatios[0]); // Устанавливаем первое поддерживаемое соотношение
      }

      // Автоматически переключаем на img2video для моделей Standard и Pro
      if (modelId === 'fal-ai/kling-video/v2.1/standard' || modelId === 'fal-ai/kling-video/v2.1/pro') {
        setGenerationMode('img2video');
      }

      // Автоматически переключаем на text2video для Veo3 (только text-to-video)
      if (modelId === 'fal-ai/veo3') {
        setGenerationMode('text2video');
      }

      // Сбрасываем параметры при переключении моделей
      if (modelId === 'pixverse/pixverse-v4.5') {
        // Устанавливаем дефолтные значения для pixverse
        setQuality('540p');
        setMotionMode('normal');
        setPixverseStyle('None');
        setEffect('None');
        setNegativePrompt('');
        setCfgScale(7.5); // Сбрасываем cfgScale
      } else if (modelId === 'kwaivgi/kling-v2.0' || newModelDetails?.provider === 'fal') {
        // Устанавливаем дефолтные значения для Kling v2.0 и Fal моделей
        setCfgScale(0.5); // Fal использует диапазон 0-1
        setNegativePrompt('blur, distort, and low quality');
        // Сбрасываем pixverse параметры
        setQuality('540p');
        setMotionMode('normal');
        setPixverseStyle('None');
        setEffect('None');
      } else {
        // Сбрасываем все специфичные параметры при переключении на другие модели
        setQuality('540p');
        setMotionMode('normal');
        setPixverseStyle('None');
        setEffect('None');
        setNegativePrompt('');
        setCfgScale(7.5);
      }
    }
  };

  // Функция для получения промпта с учетом выбранного стиля
  const getEnhancedPrompt = () => {
    // Для pixverse модели добавляем дефолтный промт если пустой
    let finalPrompt = prompt;
    if (selectedModel === 'pixverse/pixverse-v4.5' && (!prompt || prompt.trim() === '')) {
      finalPrompt = 'A beautiful cinematic video scene';
    }

    if (!selectedStyle) return finalPrompt;
    const style = videoStyleOptions.find(s => s.id === selectedStyle);
    if (!style) return finalPrompt;
    return `${finalPrompt}. ${style.prompt}`;
  };

  // Загружаем предыдущие видеогенерации при монтировании компонента
  useEffect(() => {
    const loadUserGenerations = async () => {
      if (user?.id) {
        try {
          // Загружаем генерации пользователя
          const userGenerations = await getVideoGenerations(user.id);
          if (userGenerations && userGenerations.length > 0) {
            const formattedGenerations = userGenerations.map(gen => ({
              id: gen.id,
              type: "video" as const,
              prompt: gen.prompt,
              result: gen.video_url,
              timestamp: gen.timestamp,
              local: gen.local || false,
              thumbnail_url: gen.thumbnail_url // Добавляем thumbnail_url
            }));
            setRecentGenerations(formattedGenerations);
          }

          // Загружаем данные для Community Showcase
          // В реальном приложении это будет API вызов
          // Пока используем данные пользователя с небольшой модификацией для демонстрации
          if (userGenerations && userGenerations.length > 0) {
            const showcaseVideos = userGenerations.map(gen => ({
              id: gen.id,
              videoUrl: gen.video_url,
              thumbnailUrl: gen.thumbnail_url || '/default-thumbnail.png',
              prompt: gen.prompt,
              author: user.email?.split('@')[0] || 'anonymous',
              timestamp: gen.timestamp,
            }));
            setCommunityVideos(showcaseVideos);
          }
        } catch (error) {
          console.error('Ошибка загрузки генераций:', error);
        }
      }
    };

    loadUserGenerations();
  }, [user?.id, user?.email]);

  // Загружаем баланс пользователя
  useEffect(() => {
    const loadUserCredits = async () => {
      if (user?.id) {
        setIsLoadingCredits(true);
        try {
          const credits = await getUserCredits(user.id);
          setUserCredits(Number(credits));
        } catch (error) {
          console.error('Ошибка загрузки баланса:', error);

          // При сетевых ошибках не изменяем значение
          if (!(error instanceof Error && error.message.includes('Network error'))) {
            setUserCredits(0);
          }
        } finally {
          setIsLoadingCredits(false);
        }
      }
    };

    loadUserCredits();

    // Слушатель события обновления кредитов
    const handleCreditsUpdated = (event: CustomEvent) => {
      const { userId, newBalance } = event.detail;
      if (user?.id === userId) {
        console.log(`Обновление баланса на странице видео: ${newBalance} кредитов`);
        setUserCredits(Number(newBalance));
      }
    };

    // Добавляем слушатель события
    window.addEventListener('creditsUpdated', handleCreditsUpdated as EventListener);

    return () => {
      window.removeEventListener('creditsUpdated', handleCreditsUpdated as EventListener);
    };
  }, [user?.id]);

  // Подгружаем больше видео при скроллинге
  useEffect(() => {
    const handleScroll = () => {
      // if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 500) {
      //   setVisibleVideos(prev => Math.min(prev + 2, communityVideos.length));
      // }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [communityVideos.length]);

  // Обработка выбора изображений
  const handleStartImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setStartImage(file);
      setStartImageUrl(URL.createObjectURL(file));
    }
  };

  const handleEndImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setEndImage(file);
      setEndImageUrl(URL.createObjectURL(file));
    }
  };

  // Обработка удаления изображений
  const handleRemoveStartImage = () => {
    setStartImage(null);
    setStartImageUrl(null);
    if (startImageInputRef.current) {
      startImageInputRef.current.value = '';
    }
  };

  const handleRemoveEndImage = () => {
    setEndImage(null);
    setEndImageUrl(null);
    if (endImageInputRef.current) {
      endImageInputRef.current.value = '';
    }
  };

  // Функция для загрузки изображения на сервер
  // Функция для загрузки изображения на сервер (теперь в Supabase Storage)
  // Возвращает data URL для передачи в API (если сервер поддерживает)
  // Удалено: uploadImageToServer — больше не используется
  // Получение стоимости в токенах
  const getGenerationCost = () => {
    // Для генерации видео
    if (generationMode === 'text2video' || generationMode === 'img2video') {
      const modelDetails = videoModels.find(m => m.id === selectedModel);
      if (!modelDetails) return 0;

      // Специальная обработка для Veo3 модели с динамическим ценообразованием
      if (selectedModel === 'fal-ai/veo3') {
        // Базовая стоимость 1400 кредитов с аудио, 1000 без аудио
        return generateAudio ? 1400 : 1000;
      }

      // Специальная обработка для pixverse модели
      if (selectedModel === 'pixverse/pixverse-v4.5') {
        const costKey = `${duration}s_${quality}_${motionMode}`;
        const cost = (modelDetails.tokenCost as any)[costKey];
        return cost || 0;
      }

      // Для остальных моделей - стандартная логика
      const exactCost = modelDetails.tokenCost[duration as 5 | 8 | 9 | 10];
      if (exactCost !== undefined) return exactCost;

      return 0;
    }

    // Для генерации изображений (если будет добавлено в будущем)
    return 0;
  };

  // Проверка достаточно ли токенов
  const hasEnoughCredits = () => {
    const cost = getGenerationCost();
    return userCredits >= cost;
  };

  const generateVideos = async () => {
    if (isGenerating) return; // Защита от повторного вызова
    // Проверка достаточно ли токенов
    const generationCost = getGenerationCost();
    if (user?.id && !hasEnoughCredits()) {
      toast.error(`Недостаточно токенов. Требуется: ${generationCost}, доступно: ${userCredits}`);
      return;
    }

    if (!prompt && generationMode === "text2video") {
      toast.error(t('videoGen.error.noPrompt'));
      return;
    }

    // Проверяем необходимость изображения для моделей, которые требуют его
    if (generationMode === "img2video" && !startImage && selectedModelDetails.requiresImage) {
      toast.error("Необходимо загрузить начальное изображение для этой модели");
      return;
    }

    // Для Fal моделей Master и Veo3 в режиме text2video проверяем, что есть промпт
    if (selectedModelDetails.provider === 'fal' && (selectedModel.includes('master') || selectedModel === 'fal-ai/veo3') && generationMode === "text2video" && !prompt.trim()) {
      toast.error("Необходимо указать описание для генерации видео");
      return;
    }

    setIsGenerating(true);
    setGenerationStep(1);
    setGenerationProgress(0);
    setGenerationStartTime(Date.now());

    // Уникальный идентификатор генерации
    const generationId = uuidv4();

    try {
      // Универсальное вычисление размеров видео по aspect ratio
      function getDimensionsForAspectRatio(aspectRatio: string, maxWidth = 1024, maxHeight = 1024) {
        const [w, h] = aspectRatio.split(":").map(Number);
        if (!w || !h) return { width: 1024, height: 576 }; // fallback 16:9
        // Сохраняем максимальные размеры, но не превышаем их
        let width = maxWidth;
        let height = Math.round((width * h) / w);
        if (height > maxHeight) {
          height = maxHeight;
          width = Math.round((height * w) / h);
        }
        // Округляем до ближайшего чётного числа (часто требуется для видео)
        width = Math.round(width / 2) * 2;
        height = Math.round(height / 2) * 2;
        return { width, height };
      }
      const { width, height } = getDimensionsForAspectRatio(aspectRatio);

      console.log('Starting video generation with options:', {
        prompt: getEnhancedPrompt(),
        duration: duration,
        aspectRatio,
        model: selectedModel,
        mode: generationMode
      });

      // Подготавливаем URL изображений, если они есть
      let startImageServerUrl = null;
      let endImageServerUrl = null;

      if (startImage) {
        try {
          // File -> base64
          const base64 = await fileToBase64(startImage);
          // Crop по актуальному aspectRatio
          const { cropped, wasCropped } = await cropBase64ToAspectRatio(base64, aspectRatio);
          if (wasCropped) {
            toast.warning("Загруженное начальное изображение было обрезано до выбранного формата. Результат может быть хуже, если исходное изображение не совпадает с форматом.");
          }
          // base64 -> Blob
          const blob = dataUriToBlob(cropped);
          startImageServerUrl = await uploadImageToSupabaseStorage(user?.id || 'anonymous', blob, startImage.name);
          console.log('Start image uploaded to Supabase:', startImageServerUrl);
        } catch (uploadError) {
          console.error('Error uploading start image to Supabase:', uploadError);
          toast.error('Не удалось загрузить начальное изображение.');
          setIsGenerating(false);
          return; // Прерываем генерацию, если загрузка не удалась
        }
      }

      if (endImage) {
        try {
          const base64 = await fileToBase64(endImage);
          const { cropped, wasCropped } = await cropBase64ToAspectRatio(base64, aspectRatio);
          if (wasCropped) {
            toast.warning("Загруженное конечное изображение было обрезано до выбранного формата. Результат может быть хуже, если исходное изображение не совпадает с форматом.");
          }
          const blob = dataUriToBlob(cropped);
          endImageServerUrl = await uploadImageToSupabaseStorage(user?.id || 'anonymous', blob, endImage.name);
          console.log('End image uploaded to Supabase:', endImageServerUrl);
        } catch (uploadError) {
          console.error('Error uploading end image to Supabase:', uploadError);
          toast.error('Не удалось загрузить конечное изображение.');
          setIsGenerating(false);
          return; // Прерываем генерацию, если загрузка не удалась
        }
      }

      // Определяем провайдера и используем соответствующий метод генерации
      let generatedVideoUrl: string;

      if (selectedModelDetails.provider === 'fal') {
        // Используем Fal API для новых моделей Kling 2.1
        setGenerationStep(2);
        setGenerationProgress(10);

        const falModelId = getFalModelId(selectedModel, !!startImageServerUrl);

        generatedVideoUrl = await generateVideoWithFal({
          prompt: getEnhancedPrompt(),
          model: falModelId,
          duration: duration,
          aspectRatio: aspectRatio,
          negativePrompt: negativePrompt || undefined,
          cfgScale: cfgScale,
          imageUrl: startImageServerUrl ?? undefined,
          userId: user?.id || 'anonymous',
          visibility: isPrivate ? 'private' : 'public',
          // Новые параметры для Veo3
          ...(selectedModel === 'fal-ai/veo3' && {
            enhancePrompt: enhancePrompt,
            generateAudio: generateAudio
          }),
          onProgress: (progress: number, step: number) => {
            setGenerationProgress(progress);
            setGenerationStep(step);
          }
        });
      } else {
        // Используем Replicate API для существующих моделей
        generatedVideoUrl = await generateVideoWithReplicate({
          prompt: getEnhancedPrompt(),
          width,
          height,
          duration: duration,
          userId: user?.id || 'anonymous',
          model: selectedModel,
          startImageUrl: startImageServerUrl ?? undefined, // Используем URL из Supabase
          endImageUrl: endImageServerUrl ?? undefined,     // Используем URL из Supabase
          mode: generationMode,
          aspectRatio, // <--- добавлено!
          // Новые параметры для ray flash 2
          ...(selectedModel === 'luma/ray-flash-2-540p' && {
            loop,
            concepts: concepts.length > 0 ? concepts : undefined,
          }),
          // Новые параметры для pixverse
          ...(selectedModel === 'pixverse/pixverse-v4.5' && {
            quality,
            motion_mode: motionMode,
            style: pixverseStyle !== 'None' ? pixverseStyle : undefined,
            effect: effect !== 'None' ? effect : undefined,
            negative_prompt: negativePrompt || undefined,
            last_frame_image: endImageServerUrl ?? undefined
          }),
          // Новые параметры для Kling v2.0
          ...(selectedModel === 'kwaivgi/kling-v2.0' && {
            cfg_scale: cfgScale,
            negative_prompt: negativePrompt || undefined
          }),
          // Параметр приватности для всех моделей
          visibility: isPrivate ? 'private' : 'public'
        });
      }

      // Показываем видео сразу для всех провайдеров
      if (generatedVideoUrl && generatedVideoUrl !== 'BACKGROUND_PROCESSING' && generatedVideoUrl !== 'veo3-processing-complete') {
        console.log('Setting generated video URL:', generatedVideoUrl);
        setGeneratedVideos([generatedVideoUrl]);
      } else if (generatedVideoUrl === 'BACKGROUND_PROCESSING') {
        // Только для старых FAL реализаций
        toast.info('Видео обрабатывается в фоне. Результат появится в разделе "Мои генерации".');
        return;
      } else if (generatedVideoUrl === 'veo3-processing-complete') {
        // Для Veo3 - фоновая обработка завершена
        toast.success('Видео Veo3 сгенерировано! Результат появится в разделе "Мои генерации" через несколько секунд.');
        return;
      }

      // Сохраняем видео в базу данных (только для не-Replicate и не-Fal моделей)
      const isReplicateModel = ['pixverse/pixverse-v4.5', 'luma/ray-flash-2-540p', 'kwaivgi/kling-v1.6-standard', 'kwaivgi/kling-v1.6-pro', 'kwaivgi/kling-v2.0'].includes(selectedModel);
      const isFalModel = selectedModelDetails.provider === 'fal';

      if (user?.id && !isReplicateModel && !isFalModel) {
        try {
          await saveVideoGeneration({
            user_id: user.id,
            prompt: getEnhancedPrompt(),
            video_url: generatedVideoUrl,
            thumbnail_url: startImageServerUrl || generatedVideoUrl, // Для img2video используем начальное изображение как миниатюру
            style: selectedStyle || undefined,
            duration: duration,
            aspect_ratio: aspectRatio,
            model: selectedModel,
            generation_id: generationId, // новый уникальный идентификатор
            visibility: isPrivate ? 'private' : 'public', // Передаем параметр приватности
          });

          // Обновляем список недавних генераций
          const userGenerations = await getVideoGenerations(user.id);
          setRecentGenerations(
            userGenerations.map(gen => ({
              id: gen.id,
              type: 'video' as const,
              prompt: gen.prompt,
              result: gen.video_url,
              timestamp: gen.timestamp,
              local: gen.local || false,
              thumbnail_url: gen.thumbnail_url
            }))
          );

          // Обновляем баланс токенов (для не-Replicate моделей сразу)
          try {
            const updatedCredits = await getUserCredits(user.id);
            setUserCredits(Number(updatedCredits));
            console.log('Обновлен баланс кредитов после генерации видео (прямое):', updatedCredits);
          } catch (error) {
            console.error('Ошибка обновления баланса:', error);
          }

          // Вызываем событие обновления генераций
          try {
            const event = new CustomEvent('generationsUpdated', {
              detail: { userId: user.id, type: 'video' }
            });
            window.dispatchEvent(event);
            console.log('Отправлено событие generationsUpdated для обновления UI');
          } catch (eventError) {
            console.error('Ошибка при отправке события обновления:', eventError);
          }
        } catch (saveError) {
          console.error('Ошибка при сохранении видео:', saveError);
        }
      }

      // Обновляем баланс для Replicate моделей с задержкой для webhook
      if (user?.id && isReplicateModel) {
        setTimeout(async () => {
          try {
            const updatedCredits = await getUserCredits(user.id);
            setUserCredits(Number(updatedCredits));
            console.log('Обновлен баланс кредитов после генерации видео (webhook):', updatedCredits);
          } catch (error) {
            console.error('Ошибка обновления баланса:', error);
          }
        }, 2000); // Даем webhook время обработать результат
      }

      toast.success(t('videoGen.success.generation'));
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error generating videos:', error);
        toast.error(error.message || t('videoGen.error.generation'));
      } else {
        console.error('Error generating videos:', error);
        toast.error(t('videoGen.error.generation'));
      }
    } finally {
      setIsGenerating(false);
      setGenerationStep(0);
      setGenerationProgress(0);
      setGenerationStartTime(null);
    }
  };

  // Удалено: handleDownloadVideo — больше не используется, скачивание реализовано inline через fetch/Blob

  // Добавим дополнительный useEffect для обновления списка генераций видео
  useEffect(() => {
    const loadRecentGenerations = async () => {
      if (user?.id) {
        try {
          const userGenerations = await getVideoGenerations(user.id);
          if (userGenerations && userGenerations.length > 0) {
            const formattedGenerations = userGenerations.map(gen => ({
              id: gen.id,
              type: "video" as const,
              prompt: gen.prompt,
              result: gen.video_url,
              timestamp: gen.timestamp,
              local: gen.local || false,
              thumbnail_url: gen.thumbnail_url
            }));
            setRecentGenerations(formattedGenerations);
            console.log(`Загружено ${formattedGenerations.length} видеогенераций`);
          }
        } catch (error) {
          console.error('Ошибка при загрузке последних генераций:', error);
        }
      }
    };

    // Слушаем событие обновления генераций
    const handleGenerationsUpdated = () => {
      console.log('Получено событие обновления генераций видео');
      loadRecentGenerations();
    };

    window.addEventListener('generationsUpdated', handleGenerationsUpdated);
    loadRecentGenerations(); // Загрузим список сразу при монтировании

    return () => {
      window.removeEventListener('generationsUpdated', handleGenerationsUpdated);
    };
  }, [user?.id]);

  return (
    <AppLayout>
      <SEOHead
        title={i18n.language === 'ru'
          ? "Генерация видео с помощью ИИ - UMA.AI"
          : "AI Video Generation - UMA.AI"
        }
        description={i18n.language === 'ru'
          ? "Создавайте уникальные видео с помощью искусственного интеллекта. Высокое качество, различные стили, быстрая генерация."
          : "Create unique videos using artificial intelligence. High quality, various styles, fast generation."
        }
        keywords={i18n.language === 'ru'
          ? "генерация видео, ИИ, искусственный интеллект, создание видео, нейросети"
          : "video generation, AI, artificial intelligence, video creation, neural networks"
        }
        url="https://umaai.site/video"
      />
      <div className="py-4 sm:py-8 px-4 sm:px-8 relative min-h-screen">
        <DottedBackground type="dots" className="opacity-30" />

        <div className="flex flex-col xl:flex-row max-w-6xl mx-auto relative z-10">
          <div className="flex-1">
            <div className="mb-6 sm:mb-8">
              <h1 className="text-2xl sm:text-3xl font-bold mb-3 text-foreground">{t('nav.videoGen')}</h1>
              <p className="text-foreground/70">{t('features.videoGenDescription')}</p>
              {!isLoadingCredits && user?.id && (
                <p className="mt-2 text-sm font-medium flex items-center">
                  <Sparkles size={16} className="mr-1 text-yellow-500" />
                  Баланс: {userCredits} токенов
                </p>
              )}
            </div>

            <ShimmerEffect isActive={isGenerating}>
              <div className="rounded-xl p-4 sm:p-6 mb-10 border bg-card text-card-foreground shadow-sm">
              <Tabs
                key={selectedModel} // Принудительно обновляем компонент при смене модели
                value={generationMode}
                defaultValue={
                  // Для моделей Standard и Pro по умолчанию img2video, так как они не поддерживают text2video
                  (selectedModel === 'fal-ai/kling-video/v2.1/standard' || selectedModel === 'fal-ai/kling-video/v2.1/pro')
                    ? "img2video"
                    // Для Veo3 только text2video
                    : selectedModel === 'fal-ai/veo3'
                    ? "text2video"
                    : "text2video"
                }
                onValueChange={setGenerationMode}
              >
                <TabsList className="mb-6">
                  {/* Скрываем таб Text to Video для моделей Standard и Pro */}
                  {!(selectedModel === 'fal-ai/kling-video/v2.1/standard' || selectedModel === 'fal-ai/kling-video/v2.1/pro') && (
                    <TabsTrigger value="text2video" className="flex items-center">
                      <Film className="w-4 h-4 mr-2" />
                      {i18n.language === 'ru' ? 'Текст в видео' : 'Text to Video'}
                    </TabsTrigger>
                  )}
                  {/* Скрываем таб Image to Video для Veo3 (только text-to-video) */}
                  {selectedModel !== 'fal-ai/veo3' && (
                    <TabsTrigger value="img2video" className="flex items-center">
                      <Image className="w-4 h-4 mr-2" />
                      {i18n.language === 'ru' ? 'Изображение в видео' : 'Image to Video'}
                    </TabsTrigger>
                  )}
                </TabsList>

                <TabsContent value="text2video">
              <div className="relative mb-4">
                    <textarea
                      placeholder={i18n.language === 'ru'
                        ? "Опишите видео, которое хотите создать..."
                        : "Describe the video you want to create..."}
                      className="w-full h-24 p-4 pr-[140px] rounded-lg border border-border bg-transparent text-foreground resize-none focus:outline-none focus:ring-1 focus:ring-primary"
                  value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                />

                <Button
                  className={cn(
                    "absolute right-2 top-1/2 transform -translate-y-1/2 h-10 min-w-[90px]",
                    isGenerating && "w-[135px]"
                  )}
                  onClick={generateVideos}
                  disabled={isGenerating || !prompt.trim()}
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center w-full">
                          <span className="mr-2 text-white">{t('imageGen.actions.generating')}</span>
                      <div className="flex gap-1">
                        <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"></div>
                        <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-200"></div>
                        <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-400"></div>
                      </div>
                    </div>
                  ) : (
                        <div className="flex items-center">
                      <Wand2 size={18} className="mr-2" />
                          <span className="mr-2 text-white">{i18n.language === 'ru' ? 'Создать' : 'Generate'}</span>
                          <div className="flex items-center text-xs bg-white/20 text-white px-1.5 py-0.5 rounded-full">
                            <Sparkles size={12} className="mr-0.5" />
                            {getGenerationCost()}
                          </div>
                        </div>
                      )}
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="img2video">
                  <div className="mb-4 space-y-4">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <div className="flex-1">
                        <p className="text-sm text-foreground/80 mb-2">
                          {selectedModelDetails.requiresImage
                            ? (i18n.language === 'ru' ? 'Начальное изображение (обязательно)' : 'Start image (required)')
                            : (i18n.language === 'ru' ? 'Начальное изображение (опционально)' : 'Start image (optional)')
                          }
                        </p>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            className="h-10 w-full"
                            onClick={() => startImageInputRef.current?.click()}
                          >
                            <Upload size={16} className="mr-2" />
                            {startImage
                              ? (i18n.language === 'ru' ? 'Изображение загружено' : 'Image uploaded')
                              : (i18n.language === 'ru' ? 'Загрузить изображение' : 'Upload image')}
                          </Button>
                          <input
                            type="file"
                            ref={startImageInputRef}
                            accept="image/*"
                            className="hidden"
                            onChange={handleStartImageChange}
                          />
                        </div>
                        {startImageUrl && (
                          <div className="mt-2 relative w-24 h-24 rounded-md overflow-hidden border">
                            <img
                              src={startImageUrl}
                              alt="Start"
                              className="w-full h-full object-cover"
                            />
                            <Button
                              variant="destructive"
                              size="icon"
                              className="h-6 w-6 p-1 absolute top-1 right-1 rounded-full bg-red-500 hover:bg-red-600"
                              onClick={handleRemoveStartImage}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>

                      {selectedModelDetails.supportsEndImage && (
                        <div className="flex-1">
                          <p className="text-sm text-foreground/80 mb-2">{i18n.language === 'ru' ? 'Конечное изображение (опционально)' : 'End image (optional)'}</p>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              className="h-10 w-full"
                              onClick={() => endImageInputRef.current?.click()}
                            >
                              <Upload size={16} className="mr-2" />
                              {endImage
                                ? (i18n.language === 'ru' ? 'Изображение загружено' : 'Image uploaded')
                                : (i18n.language === 'ru' ? 'Загрузить изображение' : 'Upload image')}
                            </Button>
                            <input
                              type="file"
                              ref={endImageInputRef}
                              accept="image/*"
                              className="hidden"
                              onChange={handleEndImageChange}
                            />
                          </div>
                          {endImageUrl && (
                            <div className="mt-2 relative w-24 h-24 rounded-md overflow-hidden border">
                              <img
                                src={endImageUrl}
                                alt="End"
                                className="w-full h-full object-cover"
                              />
                              <Button
                                variant="destructive"
                                size="icon"
                                className="h-6 w-6 p-1 absolute top-1 right-1 rounded-full bg-red-500 hover:bg-red-600"
                                onClick={handleRemoveEndImage}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="relative">
                      <textarea
                        placeholder={i18n.language === 'ru'
                          ? "Описание для улучшения качества видео (необязательно)..."
                          : "Description to enhance video quality (optional)..."}
                        className={`w-full h-24 p-4 pr-[140px] ${selectedModelDetails.provider === 'fal' ? 'pb-8' : 'pb-4'} rounded-lg border border-border bg-transparent text-foreground resize-none focus:outline-none focus:ring-1 focus:ring-primary`}
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                      />

                      {/* Счетчик символов - только для FAL моделей Kling (не для Veo3) */}
                      {selectedModelDetails.provider === 'fal' && selectedModel !== 'fal-ai/veo3' && (
                        <div className="absolute bottom-2 left-4 text-xs text-foreground/50">
                          <span className={prompt.length > 2500 ? 'text-red-500' : ''}>
                            {prompt.length}
                          </span>
                          <span className="text-foreground/30">/2500</span>
                          {prompt.length > 2500 && (
                            <span className="ml-2 text-red-500">
                              {i18n.language === 'ru' ? 'Будет сокращен' : 'Will be truncated'}
                            </span>
                          )}
                        </div>
                      )}

                      <Button
                        className={cn(
                          "absolute right-2 top-1/2 transform -translate-y-1/2 h-10 min-w-[90px]",
                          isGenerating && "w-[135px]"
                        )}
                        onClick={generateVideos}
                        disabled={isGenerating || (generationMode === "img2video" && !startImage && selectedModelDetails.requiresImage)}
                      >
                        {isGenerating ? (
                          <div className="flex items-center justify-center w-full">
                            <span className="mr-2 text-white">{t('imageGen.actions.generating')}</span>
                            <div className="flex gap-1">
                              <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"></div>
                              <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-200"></div>
                              <div className="w-1.5 h-1.5 bg-current rounded-full animate-pulse animation-delay-400"></div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Film size={18} className="mr-2" />
                            <span className="mr-2 text-white">{i18n.language === 'ru' ? 'Создать' : 'Generate'}</span>
                            <div className="flex items-center text-xs bg-white/20 text-white px-1.5 py-0.5 rounded-full">
                              <Sparkles size={12} className="mr-0.5" />
                              {getGenerationCost()}
                            </div>
                          </div>
                  )}
                </Button>
              </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex flex-wrap gap-3 mb-6">
                {/* Скрываем общий селектор стилей для pixverse, у него есть свой специфичный */}
                {selectedModel !== 'pixverse/pixverse-v4.5' && (
                  <div className="flex flex-col gap-2 min-w-[160px]">
                    <p className="text-sm text-foreground/80 mb-1">{i18n.language === 'ru' ? 'Стиль видео:' : 'Video style:'}</p>
                    <Select
                      value={selectedStyle || ''}
                      onValueChange={(value) => setSelectedStyle(value || null)}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={i18n.language === 'ru' ? 'Выберите стиль' : 'Select style'} />
                      </SelectTrigger>
                      <SelectContent>
                        {videoStyleOptions.map((style) => (
                          <SelectItem key={style.id} value={style.id}>
                            {style.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* --- Движение камеры только для Ray Flash 2, первая строка --- */}
                {selectedModel === 'luma/ray-flash-2-540p' && (
                  <div className="flex flex-col gap-2 min-w-[180px]">
                    <p className="text-sm text-foreground/80 mb-1">
                      {i18n.language === 'ru'
                        ? 'Движение камеры:'
                        : 'Camera movement:'}
                    </p>
                    <Select
                      value=""
                      onValueChange={(v) => {
                        if (!concepts.includes(v)) setConcepts([...concepts, v]);
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={i18n.language === 'ru' ? 'Выбрать...' : 'Select...'} />
                      </SelectTrigger>
                      <SelectContent>
                        {CAMERA_CONCEPTS.map((c) => (
                          <SelectItem key={c.value} value={c.value}>
                            {i18n.language === 'ru' ? c.ru : c.en}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {concepts.map((c) => {
                        const label = CAMERA_CONCEPTS.find(x => x.value === c);
                        const isDark = document.documentElement.classList.contains('dark');
                        // Используем инлайн-стиль для текста, чтобы перебить любой tailwind/className
                        const selectedClass = isDark
                          ? 'bg-primary text-white ring-primary'
                          : 'bg-black ring-black';
                        return (
                          <button
                            key={c}
                            type="button"
                            onClick={() => setConcepts(concepts.filter(x => x !== c))}
                            className={`
                              inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium
                              whitespace-nowrap overflow-hidden ring-1 ring-inset
                              ${selectedClass} transition-colors
                              focus:outline-none
                              ${isDark ? 'border border-gray-500' : ''}
                            `}
                            style={{
                              marginBottom: '2px',
                              color: isDark ? undefined : '#fff'
                            }}
                          >
                            <span style={isDark ? undefined : { color: '#fff' }}>
                              {i18n.language === 'ru' ? label?.ru : label?.en}
                            </span>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}

                <div className="flex flex-col gap-2 min-w-[160px]">
                  <p className="text-sm text-foreground/80 mb-1">{i18n.language === 'ru' ? 'Модель:' : 'Model:'}</p>
                  <Select value={selectedModel} onValueChange={handleModelChange}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={i18n.language === 'ru' ? 'Выберите модель' : 'Select model'}>
                        {selectedModel && videoModels.find(m => m.id === selectedModel)?.name}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent className="w-[90vw] sm:w-[500px] md:w-[550px] lg:w-[600px]">
                      {videoModels.map(model => (
                        <SelectItem key={model.id} value={model.id} className="p-4">
                          <div className="flex items-start gap-4 w-full">
                            <div className="flex-shrink-0 relative">
                              <video
                                src={model.video}
                                title={model.name}
                                className={`w-20 h-20 sm:w-24 sm:h-24 rounded-lg ${
                                  model.id === 'fal-ai/veo3' ? 'object-fill' : 'object-cover'
                                }`}
                                autoPlay
                                loop
                                muted
                                playsInline
                              />
                              {selectedModel === model.id && (
                                <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                    <svg className="w-4 h-4 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <span className="font-medium text-sm truncate">{model.name}</span>
                                <div className="flex items-center gap-2">
                                  {model.isNew && (
                                    <div className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                                      New
                                    </div>
                                  )}
                                  <div className="flex items-center text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full whitespace-nowrap">
                                    <Sparkles size={12} className="mr-1" />
                                    {model.id === 'fal-ai/veo3' ? (model.tokenCost[8] || 1280) : (model.tokenCost[5] || 100)}
                                  </div>
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground line-clamp-2">
                                {getModelDescription(model.description)}
                              </p>
                              {/* Индикатор генерации звука для Veo3 */}
                              {model.hasAudio && (
                                <div className="flex items-center mt-1">
                                  <div className="flex items-center text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full">
                                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.846 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.846l3.537-3.816a1 1 0 011.617.816zM16 8a2 2 0 11-4 0 2 2 0 014 0z" clipRule="evenodd" />
                                    </svg>
                                    {i18n.language === 'ru' ? 'Генерирует звук' : 'Generates audio'}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2">
                  <p className="text-sm text-foreground/80 mb-1">{i18n.language === 'ru' ? 'Длительность:' : 'Duration:'}</p>
                  <div className="flex items-center gap-2">
                    {selectedModelDetails.supportedDurations.map(dur => (
                    <Button
                        key={dur}
                        variant={duration === dur ? "default" : "outline"}
                      className="h-8 px-3"
                        onClick={() => setDuration(dur)}
                    >
                        {dur}{i18n.language === 'ru' ? 'с' : 's'}
                    </Button>
                    ))}
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <p className="text-sm text-foreground/80 mb-1">{i18n.language === 'ru' ? 'Формат:' : 'Format:'}</p>
                  <div className="flex items-center gap-2 flex-wrap">
                    {selectedModelDetails.aspectRatios.map(ratio => (
                      <Button
                        key={ratio}
                        variant={aspectRatio === ratio ? "default" : "outline"}
                        className="h-8 px-3"
                        onClick={() => setAspectRatio(ratio)}
                      >
                        {ratio}
                      </Button>
                    ))}
                    {/* --- Чекбокс "Зациклить видео" только для Ray Flash 2, в строке форматов --- */}
                    {selectedModel === 'luma/ray-flash-2-540p' && (
                      <div className="flex items-center ml-4">
                        <Checkbox
                          checked={loop}
                          onCheckedChange={v => setLoop(v === true)}
                          id="loop-checkbox"
                          className="border border-border bg-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-[rgba(255,255,255,0.08)]"
                          style={{
                            borderColor: 'rgba(180,180,200,0.5)',
                            background: loop ? undefined : 'rgba(255,255,255,0.08)'
                          }}
                        />
                        <label htmlFor="loop-checkbox" className="text-sm select-none cursor-pointer ml-2">
                          {i18n.language === 'ru'
                            ? 'Зациклить видео'
                            : 'Loop video'}
                        </label>
                      </div>
                    )}
                  </div>
                </div>



                {/* Дополнительные контролы для pixverse модели */}
                {selectedModel === 'pixverse/pixverse-v4.5' && (
                  <div className="flex flex-wrap gap-3 mb-6 p-4 bg-muted/30 rounded-lg border">
                    <div className="w-full mb-2">
                      <p className="text-sm font-medium text-foreground/90">{t('videoGen.pixverse.settings')}</p>
                    </div>

                    {/* Качество */}
                    <div className="flex flex-col gap-2 min-w-[160px]">
                      <p className="text-sm text-foreground/80 mb-1">{t('videoGen.pixverse.quality')}:</p>
                      <Select value={quality} onValueChange={setQuality}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {qualityOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Режим движения */}
                    <div className="flex flex-col gap-2 min-w-[160px]">
                      <p className="text-sm text-foreground/80 mb-1">{t('videoGen.pixverse.motionMode')}:</p>
                      <Select value={motionMode} onValueChange={setMotionMode}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {motionModeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {t(`videoGen.pixverse.motionModes.${option.value}`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Стиль Pixverse */}
                    <div className="flex flex-col gap-2 min-w-[160px]">
                      <p className="text-sm text-foreground/80 mb-1">{t('videoGen.pixverse.style')}:</p>
                      <Select value={pixverseStyle} onValueChange={setPixverseStyle}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {pixverseStyles.map(style => (
                            <SelectItem key={style.value} value={style.value}>
                              {t(`videoGen.pixverse.styles.${style.value}`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Эффект */}
                    <div className="flex flex-col gap-2 min-w-[160px]">
                      <p className="text-sm text-foreground/80 mb-1">{t('videoGen.pixverse.effect')}:</p>
                      <Select value={effect} onValueChange={setEffect}>
                        <SelectTrigger className="w-full">
                          <div className="flex items-center gap-3 w-full">
                            {(() => {
                              const selectedEffect = pixverseEffects.find(eff => eff.value === effect);
                              return selectedEffect?.image ? (
                                <img
                                  src={selectedEffect.image}
                                  alt={selectedEffect.value}
                                  className="w-6 h-6 rounded object-cover flex-shrink-0 border border-border"
                                  style={{ aspectRatio: '1/1' }}
                                />
                              ) : (
                                <div className="w-6 h-6 rounded bg-muted flex items-center justify-center flex-shrink-0 border border-border">
                                  <span className="text-xs text-muted-foreground">—</span>
                                </div>
                              );
                            })()}
                            <SelectValue>
                              {(() => {
                                const selectedEffect = pixverseEffects.find(eff => eff.value === effect);
                                return selectedEffect ? t(`videoGen.pixverse.effects.${selectedEffect.value.replace(/[^a-zA-Z0-9]/g, '_')}`) : 'Выберите эффект';
                              })()}
                            </SelectValue>
                          </div>
                        </SelectTrigger>
                        <SelectContent className="max-h-[500px] overflow-y-auto w-[400px] sm:w-[400px] max-w-[calc(100vw-2rem)]">
                          <div className="p-2">
                            {pixverseEffects.map(eff => (
                              <SelectItem key={eff.value} value={eff.value} className="p-2 sm:p-3 cursor-pointer">
                                <div className="flex items-center gap-2 sm:gap-3 w-full">
                                  <div className="flex-shrink-0 relative">
                                    {eff.image ? (
                                      <img
                                        src={eff.image}
                                        alt={eff.value}
                                        className="w-20 h-11 sm:w-32 sm:h-18 rounded-md object-cover border border-border"
                                        style={{ aspectRatio: '16/9' }}
                                        loading="lazy"
                                      />
                                    ) : (
                                      <div className="w-20 h-11 sm:w-32 sm:h-18 rounded-md bg-muted flex items-center justify-center border border-border" style={{ aspectRatio: '16/9' }}>
                                        <span className="text-xs sm:text-sm text-muted-foreground">Без эффекта</span>
                                      </div>
                                    )}
                                    {effect === eff.value && (
                                      <div className="absolute inset-0 bg-black/20 rounded-md flex items-center justify-center">
                                        <div className="w-5 h-5 sm:w-6 sm:h-6 bg-primary rounded-full flex items-center justify-center">
                                          <svg className="w-3 h-3 sm:w-4 sm:h-4 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                          </svg>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                  <span className="flex-1 text-left text-xs sm:text-sm">
                                    {t(`videoGen.pixverse.effects.${eff.value.replace(/[^a-zA-Z0-9]/g, '_')}`)}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </div>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Негативный промт */}
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-sm text-foreground/80 mb-1">{t('videoGen.pixverse.negativePrompt')}:</p>
                      <textarea
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        placeholder={t('videoGen.pixverse.negativePromptDescription')}
                        className="px-3 py-2 border border-input bg-background rounded-md text-sm resize-none"
                        rows={2}
                      />
                    </div>


                  </div>
                )}

                {/* Дополнительные контролы для Kling v2.0 модели */}
                {selectedModel === 'kwaivgi/kling-v2.0' && (
                  <div className="w-full flex flex-wrap gap-3 mb-6 p-4 bg-muted/30 rounded-lg border">
                    <div className="w-full mb-2">
                      <p className="text-sm font-medium text-foreground/90">{i18n.language === 'ru' ? 'Настройки Kling v2.0' : 'Kling v2.0 Settings'}</p>
                    </div>
                    {/* CFG Scale */}
                    <div className="flex flex-col gap-2 min-w-[200px]">
                      <p className="text-sm text-foreground/80 mb-1">
                        {i18n.language === 'ru' ? 'Точность следования промпту (0-1):' : 'Prompt adherence (0-1):'}
                      </p>
                      <div className="flex items-center gap-3">
                        <Slider
                          value={[cfgScale]}
                          onValueChange={([v]) => setCfgScale(v)}
                          min={0}
                          max={1}
                          step={0.01}
                          className="w-full"
                        />
                        <span className="text-sm font-medium min-w-[40px] text-center">{cfgScale.toFixed(2)}</span>
                      </div>
                    </div>
                    {/* Негативный промт */}
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-sm text-foreground/80 mb-1">{i18n.language === 'ru' ? 'Негативный промт:' : 'Negative Prompt:'}</p>
                      <textarea
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        placeholder={i18n.language === 'ru' ? 'Опишите, что НЕ должно быть в видео...' : 'Describe what should NOT be in the video...'}
                        className="px-3 py-2 border border-input bg-background rounded-md text-sm resize-none"
                        rows={2}
                      />
                    </div>


                  </div>
                )}

                {/* Дополнительные контролы для Fal моделей Kling 2.1 (не для Veo3) */}
                {selectedModelDetails.provider === 'fal' && selectedModel !== 'fal-ai/veo3' && (
                  <div className="w-full flex flex-wrap gap-3 mb-6 p-4 bg-muted/30 rounded-lg border">
                    <div className="w-full mb-2">
                      <p className="text-sm font-medium text-foreground/90">
                        {i18n.language === 'ru' ? 'Настройки Kling 2.1' : 'Kling 2.1 Settings'}
                      </p>
                    </div>
                    {/* CFG Scale */}
                    <div className="flex flex-col gap-2 min-w-[200px]">
                      <p className="text-sm text-foreground/80 mb-1">
                        {i18n.language === 'ru' ? 'Точность следования промпту (0-1):' : 'Prompt adherence (0-1):'}
                      </p>
                      <div className="flex items-center gap-3">
                        <Slider
                          value={[cfgScale]}
                          onValueChange={([v]) => setCfgScale(v)}
                          min={0}
                          max={1}
                          step={0.01}
                          className="w-full"
                        />
                        <span className="text-sm font-medium min-w-[40px] text-center">{cfgScale.toFixed(2)}</span>
                      </div>
                    </div>
                    {/* Негативный промт */}
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-sm text-foreground/80 mb-1">
                        {i18n.language === 'ru' ? 'Негативный промт:' : 'Negative Prompt:'}
                      </p>
                      <textarea
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        placeholder={i18n.language === 'ru' ? 'blur, distort, and low quality' : 'blur, distort, and low quality'}
                        className="px-3 py-2 border border-input bg-background rounded-md text-sm resize-none"
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                {/* Дополнительные контролы для Veo3 модели */}
                {selectedModel === 'fal-ai/veo3' && (
                  <div className="w-full flex flex-wrap gap-3 mb-6 p-4 bg-muted/30 rounded-lg border">
                    <div className="w-full mb-2">
                      <p className="text-sm font-medium text-foreground/90">
                        {t('videoGen.veo3.modelName')} {i18n.language === 'ru' ? 'Настройки' : 'Settings'}
                      </p>
                    </div>

                    {/* Улучшить промпт */}
                    <div className="flex items-center gap-3 min-w-[200px]">
                      <Checkbox
                        id="enhance-prompt-checkbox"
                        checked={enhancePrompt}
                        onCheckedChange={(checked) => setEnhancePrompt(checked === true)}
                        className="border-2 border-foreground/20 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                      />
                      <div className="flex flex-col">
                        <label htmlFor="enhance-prompt-checkbox" className="text-sm font-medium cursor-pointer">
                          {t('videoGen.veo3.enhancePrompt')}
                        </label>
                        <p className="text-xs text-foreground/60">
                          {t('videoGen.veo3.enhancePromptDesc')}
                        </p>
                      </div>
                    </div>

                    {/* Генерация аудио */}
                    <div className="flex items-center gap-3 min-w-[200px]">
                      <Checkbox
                        id="generate-audio-checkbox"
                        checked={generateAudio}
                        onCheckedChange={(checked) => setGenerateAudio(checked === true)}
                        className="border-2 border-foreground/20 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                      />
                      <div className="flex flex-col">
                        <label htmlFor="generate-audio-checkbox" className="text-sm font-medium cursor-pointer">
                          {t('videoGen.veo3.generateAudio')}
                        </label>
                        <p className="text-xs text-foreground/60">
                          {t('videoGen.veo3.generateAudioDesc')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Private Toggle - НИЖЕ всех параметров для всех моделей */}
              <div className={`flex justify-start ${
                selectedModel === 'pixverse/pixverse-v4.5' || selectedModel === 'kwaivgi/kling-v2.0' || (selectedModelDetails.provider === 'fal' && selectedModel !== 'fal-ai/veo3') || selectedModel === 'fal-ai/veo3'
                  ? 'mb-3'
                  : 'mb-6'
              }`}>
                <PrivateToggle
                  isPrivate={isPrivate}
                  onToggle={setIsPrivate}
                />
              </div>

              {isGenerating && (
                <div className="mt-8 text-center">
                  <div className="w-full bg-foreground/10 rounded-full h-2 mb-4">
                    <div
                      className="bg-foreground h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${generationProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-foreground/70 text-sm">
                    {generationStep === 1 && "Подготовка к генерации..."}
                    {generationStep === 2 && "Обработка видео кадров..."}
                    {generationStep === 3 && "Композиция видео последовательностей..."}
                    {generationStep === 4 && "Применение стиля и эффектов..."}
                    {generationStep === 5 && "Финализация видео..."}
                  </p>
                  {generationStartTime && (
                    <p className="text-foreground/50 text-xs mt-2">
                      Время генерации: {Math.floor((Date.now() - generationStartTime) / 1000)}с
                    </p>
                  )}
                </div>
              )}

              {generatedVideos.length > 0 && (
                <div className="mt-8">
                  <h3 className="text-xl font-bold mb-4 text-foreground">{t('imageGen.sections.generatedImages')}</h3>
                  <div className="bg-background rounded-xl overflow-hidden p-1 border border-foreground/10 shadow-sm">
                    {/* Контейнер с динамическим aspect ratio */}
                    <div
                      className="relative w-full flex items-center justify-center bg-black rounded-lg overflow-hidden"
                      style={{
                        aspectRatio: aspectRatio.replace(':', '/'),
                        maxWidth: '100%',
                        maxHeight: 480,
                        margin: '0 auto'
                      }}
                    >
                      {recentGenerations[0]?.thumbnail_url && recentGenerations[0].thumbnail_url.endsWith('.gif') ? (
                        <img
                          src={recentGenerations[0].thumbnail_url}
                          alt="GIF preview"
                          className="w-full h-full object-contain"
                          style={{ maxHeight: 480, borderRadius: 12 }}
                        />
                      ) : (
                        <video
                          src={generatedVideos[0]}
                          controls
                          className="w-full h-full object-contain"
                          style={{ maxHeight: 480, borderRadius: 12, background: "#000" }}
                        />
                      )}
                    </div>
                    <div className="p-4 flex justify-between items-center">
                      <div>
                        <p className="text-sm text-foreground/80">Prompt: {prompt}</p>
                        {selectedStyle && (
                          <p className="text-xs text-foreground/60 mt-1">
                            Style: {videoStyleOptions.find(s => s.id === selectedStyle)?.name}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-foreground/10 text-foreground"
                          onClick={async () => {
                            // Новый способ скачивания видео через fetch и Blob
                            try {
                              const response = await fetch(generatedVideos[0]);
                              if (!response.ok) throw new Error('Network error');
                              const blob = await response.blob();
                              const url = window.URL.createObjectURL(blob);
                              const link = document.createElement('a');
                              link.href = url;
                              link.download = `generated-video-${Date.now()}.mp4`;
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                              window.URL.revokeObjectURL(url);
                              toast.success("Video downloaded successfully!");
                            } catch (error) {
                              toast.error("Failed to download video. Please try again.");
                            }
                          }}
                        >
                          <Download size={16} className="mr-2" />
                          {t('imageGen.actions.download')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!isGenerating && generatedVideos.length === 0 && (
                <div className="mt-8 text-center p-10 border border-dashed border-foreground/20 rounded-2xl">
                  <Video className="w-12 h-12 mx-auto text-foreground/30" />
                </div>
              )}
              </div>
            </ShimmerEffect>
          <div className="w-full xl:w-64 xl:ml-6 mb-6 xl:mb-0 xl:hidden">
            <RecentGenerations
              generations={recentGenerations}
              type="video"
            />
          </div>

          </div> {/* Закрывающий тег для div на строке 843 */}

          <div className="w-64 ml-6 hidden xl:block">
            <RecentGenerations
              generations={recentGenerations}
              type="video"
              className="sticky top-24"
            />
          </div>
        </div>
      </div>
    {/* Модальное окно для Community Showcase */}
    <Dialog open={isCommunityModalOpen} onOpenChange={setIsCommunityModalOpen}>
      <DialogContent className="p-0 max-w-3xl w-auto bg-background/90 backdrop-blur-sm border-none shadow-2xl rounded-lg">
        {selectedCommunityVideo && (
          <div className="relative aspect-video rounded-lg overflow-hidden">
            {/* В модальном окне показываем полноценный видеоплеер */}
            {selectedCommunityVideo?.thumbnailUrl && selectedCommunityVideo.thumbnailUrl.endsWith('.gif') ? (
              <img
                src={selectedCommunityVideo.thumbnailUrl}
                alt="GIF preview"
                className="w-full h-full object-contain bg-black"
                style={{ maxHeight: 400 }}
              />
            ) : (
              <video
                src={selectedCommunityVideo?.videoUrl}
                controls
                autoPlay
                className="w-full h-full object-contain"
              />
            )}
          </div>
        )}
        {/* Кнопка закрытия включена по умолчанию в DialogContent */}
      </DialogContent>
    </Dialog>
  </AppLayout>
);
};

export default VideoGeneration;
