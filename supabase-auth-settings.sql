-- Скрипт для отключения требования подтверждения email в Supabase
-- ВАЖНО: это делается только для разработки/тестирования!

-- Отключаем требование подтверждения email для существующих пользователей
UPDATE auth.users 
SET email_confirmed_at = NOW()
WHERE email_confirmed_at IS NULL;

-- Выводим список пользователей и их статус подтверждения
SELECT 
  id, 
  email, 
  email_confirmed_at, 
  confirmed_at, 
  created_at,
  last_sign_in_at
FROM auth.users
ORDER BY created_at DESC
LIMIT 10;

-- ВНИМАНИЕ: Для полного отключения требования подтверждения email
-- нужно также изменить настройки в панели управления Supabase:
-- 1. Перейдите в раздел Authentication -> Settings -> Email
-- 2. Выключите опцию "Enable email confirmations"
-- 3. Сохраните изменения

-- Также можно проверить пользователя напрямую по email
DO $$
DECLARE
  user_email TEXT := '<EMAIL>';
  user_id UUID;
BEGIN
  -- Получаем ID пользователя
  SELECT id INTO user_id FROM auth.users WHERE email = user_email;
  
  IF user_id IS NOT NULL THEN
    -- Обновляем статус подтверждения (только email_confirmed_at)
    UPDATE auth.users 
    SET email_confirmed_at = NOW()
    WHERE id = user_id;
    
    RAISE NOTICE 'Пользователь % (ID: %) помечен как подтвержденный', user_email, user_id;
    
    -- Проверяем наличие профиля
    IF EXISTS (SELECT 1 FROM profiles WHERE id = user_id) THEN
      RAISE NOTICE 'Профиль для пользователя существует';
    ELSE
      -- Создаем профиль, если отсутствует
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (user_id, user_email, 'User', 10, NOW(), NOW());
      RAISE NOTICE 'Создан профиль для пользователя: %', user_id;
    END IF;
  ELSE
    RAISE NOTICE 'Пользователь с email % не найден', user_email;
  END IF;
END $$; 