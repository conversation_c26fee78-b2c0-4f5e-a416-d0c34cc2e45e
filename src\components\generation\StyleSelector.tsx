import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface Style {
  id: string;
  name: string;
  prompt: string;
}

interface StyleSelectorProps {
  styles: Style[];
  selectedStyle: string | null;
  onSelectStyle: (style: string | null) => void;
}

const StyleSelector = ({ styles, selectedStyle, onSelectStyle }: StyleSelectorProps) => {
  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {styles.map((style) => (
          <Button
            key={style.id}
            onClick={() => onSelectStyle(selectedStyle === style.id ? null : style.id)}
            variant="outline"
            className={cn(
              "transition-all",
              selectedStyle === style.id && "bg-primary text-primary-foreground border-primary"
            )}
          >
            {style.name}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default StyleSelector;
