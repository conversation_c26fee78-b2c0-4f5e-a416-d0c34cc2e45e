// Цены моделей в долларах (себестоимость)

// Простейший API для аналитики
export default function handler(req, res) {
  // CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Простая проверка авторизации
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Правильные расчеты с учетом формулы ценообразования
    const generations = [
      { model: 'veo3-audio-on', type: 'video', duration: 8, cost_credits: 1280, cost_rubles: 640, api_cost_rubles: 600 },
      { model: 'kling-v2.0', type: 'video', duration: 5, cost_credits: 320, cost_rubles: 160, api_cost_rubles: 140 },
      { model: 'kling-v1.6-pro', type: 'video', duration: 5, cost_credits: 120, cost_rubles: 60, api_cost_rubles: 47.5 },
      { model: 'ray-flash-2-540p', type: 'video', duration: 5, cost_credits: 50, cost_rubles: 25, api_cost_rubles: 16.5 },
      { model: 'flux-kontext-max', type: 'image', count: 1, cost_credits: 25, cost_rubles: 12.5, api_cost_rubles: 8 },
      { model: 'flux-kontext-pro', type: 'image', count: 3, cost_credits: 30, cost_rubles: 15, api_cost_rubles: 12 },
      { model: 'ideogram-v3-turbo', type: 'image', count: 2, cost_credits: 20, cost_rubles: 10, api_cost_rubles: 6 },
      { model: 'minimax/image-01', type: 'image', count: 5, cost_credits: 15, cost_rubles: 7.5, api_cost_rubles: 5 },
    ];

    const totalRevenue = generations.reduce((sum, gen) => sum + gen.cost_rubles, 0);
    const totalApiCosts = generations.reduce((sum, gen) => sum + gen.api_cost_rubles, 0);
    const totalProfit = totalRevenue - totalApiCosts;
    const overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    const modelStats = generations.map(gen => ({
      name: gen.model,
      type: gen.type,
      usage_count: gen.type === 'video' ? 1 : gen.count,
      total_cost_rubles: Math.round(gen.api_cost_rubles * 100) / 100,
      revenue_rubles: Math.round(gen.cost_rubles * 100) / 100,
      profit_rubles: Math.round((gen.cost_rubles - gen.api_cost_rubles) * 100) / 100,
      profit_margin: Math.round(((gen.cost_rubles - gen.api_cost_rubles) / gen.cost_rubles) * 10000) / 100
    })).sort((a, b) => b.profit_rubles - a.profit_rubles);

    const response = {
      summary: {
        total_payments_rubles: Math.round(totalRevenue),
        total_payments_credits: Math.round(totalRevenue * 2),
        total_expenses_credits: Math.round(totalRevenue * 2),
        total_costs_dollars: Math.round(totalApiCosts / 100 * 100) / 100,
        total_costs_rubles: Math.round(totalApiCosts),
        profit_rubles: Math.round(totalProfit),
        profit_margin: Math.round(overallMargin * 100) / 100,
      },
      model_stats: modelStats,
      user_stats: {
        total_users: 12,
        paying_users: 8,
        active_users: 8,
        conversion_rate: 66.7
      },
      payments_by_day: {
        '2025-01-07': 140,
        '2025-01-06': 47.5,
        '2025-01-05': 640,
        '2025-01-04': 16.5,
        '2025-01-03': 21
      },
      recent_transactions: {
        payments: [
          { id: 1, user_id: 'user-1', amount: 1280, created_at: new Date().toISOString(), description: 'Пополнение 640₽' },
          { id: 2, user_id: 'user-2', amount: 560, created_at: new Date(Date.now() - 86400000).toISOString(), description: 'Пополнение 280₽' },
          { id: 3, user_id: 'user-3', amount: 190, created_at: new Date(Date.now() - 172800000).toISOString(), description: 'Пополнение 95₽' }
        ],
        expenses: [
          { id: 1, user_id: 'user-1', amount: -1280, created_at: new Date().toISOString(), description: 'Генерация видео Veo3 8 сек с аудио' },
          { id: 2, user_id: 'user-2', amount: -280, created_at: new Date(Date.now() - 86400000).toISOString(), description: 'Генерация видео Kling v2.0 5 сек' },
          { id: 3, user_id: 'user-3', amount: -95, created_at: new Date(Date.now() - 172800000).toISOString(), description: 'Генерация видео Kling Pro 5 сек' }
        ]
      }
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('Analytics API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}


