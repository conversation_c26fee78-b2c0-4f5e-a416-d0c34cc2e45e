import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Moon, Sun, Coins } from 'lucide-react';
import { getUserCredits } from '@/utils/database';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { BuyCreditsDialog } from '@/components/ui/BuyCreditsDialog';

import LanguageSwitcher from './LanguageSwitcher';

interface HeaderProps {
  className?: string;
}

const Header = ({ className }: HeaderProps) => {
  const { user, signOut } = useAuth();
  const { theme, setTheme } = useTheme();
  const { t, i18n } = useTranslation();
  const [credits, setCredits] = useState<number | null>(null);
  
  // Функция получения кредитов
  const fetchCredits = async () => {
    if (user) {
      try {
        const userCredits = await getUserCredits(user.id);
        setCredits(userCredits);
      } catch (error) {
        console.error('Ошибка при получении кредитов:', error);
        
        // При сетевых ошибках не изменяем значение
        if (!(error instanceof Error && error.message.includes('Network error'))) {
          setCredits(null);
        }
      }
    }
  };
  
  // Обработчик обновления кредитов
  const handleCreditsUpdated = (event: CustomEvent) => {
    if (event.detail?.userId === user?.id) {
      setCredits(event.detail.newBalance);
    }
  };

  useEffect(() => {
    fetchCredits();
    
    // Добавляем слушатель события обновления кредитов
    window.addEventListener('creditsUpdated', handleCreditsUpdated as EventListener);
    
    // Очистка при размонтировании компонента
    return () => {
      window.removeEventListener('creditsUpdated', handleCreditsUpdated as EventListener);
    };
  }, [user]);
  
  return (
    <header className={cn(
      "fixed top-0 right-0 left-0 z-50 py-4 px-6 flex justify-between",
      "bg-white/60 dark:bg-black/60 backdrop-blur-md",
      "border-b border-black/5 dark:border-white/5",
      className
    )}>
      <div className="flex items-center">
      </div>
      
      <div className="flex items-center gap-4">
        {/* Отображение баланса кредитов для авторизованных пользователей */}
        {user && (
          <div className="flex items-center gap-3 mr-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1.5 text-sm font-medium">
                    <Coins className="h-4 w-4 text-primary" />
                    <span>{credits !== null ? credits : '...'}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{i18n.language === 'ru' ? 'Ваши кредиты' : 'Your credits'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* Кнопка покупки кредитов */}
            <BuyCreditsDialog onSuccessfulPurchase={() => fetchCredits()} />
          </div>
        )}
        
        {/* Переключатель темы */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
          aria-label={t('header.toggleTheme')}
          title={t('header.toggleTheme')}
          className="w-9 h-9 rounded-full text-black dark:text-white hover:text-black hover:bg-black/5 dark:hover:text-white dark:hover:bg-white/5"
        >
          {theme === 'light' ? (
            <Moon className="h-5 w-5" />
          ) : (
            <Sun className="h-5 w-5" />
          )}
        </Button>
        
        {/* Language Switcher */}
        <div className="relative z-50">
          <LanguageSwitcher />
        </div>

        {user ? (
          <Button 
            variant="ghost" 
            className="text-black dark:text-white hover:text-black hover:bg-black/5 dark:hover:text-white dark:hover:bg-white/5"
            onClick={async () => {
              console.log('Начинаем выход из системы');
              try {
                await signOut();
                console.log('Выход из системы выполнен успешно');
              } catch (error) {
                console.error('Ошибка при выходе:', error);
              }
            }}
          >
            {t('header.logout')}
          </Button>
        ) : (
          <>
            <Button variant="ghost" className="text-black dark:text-white hover:text-black hover:bg-black/5 dark:hover:text-white dark:hover:bg-white/5" asChild>
              <Link to="/login">{t('header.loginButton')}</Link>
            </Button>
            <Button className="bg-black hover:bg-black/80 text-white" asChild>
              <Link to="/login">{t('header.signUpButton')}</Link>
            </Button>
          </>
        )}
      </div>
    </header>
  );
};

export default Header;
