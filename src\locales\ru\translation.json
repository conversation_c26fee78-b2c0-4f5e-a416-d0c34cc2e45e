{"landing": {"introducing": "Представляем Uma AI", "welcomeTo": "Добро пожаловать в", "appName": "Uma AI", "title1": "Создавайте с", "title2": "искусственным интеллектом", "subtitle": "Создание исключительного цифрового опыта через инновационный дизайн и передовые технологии.", "getStarted": "Начать бесплатно", "viewDemos": "Посмотреть демо", "ctaTitle": "Начните создавать с Uma AI уже сегодня", "ctaSubtitle": "Присоединяйтесь к тысячам создателей, раскрывающих возможности контента, сгенерированного ИИ. Кредитная карта для начала не требуется."}, "login": {"title": "С возвращением", "subtitle": "Войдите, чтобы продолжить работу с Uma AI", "footer": "© {{year}} Uma AI. Все права защищены."}, "auth": {"login": "Войти", "register": "Регистрация", "registrationSuccessful": "Регистрация успешна!", "checkEmailForConfirmation": "Проверьте почту для подтверждения", "email": "Email", "emailPlaceholder": "Введите ваш email", "password": "Пароль", "passwordPlaceholder": "Введите пароль", "name": "Имя", "namePlaceholder": "Введите ваше имя", "passwordTooShort": "Пароль должен содержать минимум 6 символов", "forgotPassword": "Забыли пароль?", "orContinueWith": "Или продолжить с", "signUpPrompt": "Нет аккаунта? ", "signUpLink": "Зарегистрироваться", "hasAccount": "Уже есть аккаунт? ", "loginLink": "Войти", "forgotPasswordLink": "Забыли пароль?", "backToLogin": "Вернуться к входу", "resetPasswordInstructions": "Введите почту для сброса пароля", "resetPasswordSent": "Письмо для сброса пароля отправлено", "loginSuccess": "Вход выполнен успешно", "signInSuccess": "Вход выполнен успешно", "registerSuccess": "Регистрация успешна! Выполняем вход...", "resetSuccess": "Письмо для сброса пароля отправлено! Проверьте почту.", "authError": "Ошибка аутентификации. Проверьте данные.", "socialSuccess": "Вход через {{provider}} успешен!", "socialError": "Вход через {{provider}} не удался. Попробуйте снова.", "showPassword": "Показать пароль", "hidePassword": "Скрыть пароль", "footer": "© {{year}} Uma AI. Все права защищены."}, "buyCredits": {"buttonText": "Купить кредиты", "title": "Купить кредиты", "description": "Выберите подходящий план", "test": "Тестовый план только для разработки", "testPlan": "Тестовый", "credits": "креди<PERSON><PERSON>", "pleaseSelectPlan": "Пожалуйста, выберите план", "creatingPayment": "Создание платежа...", "redirecting": "Перенаправление на страницу оплаты...", "loading": "Обработка...", "proceedToPayment": "Перейти к оплате", "errors": {"invalidPaymentResponse": "Некорректный ответ платежной системы", "noConfirmationUrl": "Отсутствует URL для подтверждения платежа", "paymentCreation": "Ошибка при создании платежа", "unknown": "Неизвестная ошибка", "noEmail": "Не найден email пользователя"}, "customAmount": "Произвольная сумма", "enterAmount": "Введите сумму (₽)", "minAmount": "Минимальная сумма: 2000₽", "maxAmount": "Максимальная сумма: 100000₽", "creditsCalculated": "Вы получите {{credits}} кредитов", "invalidAmount": "Введите корректную сумму от 2000₽ до 100000₽", "action": "Купить", "plans": {"test": {"name": "Тестовый", "credits": "1000 кредитов", "price": "1 ₽"}, "basic": {"name": "Базовый", "credits": "500 кредитов", "price": "299 ₽", "description": "Пакет кредитов для начинающих"}, "standard": {"name": "Стандартный", "credits": "1500 кредитов", "price": "799 ₽", "description": "Самый популярный выбор"}, "premium": {"name": "Премиум", "credits": "4000 кредитов", "price": "2000 ₽", "description": "Для активных пользователей"}, "custom": {"name": "Ультра (Свободный)", "credits": "кредитов по курсу 1₽ = 2 кредита", "price": "от 2000 ₽", "description": "Выберите любую сумму от 2000₽ до 100000₽"}}, "features": {"text2img": "text2img (5 кр.)", "img2img": "img2img (10 кр.)", "video5s": "видео (5с) (100 кр.)", "video9s": "видео (9с) (150 кр.)", "unlimitedText": "* Генерация текста - безлимитно"}}, "credits": {"unlimitedCredits": "Безлимитные кредиты", "plans": {"basic": {"name": "Базовый"}, "standard": {"name": "Стандартный"}, "premium": {"name": "Премиум"}}, "buyMore": "Купить ещё", "title": "Купить кредиты", "description": "Выберите подходящий план", "creditsLeft": "{{count}} кредитов осталось", "action": "Купить"}, "pricing": {"sectionTitle": "Выберите свой план", "sectionSubtitle": "Начните бесплатно, улучшайте по мере роста", "footer": "Все цены указаны в рублях. НДС включен где применимо.", "freePlan": {"title": "Тестовый", "price": "1 ₽", "description": "Тестовый план только для разработки", "button": "Попробовать тест", "features": ["1000 кредитов", "text2img (5 кр.)", "img2img (10 кр.)", "видео (5с) (100 кр.)", "видео (9с) (150 кр.)"]}, "proPlan": {"title": "Базовый", "price": "300 ₽", "description": "Пакет кредитов для начинающих", "button": "Выбрать Базовый", "mostPopular": "Для начинающих", "features": ["600 кредитов", "text2img (5 кр.)", "img2img (10 кр.)", "видео (5с) (100 кр.)", "видео (9с) (150 кр.)"]}, "standardPlan": {"title": "Стандартный", "price": "750 ₽", "description": "Самый популярный выбор", "button": "Выбрать Стандартный", "mostPopular": "Популярный выбор", "features": ["1500 кредитов", "text2img (5 кр.)", "img2img (10 кр.)", "видео (5с) (100 кр.)", "видео (9с) (150 кр.)"]}, "enterprisePlan": {"title": "Премиум", "price": "2000 ₽", "description": "Для активных пользователей", "button": "Выбрать Премиум", "features": ["4000 кредитов", "text2img (5 кр.)", "img2img (10 кр.)", "видео (5с) (100 кр.)", "видео (9с) (150 кр.)"]}, "customPlan": {"title": "Ультра (Свободный)", "price": "от 2000 ₽", "description": "Выберите любую сумму от 2000₽ до 100000₽", "button": "Выбрать Ультра", "features": ["кредитов по курсу 1₽ = 2 кредита", "text2img (5 кр.)", "img2img (10 кр.)", "видео (5с) (100 кр.)", "видео (9с) (150 кр.)"]}}, "common": {"loading": "Загрузка..."}, "features": {"sectionTitle": "Одна платформа для создания с ИИ", "sectionSubtitle": "Одна платформа для всех ваших потребностей в ИИ. Легко переключайтесь между различными инструментами.", "textGenTitle": "Генерация текста", "textGenDescription": "Создавайте увлекательный контент, истории и ответы с помощью наших продвинутых языковых моделей.", "imageGenTitle": "Генерация изображений", "imageGenDescription": "Превращайте свои идеи в потрясающие визуальные образы с нашим ИИ-генератором изображений.", "videoGenTitle": "Генерация видео", "videoGenDescription": "Оживите ваши истории с помощью видео, созданных ИИ. Превращайте текстовые описания в захватывающие визуальные повествования."}, "showcase": {"sectionTitle": "Создано с помощью Uma AI", "sectionSubtitle": "Посмотрите, что возможно с нашими инструментами генерации ИИ. От потрясающих визуальных образов до креативного контента.", "item1Title": "Мощные автомобили", "item1Prompt": "Спортивные автомобили с индивидуальным стилем в красивых природных условиях", "item2Title": "Городские приключения", "item2Prompt": "Классические автомобили, мчащиеся по футуристическим городским пейзажам ночью", "item3Title": "Люксовые путешествия", "item3Prompt": "Роскошные автомобили в живописных горных локациях с атмосферным настроением"}, "dashboard": {"title": "История", "subtitle": "Генерируйте с помощью ИИ", "tabAll": "Все", "tabImage": "Изображения", "tabVideo": "Видео", "noExamples": "Нет сгенерированных данных", "noImageExamples": "Нет изображений для отображения", "noVideoExamples": "Нет видео для отображения", "history": "История", "historyDescription": "Просмотр истории ваших генераций", "modal": {"generatedImageAlt": "Сгенерированное изображение", "copyPrompt": "Копировать запрос", "downloadImage": "Скачать", "promptCopied": "Запрос скопирован!", "promptCopyError": "Не удалось скопировать запрос.", "downloadStarted": "Загрузка изображения началась!", "downloadError": "Ошибка загрузки изображения. Ссылка может быть недействительна или устарела.", "seeMore": "Показать полностью", "seeLess": "Скрыть", "prompt": "Промт", "usePrompt": "Использовать промт"}, "createImage": "Создать изображение", "createImageDesc": "Создавайте любые изображения, какие только пожелаете.", "createVideo": "Создать видео", "createVideoDesc": "Создавайте любые видео с помощью ИИ."}, "sidebar": {"dashboard": "Дашборд", "history": "История", "text": "Текст", "image": "Изображение", "video": "Видео", "speech": "Речь", "analytics": "Аналитика", "upgradePlan": "Улучшить план", "unlockFeatures": "Открыть все функции", "profile": "Профиль", "settings": "Настройки", "home": "Главная", "darkTheme": "Темная тема", "lightTheme": "Светлая тема"}, "navigation": {"home": "Главная", "dashboard": "Дашборд", "chat": "Генерация текста", "image": "Генерация изображений", "video": "Генерация видео", "speech": "Генерация речи", "history": "История", "profile": "Профиль", "settings": "Настройки", "upgradeAccount": "Улучшить аккаунт", "buyCredits": "Купить кредиты"}, "textGen": {"pageTitle": "Генерация текста", "initialGreeting": "Привет! Я Uma AI. Чем могу помочь?", "inputPlaceholder": "Введите ваше сообщение...", "defaultError": "Не удалось обработать запрос. Пожалуйста, попробуйте снова.", "generationErrorToast": "Не удалось сгенерировать ответ. Пожалуйста, попробуйте снова.", "generationErrorMessage": "Извините, произошла ошибка при обработке вашего запроса. Пожалуйста, попробуйте снова.", "recentVideos": "Последние видео", "noRecentVideos": "Нет недавних видео", "chats": "Чаты", "newChat": "Новый чат", "loadChatError": "Ошибка при загрузке чата", "imageUploadErrorToast": "Ошибка при загрузке изображения", "createMode": {"welcome": "Добро пожаловать в режим создания видео! Опишите, какое видео вы хотите создать.", "parametersTitle": "Укажите параметры видео", "parametersDescription": "Настройте параметры для создания вашего видео. Каждый параметр влияет на качество и стоимость генерации.", "duration": "Общая длительность видео", "fragmentDuration": "Длительность одного фрагмента", "aspectRatio": "Формат кадра", "resolution": "Разрешение", "transitionType": "Тип перех<PERSON><PERSON>ов", "privacy": "Приватность", "estimatedCost": "Стоимость проекта:", "confirmParameters": "Подтвердить параметры", "storyboardReady": "Раскадровка готова", "storyboardDescription": "Ваша раскадровка создана. Вы можете отредактировать любой кадр перед генерацией видео.", "regenerate": "Перегенерировать", "editPrompt": "Изменить промт", "confirmAndGenerate": "Подтвердить и начать генерацию видео"}}, "imageGen": {"placeholders": {"imagePrompt": "Опишите изображение, которое вы хотите создать...", "style": "Выберите стиль изображения", "aspectRatio": "Выберите соотношение сторон", "format": "Выберите формат", "negativePrompt": "Введите отрицательный запрос (опционально)"}, "image": "изображение", "images": "изображений", "actions": {"generate": "Сгенерировать", "generating": "Генерация", "download": "Скачать", "share": "Поделиться", "save": "Сохранить"}, "steps": {"starting": "Начало генерации...", "creating": "Создание изображений...", "finalizing": "Завершение...", "saving": "Сохранение изображения...", "complete": "Готово!"}, "sections": {"generatedImages": "Сгенерированные изображения", "prompt": "Запрос", "settings": "Настройки", "style": "Стиль", "aspectRatio": "Соотношение сторон", "imageCount": "Количество изображений", "format": "Формат"}, "settings": {"modelLabel": "Модель генерации:", "selectModel": "Выберите модель", "selectStyle": "Выберите стиль"}, "models": {"togetherDesc": "Быстрая генерация изображений с тонкими деталями и художественными стилями", "ideogram": "Ideogram 2", "ideogramDesc": "Универсальная модель, поддерживает img to img. Хорошо справляется с текстом", "fluxpro": "Flux Pro", "fluxproDesc": "Модель, заточенная под редактирование фотографий", "ideogram3": "Ideogram 3", "ideogram3Desc": "Модель нового поколения с улучшенным качеством и детализацией, поддерживает img to img. Отлично справляется с текстом", "minimaxDesc": "Высоко детализированная, гиперреалистичная модель", "imagen4Desc": "Передовая модель для создания высокодетализированных изображений", "bagelDesc": "Универсальная модель с поддержкой редактирования и высоким качеством. Идеально редактирует изображения", "fluxKontextPro": "Flux Kontext Pro", "fluxKontextProDesc": "Передовая модель редактирования изображений с высоким качеством и точным следованием промптам", "fluxKontextMax": "Flux Kontext Max", "fluxKontextMaxDesc": "Премиум модель с максимальной производительностью и улучшенной генерацией типографики"}, "options": {"imageCount": {"label": "Количество изображений:", "one": "1 изображение", "two": "2 изображения", "three": "3 изображения", "four": "4 изображения"}, "format": {"label": "Формат:", "square": "Квадр<PERSON>тный", "portrait": "Портретный", "landscape": "Пейзажный"}}, "error": {"invalidPrompt": "Пожалуйста, введите описание изображения", "emptyPrompt": "Пожалуйста, введите запрос для генерации изображения.", "noPrompt": "Введите описание изображения", "promptTooLong": "Запрос слишком длинный. Максимум 1000 символов.", "generation": "Ошибка генерации изображения. Пожалуйста, попробуйте снова.", "rateLimit": "Слишком много запросов. Пожалуйста, подождите несколько секунд.", "noImages": "Не удалось сгенерировать изображения. Попробуйте изменить описание.", "downloadFailed": "Не удалось скачать изображение.", "promptRequired": "Введите описание изображения", "authRequired": "Необходима авторизация", "modelNotFound": "Модель не найдена", "insufficientCredits": "Недостаточно кредитов. Требуется: {{count}}", "noImagesTogether": "Не удалось получить изображения от Together AI", "noImagesReplicate": "Не удалось получить изображения от Replicate", "invalidService": "Неподдерживаемый сервис генерации", "noImagesGenerated": "Не удалось сгенерировать изображения"}, "success": {"generation": "Изображения успешно сгенерированы!", "save": "Изображение сохранено!", "download": "Изображение скачано!"}, "characterReference": {"title": "Референс персонажа", "tooltip": "Загрузите изображения лиц для переноса персонажа в сгенерированное изображение. Поддерживается до 3 изображений.", "uploadText": "Нажмите или перетащите изображения сюда", "supportedFormats": "JPG, PNG, WebP", "maxSize": "Макс. 10MB", "maxReached": "Достигнут лимит изображений ({{max}})", "tooManyImages": "Слишком много изображений. Максимум: {{max}}", "invalidFileType": "Неподдерживаемый тип файла. Используйте изображения.", "fileTooLarge": "Файл слишком большой. Максимум 10MB.", "uploadError": "Ошибка загрузки файла", "uploadSuccess": "Загружено изображений: {{count}}", "previewAlt": "Превью изображения {{index}}", "imageCount": "{{current}} из {{max}} изображений"}, "bagel": {"modeTitle": "Режим работы", "modes": {"text2img": "Генерация", "text2imgDesc": "Создание изображений из текстового описания", "edit": "Редактирование", "editDesc": "Редактирование существующих изображений по инструкции"}}, "alt": {"generated": "Сгенерированное изображение", "selected": "Выбранное изображение для деталей"}}, "home": {"title": "Главная", "subtitle": "Генерируйте с помощью ИИ"}, "videoGen": {"pixverse": {"modelName": "Pixverse v4.5", "modelDescription": "Продвинутая модель с уникальными эффектами и стилями", "quality": "Качество", "motionMode": "Режим движения", "style": "Стиль", "effect": "Эффект", "seed": "Сид", "negativePrompt": "Негативный промт", "settings": "Настройки Pixverse", "qualityDescription": "Разрешение видео", "motionModeDescription": "Режим движения видео", "seedDescription": "Случайное число для воспроизводимой генерации", "negativePromptDescription": "Что избегать в видео", "styles": {"None": "Без стиля", "anime": "Аниме", "3d_animation": "3D Анимация", "clay": "Гл<PERSON><PERSON>", "cyberpunk": "К<PERSON>б<PERSON>р<PERSON><PERSON>нк", "comic": "<PERSON>о<PERSON><PERSON><PERSON><PERSON>"}, "effects": {"None": "Без эффекта", "Let_s_YMCA_": "YMCA танец", "Subject_3_Fever": "Лихорадка субъекта 3", "Ghibli_Live_": "Живая студия Гибли", "Suit_Swagger": "Деловой стиль", "Muscle_Surge": "Мышечный всплеск", "360__Microwave": "360° микроволновка", "Warmth_of_Jesus": "Тепло Иисуса", "Emergency_Beat": "Экстренный ритм", "Anything__Robot": "Что угодно, робот", "Kungfu_Club": "Клуб кунг-фу", "Mint_in_Box": "Мята в коробке", "Retro_Anime_Pop": "Ретро аниме поп", "Vogue_Walk": "Модная походка", "Mega_Dive": "Мега погружение", "Evil_Trigger": "Злой триггер"}, "motionModes": {"normal": "Обычный", "smooth": "Плавный"}}, "kling": {"cfgScaleLabel": "Возможность отклонения от промпта"}, "veo3": {"modelName": "Veo3", "modelDescription": "Лучшая модель видео генерации от Google. Генерирует видео со звуком высочайшего качества", "hasAudio": "Генер<PERSON><PERSON><PERSON><PERSON>т звук", "enhancePrompt": "Улучшить промпт", "enhancePromptDesc": "Автоматически улучшает ваш промпт для лучшего результата", "generateAudio": "Генерация аудио", "generateAudioDesc": "Генерировать звук для видео. Отключение снижает стоимость на 400 кредитов"}, "models": {"rayFlash2": "Быстрая и качественная модель. Поддерживает изображение в видео, начальный и конечный кадр, зацикливание", "kling16Standard": "Стандартная модель с отличным качеством и стабильностью. Поддерживает изображение в видео", "kling16Pro": "Модель в хорошем качестве с детализацией. Поддерживает изображение в видео, начальный и конечный кадр", "kling20": "Флагманская модель с динамичными движениями и улучшенным качеством. Поддерживает изображение в видео", "pixverse": "Креативная модель с уникальными эффектами и стилями. Поддерживает изображение в видео, начальный и конечный кадр", "kling21Standard": "Стандартная модель с хорошим качеством по доступной цене. Только изображение в видео", "kling21Pro": "Профессиональная модель с отличным качеством и детализацией. Только изображение в видео", "kling21Master": "Флагманская модель с превосходным качеством. Поддерживает текст в видео и изображение в видео", "veo3": "Лучшая модель видео генерации от Google. Генерирует 8-секундные видео со звуком высочайшего качества"}, "success": {"generation": "Видео успешно сгенерировано!"}}, "speechGen": {"title": "Генерация речи", "textToSpeech": "Текст в речь", "voiceCloningTab": "Клонирование голоса", "selectVoice": "Выбрать голос", "recordVoice": "Записать голос", "uploadAudio": "Загрузить аудио", "generateSpeech": "Сгенерировать речь", "cloneVoice": "Клонировать голос", "textPlaceholder": "Введите текст для генерации речи...", "voiceLibrary": {"title": "Библиотека голосов", "default": "Дефолтные", "yours": "<PERSON>а<PERSON>и", "community": "Сообщество", "noUserVoices": "У вас пока нет клонированных голосов", "createFirstVoice": "Создайте свой первый голос в разделе \"Клонирование голоса\"", "noCommunityVoices": "Пока нет голосов от сообщества", "beFirstToShare": "Станьте первым, кто поделится своим голосом!"}, "voiceCloning": {"voiceName": "Название голоса", "voiceNamePlaceholder": "Введите название для вашего голоса", "recordingTitle": "Запись голоса", "supportedFormats": "Поддерживаемые форматы: "}, "parameters": {"model": "Модель", "speed": "Скорость", "pitch": "Высота тона", "volume": "Громкость", "emotion": "Эмоция", "languageBoost": "Усиление языка", "englishNormalization": "Нормализация английского"}, "emotions": {"auto": "Автоматически", "neutral": "Нейтральная", "happy": "Радостная", "sad": "Грустная", "angry": "Злая", "fearful": "Испуганная", "disgusted": "Отвращение", "surprised": "Удивленная"}, "recording": {"start": "Начать запись", "stop": "Остановить запись", "recording": "Запись...", "duration": "Длительность: {{duration}}", "maxDuration": "Максимум 5 минут", "minDuration": "Минимум 10 секунд", "preview": "Предварительное прослушивание", "rerecord": "Перезаписать"}, "cost": "Стоимость: {{cost}} токенов", "minCost": "Минимальная стоимость: 5 токенов", "cloneCost": "Стоимость клонирования: 900 токенов", "history": {"title": "История речи", "empty": "Нет сгенерированных записей", "play": "Воспроизвести", "download": "Скачать"}, "errors": {"noText": "Введите текст для генерации", "noVoice": "Выберите голос", "noRecording": "Сделайте запись голоса", "recordingTooShort": "Запись слишком короткая (минимум 10 секунд)", "recordingTooLong": "Запись слишком длинная (максимум 5 минут)", "invalidFile": "Неподдерживаемый формат файла", "fileTooLarge": "Файл слишком большой (максимум 20MB)", "microphoneAccess": "Нет доступа к микрофону", "generation": "Ошибка генерации речи", "cloning": "Ошибка клонирования голоса"}, "success": {"generated": "Речь успешно сгенерирована", "cloned": "Голос успешно клонирован", "recorded": "Запись завершена"}}, "privacy": {"private": "Приватная", "privateTooltip": "Сделать эту генерацию приватной - она не появится на Dashboard"}, "blog": {"title": "Блог Uma AI", "subtitle": "Последние новости, советы и инсайты о генерации контента с помощью искусственного интеллекта", "readMore": "Читать", "backToBlog": "Назад к блогу", "shareArticle": "Поделиться", "moreArticles": "Больше статей", "articleRead": "Статья прочитана", "noArticles": "Пока нет статей", "noArticlesDesc": "Скоро здесь появятся интересные статьи о генерации контента с ИИ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publishedOn": "Опубликовано", "readTime": "мин чтения", "tags": "Теги"}}