
import React, { useEffect, useRef } from 'react';

interface FloatingParticlesProps {
  count?: number;
  className?: string;
}

const FloatingParticles: React.FC<FloatingParticlesProps> = ({ 
  count = 50,
  className = ""
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    let animationFrameId: number;
    let particles: Particle[] = [];
    
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      // Re-create particles when canvas size changes
      createParticles();
    };
    
    class Particle {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      color: string;
      opacity: number;
      
      constructor() {
        const canvasWidth = canvas?.width ?? 0;
        const canvasHeight = canvas?.height ?? 0;
        this.x = Math.random() * canvasWidth;
        this.y = Math.random() * canvasHeight;
        this.size = Math.random() * 2 + 0.5;
        this.speedX = Math.random() * 0.6 - 0.3;
        this.speedY = Math.random() * 0.6 - 0.3;
        this.color = getRandomColor();
        this.opacity = Math.random() * 0.6 + 0.1;
      }
      
      update() {
        // Move particle
        this.x += this.speedX;
        this.y += this.speedY;
        
        // Bounce off edges
        const canvasWidth = canvas?.width ?? 0;
        const canvasHeight = canvas?.height ?? 0;
        if (this.x > canvasWidth || this.x < 0) {
          this.speedX = -this.speedX;
        }
        if (this.y > canvasHeight || this.y < 0) {
          this.speedY = -this.speedY;
        }
      }
      
      draw() {
        ctx!.beginPath();
        ctx!.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx!.fillStyle = this.color + Math.floor(this.opacity * 255).toString(16);
        ctx!.fill();
      }
    }
    
    const getRandomColor = () => {
      const colors = [
        '#9333ea', // Purple
        '#6366f1', // Indigo
        '#7e22ce', // Purple dark
        '#a855f7', // Purple lighter
        '#6d28d9', // Purple dark
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    };
    
    const createParticles = () => {
      particles = [];
      for (let i = 0; i < count; i++) {
        particles.push(new Particle());
      }
    };
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });
      
      // Connect nearby particles with lines
      connectParticles();
      
      animationFrameId = requestAnimationFrame(animate);
    };
    
    const connectParticles = () => {
      const maxDistance = 150;
      
      for (let a = 0; a < particles.length; a++) {
        for (let b = a; b < particles.length; b++) {
          const dx = particles[a].x - particles[b].x;
          const dy = particles[a].y - particles[b].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < maxDistance) {
            const opacity = 1 - (distance / maxDistance);
            ctx!.beginPath();
            ctx!.strokeStyle = `rgba(147, 51, 234, ${opacity * 0.15})`;
            ctx!.lineWidth = 0.5;
            ctx!.moveTo(particles[a].x, particles[a].y);
            ctx!.lineTo(particles[b].x, particles[b].y);
            ctx!.stroke();
          }
        }
      }
    };
    
    // Initial setup
    resizeCanvas();
    createParticles();
    animate();
    
    // Handle window resize
    window.addEventListener('resize', resizeCanvas);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrameId);
    };
  }, [count]);
  
  return (
    <canvas 
      ref={canvasRef}
      className={`fixed inset-0 z-0 ${className}`}
    />
  );
};

export default FloatingParticles;
