// Fal API status endpoint for checking generation progress
// GET /api/fal/status?requestId=<request_id>&model=<model_id>

import fetch from "node-fetch";

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  console.log(`[FAL STATUS] Received ${req.method} request to ${req.url}`);
  console.log(`[FAL STATUS] Headers:`, req.headers);
  console.log(`[FAL STATUS] Body:`, req.body);
  console.log(`[FAL STATUS] Query:`, req.query);

  if (req.method === 'OPTIONS') {
    console.log(`[FAL STATUS] Handling OPTIONS request`);
    return res.status(200).end();
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    console.log(`[FAL STATUS] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Поддерживаем параметры как в query (GET), так и в body (POST)
    let requestId, model, getResult;

    if (req.method === 'GET') {
      ({ requestId, model, getResult } = req.query);
    } else if (req.method === 'POST') {
      ({ requestId, model, getResult } = req.body);
    }

    console.log('[FAL STATUS] Checking status for:', { requestId, model, getResult, method: req.method });

    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL STATUS] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Формируем URL для проверки статуса
    // FAL API использует базовый URL без полного пути модели
    let statusUrl;

    // Определяем правильный URL в зависимости от типа модели
    if (model.includes('elevenlabs/tts')) {
      // Для речевых моделей ElevenLabs
      statusUrl = `https://queue.fal.run/${model}/requests/${requestId}/status`;
    } else {
      // Для видео моделей (по умолчанию Kling)
      statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    }

    console.log('[FAL STATUS] Requesting status from:', statusUrl);

    // Отправляем запрос к Fal API
    const falResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL STATUS] API error:', errorText);
      return res.status(falResponse.status).json({
        error: 'Fal API status error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL STATUS] Status response:', result);

    // Если запрошен результат и статус COMPLETED, получаем результат
    if (getResult === 'true' && result.status === 'COMPLETED' && result.response_url) {
      console.log('[FAL STATUS] Getting result from response_url:', result.response_url);

      try {
        const resultResponse = await fetch(result.response_url, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (resultResponse.ok) {
          const resultData = await resultResponse.json();
          console.log('[FAL STATUS] Got result data');
          return res.status(200).json(resultData);
        } else {
          console.error('[FAL STATUS] Failed to get result:', resultResponse.status);
        }
      } catch (resultError) {
        console.error('[FAL STATUS] Error getting result:', resultError);
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL STATUS] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
