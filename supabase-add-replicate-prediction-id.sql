-- Добавляем поле replicate_prediction_id в таблицу video_generations для предотвращения дублирования

-- Проверяем существование колонки replicate_prediction_id в таблице video_generations
DO $$ 
BEGIN
  -- Проверяем существует ли колонка replicate_prediction_id в таблице video_generations
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'video_generations' 
      AND column_name = 'replicate_prediction_id'
  ) THEN
    -- Если колонка не существует, добавляем её
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN replicate_prediction_id TEXT';
    RAISE NOTICE 'Колонка replicate_prediction_id добавлена в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Колонка replicate_prediction_id уже существует в таблице video_generations';
  END IF;
END $$;

-- Создаем уникальный индекс для предотвращения дублирования по replicate_prediction_id
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_indexes 
    WHERE tablename = 'video_generations' 
      AND indexname = 'idx_video_generations_replicate_prediction_id'
  ) THEN
    CREATE UNIQUE INDEX idx_video_generations_replicate_prediction_id 
    ON video_generations (replicate_prediction_id) 
    WHERE replicate_prediction_id IS NOT NULL;
    
    RAISE NOTICE 'Уникальный индекс idx_video_generations_replicate_prediction_id создан';
  ELSE
    RAISE NOTICE 'Индекс idx_video_generations_replicate_prediction_id уже существует';
  END IF;
END $$;

-- Добавляем комментарий к таблице
COMMENT ON COLUMN video_generations.replicate_prediction_id IS 'ID предсказания от Replicate API для предотвращения дублирования записей';