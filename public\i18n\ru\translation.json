{"common": {"appName": "", "loading": "Загрузка...", "error": "Ошибка", "success": "Успех", "save": "Сохранить", "cancel": "Отмена", "delete": "Удалить", "edit": "Редактировать", "create": "Создать", "submit": "Отправить", "close": "Закрыть", "copied": "Скопировано в буфер обмена", "notFound": "Не найдено", "errorOccurred": "Произошла ошибка"}, "nav": {"home": "Главная", "dashboard": "Дашборд", "profile": "Профиль", "history": "История", "settings": "Настройки", "logout": "Выйти", "login": "Войти", "signUp": "Регистрация", "videoGen": "Генерация видео"}, "auth": {"login": "Вход", "register": "Регистрация", "email": "Email", "password": "Пароль", "name": "Имя", "forgotPassword": "Забыли пароль?", "resetPassword": "Сбросить пароль", "noAccount": "Нет аккаунта?", "hasAccount": "Уже есть аккаунт?", "signIn": "Войти", "signUp": "Зарегистрироваться", "signInWithGoogle": "Войти через Google", "signInSuccess": "Вход выполнен успешно!", "signUpSuccess": "Регистрация выполнена успешно!", "signInError": "Не удалось войти", "signUpError": "Не удалось зарегистрироваться", "signOutSuccess": "Выход выполнен успешно", "signOutError": "Не удалось выйти", "resetEmailSent": "Письмо для сброса пароля отправлено!", "resetPasswordError": "Не удалось отправить письмо для сброса пароля", "confirmEmail": "Пожалуйста, проверьте свою почту для подтверждения аккаунта", "invalidCredentials": "Неверный email или пароль", "loginFailed": "Ошибка входа", "invalidEmail": "Пожалуйста, введите корректный email адрес", "passwordTooShort": "Пароль должен содержать минимум 6 символов", "generalError": "Произошла ошибка. Пожалуйста, попробуйте еще раз", "emailNotConfirmed": "Email не подтвержден", "checkEmailForConfirmation": "Пожалуйста, проверьте почту и нажмите на ссылку подтверждения", "userAlreadyExists": "Пользователь с таким email уже существует", "tryLoginInstead": "Попробуйте войти или сбросить пароль", "userExistsOrInvalidPassword": "Пользователь существует, но либо email не подтвержден, либо пароль неверный", "registrationSuccessful": "Регистрация успешна!", "emailAlreadyRegistered": "Email уже зарегистрирован", "userBanned": "Пользователь заблокирован", "userLocked": "Аккаунт временно заблокирован из-за большого количества попыток входа", "passwordNotMatch": "Пароли не совпадают", "emailPlaceholder": "Введите ваш email", "passwordPlaceholder": "Введите ваш пароль", "namePlaceholder": "Введите ваше имя (необязательно)", "orContinueWith": "или продолжить с", "sendResetLink": "Отправить ссылку для сброса", "resetPasswordInstructions": "Введите ваш email, и мы отправим ссылку для сброса пароля", "backToLogin": "Вернуться к входу", "profileCreationFailed": "Не удалось создать профиль пользователя", "refreshingProfile": "Обновление информации профиля", "unexpectedError": "Произошла непредвиденная ошибка", "verifyingSession": "Проверка вашей сессии"}, "dashboard": {"welcome": "", "subtitle": "Платформа генерации контента на базе ИИ", "recentGenerations": "Недавние генерации", "newGeneration": "Новая генерация", "noGenerations": "Пока нет генераций. Создайте свою первую!", "viewAll": "Показать все", "createNew": "Создать новую", "allTab": "Все", "tabAll": "Все", "tabImage": "Изображения", "tabVideo": "Видео", "imageTab": "Изображения", "videoTab": "Видео", "loadingGenerations": "Загрузка ваших творений", "noHistory": "История генераций пуста", "noExamples": "Пока нет примеров.", "noImageExamples": "Пока нет примеров изображений.", "noVideoExamples": "Пока нет примеров видео.", "history": "История", "historyDescription": "Здесь вы можете просмотреть историю генераций.", "modal": {"copyPrompt": "Скопировать запрос", "downloadImage": "Скачать", "prompt": "Запрос", "promptCopied": "Запрос скопирован в буфер обмена!", "promptCopyError": "Не удалось скопировать запрос.", "downloadStarted": "Скачивание изображения началось!", "downloadError": "Не удалось скачать изображение. Ссылка может быть недействительной или истекла.", "generatedImageAlt": "Сгенерированное изображение", "seeLess": "Показать меньше", "seeMore": "Показать больше", "imageTitlePlaceholder": "Сгенерированное изображение", "imageDescriptionPlaceholder": "Детальный просмотр сгенерированного изображения и его опций.", "usePrompt": "Использовать промт"}}, "sidebar": {"home": "Главная", "dashboard": "Дашборд", "history": "История", "text": "Текст", "image": "Изображение", "video": "Видео"}, "profile": {"title": "Профиль", "personalInfo": "Персональная информация", "updateProfile": "Обновить профиль", "credits": "Кредиты", "creditsBalance": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> кредитов", "addCredits": "Добавить кредиты", "accountSettings": "Настройки аккаунта", "changePassword": "Изменить пароль", "deleteAccount": "Удалить аккаунт", "profileUpdated": "Профиль успешно обновлен", "refreshBalance": "Обновить баланс", "loadingProfile": "Загрузка данных профиля", "tokenAdded": "Токены успешно добавлены", "failedToAddTokens": "Не удалось добавить токены"}, "credits": {"title": "Купить кредиты", "description": "Выберите подходящий план для пополнения кредитов", "action": "Купить сейчас", "creditsLeft": "кредитов осталось", "buyMore": "Купить больше кредитов", "unlimitedCredits": "Безлимитные кредиты", "plans": {"basic": {"name": "Базовый"}, "standard": {"name": "Стандартный"}, "premium": {"name": "Премиум"}}}, "navigation": {"buyCredits": "Купить кредиты", "videoGen": "Генерация видео"}, "textGen": {"pageTitle": "Генерация текста", "initialGreeting": "Добро пожаловать! Начните генерировать текст с помощью ИИ.", "inputPlaceholder": "Введите ваш запрос здесь..."}, "generation": {"new": "Новая генерация", "image": "Изображение", "video": "Видео", "text": "Текст", "prompt": "Запрос", "style": "Стиль", "model": "Модель", "generate": "Генерировать", "generating": "Генерация...", "success": "Генерация успешна!", "error": "Генерация не удалась", "history": "История генераций", "noHistory": "Пока нет истории генераций", "downloadImage": "Скачать изображение", "copyPrompt": "Скопировать запрос", "regenerate": "Сгенерировать заново", "share": "Поделиться", "delete": "Удалить", "confirmDelete": "Вы уверены, что хотите удалить эту генерацию?", "credits": "Кредиты", "creditsRequired": "Требуемые кредиты", "insufficientCredits": "Недостаточно кредитов", "aspectRatio": "Соотношение сторон", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "noMoreHistory": "Больше нет элементов в истории", "loadingMore": "Загрузка дополнительных элементов", "copyImage": "Скопировать изображение", "generatedOn": "Создано", "modelUsed": "Используемая модель"}, "imageGen": {"alt": {"generated": "Сгенерированное изображение"}, "image": "Изображение", "images": "Изображения", "image_one": "изображение", "image_other": "изображения", "emptyState": "Пока нет сгенерированных изображений.", "settings": {"selectStyle": "Выбрать стиль", "selectModel": "Выбрать модель"}, "placeholders": {"imagePrompt": "Опишите ваше изображение..."}, "actions": {"generating": "Генерация", "download": "Скачать"}, "sections": {"generatedImages": "Сгенерированные изображения"}, "models": {"together": "Together AI", "togetherDesc": "Быстрая генерация изображений с тонкими деталями и художественными стилями", "ideogram": "Ideogram 2", "ideogramDesc": "Универсальная модель, поддерживает img to img. Хорошо справляется с текстом", "hidream": "Hi-Dream", "hidreamDesc": "Продвинутая фотореалистичная модель премиум-качества", "ideogram3": "Ideogram 3", "ideogram3Desc": "Модель нового поколения с улучшенным качеством и детализацией, поддерживает img to img. Отлично справляется с текстом", "fluxpro": "Flux Pro", "fluxproDesc": "Модель, заточенная под редактирование фотографий", "minimaxDesc": "Высоко детализированная, гиперреалистичная модель", "imagen4Desc": "Передовая модель для создания высокодетализированных изображений", "bagelDesc": "Универсальная модель с поддержкой редактирования и высоким качеством. Идеально редактирует изображения", "fluxKontextPro": "Flux Kontext Pro", "fluxKontextProDesc": "Передовая модель редактирования изображений с высоким качеством и точным следованием промптам", "fluxKontextMax": "Flux Kontext Max", "fluxKontextMaxDesc": "Премиум модель с максимальной производительностью и улучшенной генерацией типографики"}, "steps": {"starting": "Инициализация...", "creating": "Создание изображения...", "finalizing": "Обработка результатов...", "saving": "Сохранение..."}, "success": {"generation": "Изображение успешно создано!"}, "error": {"generation": "Не удалось сгенерировать изображение", "noPrompt": "Введите описание изображения", "downloadFailed": "Не удалось скачать изображение", "promptTooLong": "Описание слишком длинное, максимум 500 символов", "invalidService": "Ошибка: выбранная модель не поддерживается или параметры запроса некорректны"}, "errors": {"promptTooLong": "Описание слишком длинное, максимум 500 символов"}}, "features": {"textGenTitle": "Генерация текста", "textGenDescription": "Генерируйте качественный текст с помощью современных ИИ-моделей.", "imageGenTitle": "Генерация изображений", "imageGenDescription": "Создавайте потрясающие изображения по вашим запросам.", "videoGenTitle": "Генерация видео", "videoGenDescription": "Создавайте креативные видео с помощью ИИ.", "sectionTitle": "Возможности", "sectionSubtitle": "Что вы можете делать"}, "videoGen": {"pixverse": {"cfgScaleLabel": "CFG Scale (насколько строго следовать промпту)"}, "kling": {"cfgScaleLabel": "Возможность отклонения от промпта"}}, "showcase": {"item1Title": "ИИ-арт", "item1Prompt": "Футуристический городской пейзаж на закате", "item2Title": "Фотореализм", "item2Prompt": "Портрет молодой женщины, фотореалистичный", "item3Title": "Креативный дизайн", "item3Prompt": "Логотип для IT-стартапа", "sectionTitle": "Примеры", "sectionSubtitle": "Посмотрите, что может Ума Ai"}, "pricing": {"testPlan": {"title": "Тестовый план", "price": "1 ₽", "description": "Тестовый план только для разработки", "features": ["1000 кредитов"], "button": "Тест"}, "basicPlan": {"title": "Базовый", "price": "300 ₽", "description": "Пакет кредитов для начинающих", "features": ["600 кредитов"], "button": "Выбрать Базовый"}, "standardPlan": {"title": "Стандартный", "price": "750 ₽", "description": "Самый популярный выбор", "features": ["1500 кредитов"], "button": "Выбрать Стандартный"}, "proPlan": {"title": "Профессиональный", "price": "2000 ₽", "description": "Для активных пользователей", "features": ["4000 кредитов"], "button": "Выбрать Профессиональный"}, "sectionTitle": "Тарифы", "sectionSubtitle": "Выберите свой план", "footer": "Все тарифы включают доступ к возможностям Ума Ai"}, "history": {"loading": "Загрузка истории...", "loadingMore": "Загрузка...", "loadMore": "Загрузить еще", "noMoreGenerations": "Больше генераций нет.", "noGenerations": "У вас пока нет генераций.", "tryCreating": "Попробуйте создать что-нибудь!", "downloadImage": "Скачать изображение"}, "buyCredits": {"buttonText": "Купить кредиты", "title": "Купить кредиты", "description": "Выберите подходящий план для пополнения кредитов", "credits": "креди<PERSON><PERSON>", "testPlan": "Тестовый план", "test": "Тест", "pleaseSelectPlan": "Пожалуйста, выберите план", "creatingPayment": "Создание платежа...", "redirecting": "Перенаправление на страницу оплаты...", "loading": "Загрузка...", "proceedToPayment": "Перейти к оплате", "errors": {"invalidPaymentResponse": "Некорректный ответ от платежного API", "noConfirmationUrl": "Не получен URL подтверждения", "paymentCreation": "Ошибка создания платежа", "unknown": "Неизвестная ошибка"}}, "language": {"switch": "Сменить язык", "select": "Выбрать язык"}, "header": {"toggleTheme": "Переключить тему", "loginButton": "Войти", "signUpButton": "Регистрация", "logout": "Выйти", "imageGen": "Генерация изображений"}, "landing": {"welcomeTo": "", "appName": "", "introducing": "Представляем", "subtitle": "Платформа генерации контента на базе ИИ", "getStarted": "Начать", "viewDemos": "Посмотреть демо", "ctaTitle": "Готовы попробовать Ума Ai?", "ctaSubtitle": "Зарегистрируйтесь и начните генерировать контент с помощью ИИ!"}, "login": {"title": "Вход в аккаунт", "subtitle": "Введите свои данные для доступа к Ума Ai", "footer": "Нет аккаунта? Зарегистрируйтесь."}, "home": {"title": "Главная", "subtitle": "Генерируйте с помощью ИИ"}, "chats": "Чаты"}