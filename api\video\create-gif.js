import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import os from 'os';
import fs from 'fs/promises';

const execAsync = promisify(exec);

// Инициализация Supabase клиента для использования в API
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Используем Service Role Key для операций на бэкенде

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const { videoUrl, generationId, userId } = req.body;

  if (!videoUrl || !generationId || !userId) {
    res.status(400).json({ error: "Missing videoUrl, generationId, or userId" });
    return;
  }

  let tempVideoPath = null;
  let tempGifPath = null;

  try {
    console.log(`[CREATE GIF API] Received request for generation ${generationId}, videoUrl: ${videoUrl}`);

    // 1. Скачать видеофайл
    const response = await fetch(videoUrl);
    if (!response.ok) {
      throw new Error(`Failed to download video: ${response.statusText}`);
    }

    // Создаем временную директорию для файлов
    const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'video-gif-'));
    tempVideoPath = path.join(tempDir, `video_${generationId}.mp4`);
    tempGifPath = path.join(tempDir, `preview_${generationId}.gif`);

    const fileStream = await fs.open(tempVideoPath, 'w');
    await response.body.pipeTo(fileStream.writable);
    await fileStream.close();

    console.log(`[CREATE GIF API] Video downloaded to ${tempVideoPath}`);

    // 2. Конвертировать видео в GIF с помощью FFmpeg
    // Убедитесь, что FFmpeg установлен и доступен в PATH на сервере, где будет развернут этот API.
    // Пример команды FFmpeg для создания GIF:
    // ffmpeg -i input.mp4 -vf "fps=10,scale=320:-1:flags=lanczos" -c:v gif -f gif output.gif
    // fps=10: 10 кадров в секунду (можно настроить)
    // scale=320:-1: масштабировать ширину до 320px, сохраняя пропорции (можно настроить)
    // flags=lanczos: алгоритм масштабирования
    // -c:v gif: кодек GIF
    // -f gif: формат вывода GIF
    // -ss 0 -t 5: взять первые 5 секунд видео (опционально, для коротких превью)

    // Проверяем, существует ли FFmpeg
    try {
        await execAsync('ffmpeg -version');
        console.log('[CREATE GIF API] FFmpeg found.');
    } catch (ffmpegCheckError) {
        console.error('[CREATE GIF API] FFmpeg not found. Please ensure FFmpeg is installed and in the server\'s PATH.');
        // В случае отсутствия FFmpeg, возвращаем ошибку, но не прерываем процесс
        // Можно добавить логику для отправки уведомления или сохранения статуса ошибки в БД
        res.status(500).json({ error: "FFmpeg not found on the server." });
        return; // Прерываем выполнение API, так как конвертация невозможна
    }


    const ffmpegCommand = `ffmpeg -i "${tempVideoPath}" -vf "fps=10,scale=320:-1:flags=lanczos" -c:v gif -f gif "${tempGifPath}"`;
    console.log(`[CREATE GIF API] Executing FFmpeg command: ${ffmpegCommand}`);

    try {
        const { stdout, stderr } = await execAsync(ffmpegCommand);
        console.log('[CREATE GIF API] FFmpeg stdout:', stdout);
        console.error('[CREATE GIF API] FFmpeg stderr:', stderr); // FFmpeg часто пишет прогресс в stderr
    } catch (ffmpegError) {
        console.error('[CREATE GIF API] Error during FFmpeg conversion:', ffmpegError);
        throw new Error(`FFmpeg conversion failed: ${ffmpegError.message}`);
    }

    console.log(`[CREATE GIF API] Video converted to GIF: ${tempGifPath}`);

    // 3. Загрузить GIF в Supabase Storage
    const gifBlob = await fs.readFile(tempGifPath);
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('generation_thumbnails') // Убедитесь, что у вас есть бакет 'generation_thumbnails'
      .upload(`${userId}/${generationId}.gif`, gifBlob, {
        contentType: 'image/gif',
        upsert: true // Обновляем, если файл уже существует
      });

    if (uploadError) {
      console.error('[CREATE GIF API] Error uploading GIF to Supabase Storage:', uploadError);
      throw new Error(`Failed to upload GIF to storage: ${uploadError.message}`);
    }

    // Получить публичный URL загруженного GIF
    const { data: publicUrlData } = supabase.storage
      .from('generation_thumbnails')
      .getPublicUrl(`${userId}/${generationId}.gif`);

    const publicGifUrl = publicUrlData.publicUrl;
    console.log(`[CREATE GIF API] GIF uploaded to Supabase Storage: ${publicGifUrl}`);

    // 4. Обновить запись в базе данных ссылкой на GIF
    const { error: dbUpdateError } = await supabase
      .from('video_generations')
      .update({ thumbnail_url: publicGifUrl })
      .eq('id', generationId);

    if (dbUpdateError) {
      console.error('[CREATE GIF API] Error updating database with GIF URL:', dbUpdateError);
      // Не выбрасываем ошибку, так как GIF уже загружен, но логируем проблему с БД
    } else {
      console.log(`[CREATE GIF API] Database updated with thumbnail_url for generation ${generationId}`);
    }

    res.status(200).json({ success: true, gifUrl: publicGifUrl });

  } catch (error) {
    console.error('[CREATE GIF API] Overall error:', error);
    res.status(500).json({ error: error.message || 'Failed to create GIF thumbnail' });
  } finally {
    // Очистка временных файлов и директории
    if (tempVideoPath) {
      try {
        await fs.unlink(tempVideoPath);
        console.log(`[CREATE GIF API] Cleaned up temporary video file: ${tempVideoPath}`);
      } catch (e) { console.error('[CREATE GIF API] Failed to clean up video file:', e); }
    }
    if (tempGifPath) {
       try {
        await fs.unlink(tempGifPath);
        console.log(`[CREATE GIF API] Cleaned up temporary GIF file: ${tempGifPath}`);
      } catch (e) { console.error('[CREATE GIF API] Failed to clean up GIF file:', e); }
    }
     if (tempVideoPath) { // Проверяем директорию по одному из путей
        try {
            const tempDir = path.dirname(tempVideoPath);
            await fs.rmdir(tempDir, { recursive: true }); // Удаляем директорию рекурсивно
            console.log(`[CREATE GIF API] Cleaned up temporary directory: ${tempDir}`);
        } catch (e) { console.error('[CREATE GIF API] Failed to clean up temporary directory:', e); }
    }
  }
}