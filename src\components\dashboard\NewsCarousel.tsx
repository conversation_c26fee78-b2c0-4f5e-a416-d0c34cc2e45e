import { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface NewsItem {
  id: string;
  image?: string;
  video?: string;
  titleRu: string;
  titleEn: string;
  descriptionRu: string;
  descriptionEn: string;
  route: string;
  model?: string;
}

const newsItems: NewsItem[] = [
  {
    id: 'veo3',
    video: '/veo3-banner.webm',
    titleRu: 'Создавай с Veo3',
    titleEn: 'Make with Veo3',
    descriptionRu: 'Передовая модель Google\nс автоматическими звуковыми\nэффектами и озвучкой',
    descriptionEn: 'Advanced Google model\nwith automatic sound effects\nand voice generation',
    route: '/video',
    model: 'fal-ai/veo3'
  },
  {
    id: 'kling21',
    video: '/trykling2.1.webm',
    titleRu: 'Генерируй с Kling 2.1',
    titleEn: 'Generate with Kling 2.1',
    descriptionRu: 'Создавай невероятные видео\nиз изображений с новейшей\nмоделью Kling 2.1',
    descriptionEn: 'Create incredible videos\nfrom images with the latest\nKling 2.1 model',
    route: '/video',
    model: 'fal-ai/kling-video/v2.1/standard'
  },
  {
    id: 'imagen4',
    image: '/tryimagen4.png',
    titleRu: 'Генерируй с Imagen 4',
    titleEn: 'Generate with Imagen 4',
    descriptionRu: 'Создавай фотореалистичные\nизображения с невероятной\nдетализацией и качеством',
    descriptionEn: 'Create photorealistic images\nwith incredible detail\nand quality',
    route: '/image',
    model: 'imagen4'
  },
  {
    id: 'flux-kontext',
    image: '/tryfluxkontext.png',
    titleRu: 'Редактируй с Flux Kontext',
    titleEn: 'Edit with Flux Kontext',
    descriptionRu: 'Редактировать картинки еще\nникогда не было так просто.\nПопробуй Flux Kontext Pro или Max',
    descriptionEn: 'Editing images has never been\nso easy. Try Flux Kontext\nPro or Max for editing',
    route: '/image',
    model: 'flux-kontext-pro'
  },
  {
    id: 'elevenlabs',
    image: '/try11labs.webp',
    titleRu: 'Озвучивай с 11labs',
    titleEn: 'Voice with 11labs',
    descriptionRu: 'Добавлены передовые модели\nTTS для создания реалистичной\nречи с естественными голосами',
    descriptionEn: 'Advanced TTS models added\nfor creating realistic speech\nwith natural voices',
    route: '/speech',
    model: 'fal-ai/elevenlabs/tts/turbo-v2.5'
  }
];

const NewsCarousel = () => {
  const { i18n } = useTranslation();
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const isRussian = i18n.language.startsWith('ru');

  // Drag scrolling handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTryClick = (item: NewsItem) => {
    if (item.model) {
      // Перенаправляем с параметром модели
      navigate(`${item.route}?model=${item.model}`);
    } else {
      navigate(item.route);
    }
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    if (scrollContainerRef.current) {
      const itemWidth = 566; // 560px width + 6px gap
      scrollContainerRef.current.scrollTo({
        left: index * itemWidth,
        behavior: 'smooth'
      });
    }
  };

  // Update current index based on scroll position
  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const itemWidth = 566;
      const scrollLeft = scrollContainerRef.current.scrollLeft;
      const newIndex = Math.round(scrollLeft / itemWidth);
      setCurrentIndex(Math.min(newIndex, newsItems.length - 1));
    }
  };

  return (
    <div className="w-full mb-8">
      {/* Carousel container */}
      <div
        ref={scrollContainerRef}
        className="overflow-x-auto scrollbar-none cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeave}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onScroll={handleScroll}
        style={{ userSelect: 'none' }}
      >
        <div className="flex gap-6 px-2 min-w-max">
          {newsItems.map((item) => (
            <div
              key={item.id}
              className="relative group flex-shrink-0 w-[560px] h-[315px] rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:contrast-110"
              style={{ aspectRatio: '16/9' }}
            >
              {/* Background image or video */}
              {item.video ? (
                <video
                  src={item.video}
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full h-full object-cover transition-all duration-300"
                  draggable={false}
                />
              ) : (
                <img
                  src={item.image}
                  alt={isRussian ? item.titleRu : item.titleEn}
                  className="w-full h-full object-cover transition-all duration-300"
                  draggable={false}
                />
              )}
              
              {/* Soft black glow overlay extending beyond edges */}
              <div className="absolute -bottom-4 -left-4 w-full h-full pointer-events-none">
                {/* Main soft glow extending beyond container */}
                <div className="absolute bottom-0 left-0 w-[90%] h-[70%] bg-gradient-to-tr from-black/90 via-black/60 to-transparent blur-lg opacity-80" />

                {/* Additional soft layers for depth */}
                <div className="absolute bottom-0 left-0 w-[70%] h-[50%] bg-gradient-to-r from-black/80 via-black/40 to-transparent blur-md opacity-70" />

                <div className="absolute bottom-0 left-0 w-[50%] h-[40%] bg-gradient-to-t from-black/85 to-transparent blur-sm opacity-75" />

                {/* Soft circular glows */}
                <div className="absolute bottom-2 left-2 w-40 h-40 bg-black/60 rounded-full blur-2xl opacity-50" />
                <div className="absolute bottom-8 left-8 w-24 h-24 bg-black/70 rounded-full blur-xl opacity-40" />

                {/* Final feathered edge extending beyond */}
                <div className="absolute bottom-0 left-0 w-[80%] h-[60%] bg-gradient-to-tr from-black/70 via-black/30 to-transparent blur-lg opacity-60" />
              </div>
              
              {/* Title and description in bottom left */}
              <div className="absolute bottom-6 left-6 z-10 max-w-[60%]">
                <h3 className="text-white font-bold text-lg leading-tight drop-shadow-lg mb-2">
                  {isRussian ? item.titleRu : item.titleEn}
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed drop-shadow-md whitespace-pre-line">
                  {isRussian ? item.descriptionRu : item.descriptionEn}
                </p>
              </div>
              
              {/* Try button in bottom right */}
              <div className="absolute bottom-6 right-6 z-10">
                <button
                  onClick={() => handleTryClick(item)}
                  className="bg-white text-black px-4 py-2 rounded-lg font-bold hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                  {isRussian ? 'Попробуй' : 'Try'}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dots indicator */}
      {newsItems.length > 1 && (
        <div className="flex justify-center mt-4 gap-2">
          {newsItems.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200",
                index === currentIndex
                  ? "bg-black dark:bg-white"
                  : "bg-black/30 dark:bg-white/30 hover:bg-black/50 dark:hover:bg-white/50"
              )}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default NewsCarousel;
