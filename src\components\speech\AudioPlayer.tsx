import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, Download, Volume2, VolumeX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/utils/audioUtils';
import { toast } from 'sonner';

interface AudioPlayerProps {
  src: string;
  title?: string;
  className?: string;
  showDownload?: boolean;
  autoPlay?: boolean;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  src,
  title,
  className,
  showDownload = true,
  autoPlay = false
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const audio = new Audio(src);
    audioRef.current = audio;

    // Обработчики событий
    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
      if (autoPlay) {
        audio.play();
        setIsPlaying(true);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      setError('Ошибка загрузки аудио');
      setIsLoading(false);
    };

    const handleCanPlay = () => {
      setIsLoading(false);
    };

    // Добавляем обработчики
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);

    // Устанавливаем громкость
    audio.volume = volume;

    return () => {
      // Очистка
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.pause();
    };
  }, [src, autoPlay, volume]);

  // Переключение воспроизведения
  const togglePlayPause = () => {
    if (!audioRef.current || error) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play().catch((err) => {
        console.error('Play error:', err);
        setError('Ошибка воспроизведения');
      });
      setIsPlaying(true);
    }
  };

  // Перемотка
  const handleSeek = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressRef.current || error) return;

    const rect = progressRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // Переключение звука
  const toggleMute = () => {
    if (!audioRef.current) return;

    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  // Изменение громкости
  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
      setIsMuted(newVolume === 0);
    }
  };

  // Скачивание файла
  const handleDownload = async () => {
    try {
      const response = await fetch(src);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = title ? `${title}.mp3` : `audio-${Date.now()}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
      toast.success('Файл загружен');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Ошибка загрузки файла');
    }
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (error) {
    return (
      <div className={cn("p-4 bg-destructive/10 border border-destructive/20 rounded-lg", className)}>
        <div className="text-sm text-destructive">{error}</div>
      </div>
    );
  }

  return (
    <div className={cn("bg-card border rounded-lg p-4 space-y-3", className)}>
      {/* Заголовок */}
      {title && (
        <div className="text-sm font-medium text-foreground truncate">
          {title}
        </div>
      )}

      {/* Основные элементы управления */}
      <div className="flex items-center gap-3">
        {/* Кнопка воспроизведения */}
        <Button
          onClick={togglePlayPause}
          variant="default"
          size="sm"
          className="flex-shrink-0"
          disabled={isLoading || error !== null}
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          ) : isPlaying ? (
            <Pause size={16} />
          ) : (
            <Play size={16} />
          )}
        </Button>

        {/* Прогресс-бар */}
        <div className="flex-1 space-y-1">
          <div
            ref={progressRef}
            className="h-2 bg-muted rounded-full cursor-pointer relative overflow-hidden"
            onClick={handleSeek}
          >
            <div
              className="h-full bg-primary rounded-full transition-all duration-150"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          {/* Время */}
          <div className="flex justify-between text-xs text-foreground/60">
            <span>{formatDuration(currentTime * 1000)}</span>
            <span>{formatDuration(duration * 1000)}</span>
          </div>
        </div>

        {/* Управление громкостью */}
        <div className="flex items-center gap-2">
          <Button
            onClick={toggleMute}
            variant="ghost"
            size="sm"
            className="flex-shrink-0 p-1"
          >
            {isMuted || volume === 0 ? (
              <VolumeX size={16} />
            ) : (
              <Volume2 size={16} />
            )}
          </Button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-muted rounded-lg appearance-none cursor-pointer slider"
          />
        </div>

        {/* Кнопка скачивания */}
        {showDownload && (
          <Button
            onClick={handleDownload}
            variant="ghost"
            size="sm"
            className="flex-shrink-0 p-1"
            title="Скачать аудио"
          >
            <Download size={16} />
          </Button>
        )}
      </div>

      {/* Дополнительная информация */}
      {isLoading && (
        <div className="text-xs text-foreground/60 text-center">
          Загрузка аудио...
        </div>
      )}
    </div>
  );
};

export default AudioPlayer;