// Правильная интеграция с FAL API согласно документации
// POST /api/fal-correct

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo, updateUserCredits } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL CORRECT] ${req.method} request received`);
  console.log('[FAL CORRECT] Request body keys:', Object.keys(req.body || {}));
  console.log('[FAL CORRECT] Full request body:', JSON.stringify(req.body, null, 2));



  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    if (!req.body) {
      console.error('[FAL CORRECT] No request body received');
      return res.status(400).json({ error: 'No request body' });
    }

    const { model, userId, inputKeys, ...input } = req.body;

    console.log('[FAL CORRECT] Processing request:', { model, userId, inputKeys });
    console.log('[FAL CORRECT] Extracted input:', input);
    console.log('[FAL CORRECT] Input keys:', Object.keys(input));
    console.log('[FAL CORRECT] Prompt value:', input.prompt);

    if (!model || !userId) {
      console.error('[FAL CORRECT] Missing required parameters:', { model: !!model, userId: !!userId });
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL CORRECT] FAL_KEY not found');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Проверяем формат ключа
    console.log('[FAL CORRECT] FAL Key format check:', {
      exists: !!falKey,
      length: falKey.length,
      startsCorrectly: falKey.startsWith('fal_') || falKey.startsWith('sk-'),
      firstChars: falKey.substring(0, 10)
    });

    // Определяем стоимость
    let cost = 100;
    if (model.includes('standard')) cost = 75;
    else if (model.includes('pro')) cost = 120;
    else if (model.includes('master')) cost = 320;

    console.log(`[FAL CORRECT] Generation cost: ${cost} credits`);

    // Списываем кредиты с правильной обработкой ошибок
    console.log(`[FAL CORRECT] Deducting ${cost} credits from user ${userId}`);
    try {
      await updateUserCredits(userId, -cost, 'generation', `FAL Video Generation (${model})`);
      console.log(`[FAL CORRECT] Credits deducted successfully`);
    } catch (creditError) {
      console.error('[FAL CORRECT] Error deducting credits:', creditError);
      // Если пользователь не найден или недостаточно кредитов, возвращаем понятную ошибку
      if (creditError.message?.includes('JSON object requested') || creditError.message?.includes('uuid')) {
        return res.status(400).json({ error: 'Пользователь не найден или неверный ID пользователя' });
      }
      return res.status(400).json({ error: creditError.message || 'Недостаточно кредитов или ошибка списания' });
    }

    // Обрабатываем входные данные
    let processedInput = { ...input };
    console.log('[FAL CORRECT] Input received:', processedInput);

    // Проверяем, что промпт не пустой
    if (!processedInput.prompt || processedInput.prompt.trim() === '') {
      console.error('[FAL CORRECT] Empty or missing prompt:', processedInput.prompt);
      return res.status(400).json({ error: 'Prompt is required and cannot be empty' });
    }

    // Временно отключаем обработку изображений для отладки
    if (processedInput?.image_url) {
      console.log(`[FAL CORRECT] Image URL provided: ${processedInput.image_url}`);
      console.log('[FAL CORRECT] Skipping image processing for debugging');
      // Оставляем оригинальный URL без обработки
    }

    // Удаляем служебные параметры (они могут быть как в input, так и на верхнем уровне)
    delete processedInput.metadata_visibility;
    delete processedInput.inputKeys;
    delete processedInput.model;
    delete processedInput.userId;

    console.log('[FAL CORRECT] Processed input after cleanup:', processedInput);
    console.log('[FAL CORRECT] Final prompt value:', processedInput.prompt);

    // ПРОБЛЕМА НАЙДЕНА: промпт теряется при создании falInput
    console.log('[FAL CORRECT] DEBUG - processedInput.prompt:', processedInput.prompt);
    console.log('[FAL CORRECT] DEBUG - processedInput keys:', Object.keys(processedInput));

    // Создаем правильную структуру input для FAL API
    const falInput = {};

    // Добавляем поля по одному с проверкой
    if (processedInput.prompt) {
      falInput.prompt = processedInput.prompt;
      console.log('[FAL CORRECT] Added prompt to falInput:', falInput.prompt.substring(0, 50) + '...');
    } else {
      console.error('[FAL CORRECT] ERROR: No prompt in processedInput!');
    }

    if (processedInput.image_url) {
      falInput.image_url = processedInput.image_url;
      console.log('[FAL CORRECT] Added image_url to falInput');
    }

    if (processedInput.duration) {
      falInput.duration = processedInput.duration;
      console.log('[FAL CORRECT] Added duration to falInput:', falInput.duration);
    }

    if (processedInput.aspect_ratio) {
      falInput.aspect_ratio = processedInput.aspect_ratio;
      console.log('[FAL CORRECT] Added aspect_ratio to falInput:', falInput.aspect_ratio);
    }

    if (processedInput.negative_prompt) {
      falInput.negative_prompt = processedInput.negative_prompt;
      console.log('[FAL CORRECT] Added negative_prompt to falInput');
    }

    if (processedInput.cfg_scale !== undefined) {
      falInput.cfg_scale = processedInput.cfg_scale;
      console.log('[FAL CORRECT] Added cfg_scale to falInput:', falInput.cfg_scale);
    }

    console.log('[FAL CORRECT] Final FAL input:', falInput);

    // Проверяем промпт на опасные слова
    const dangerousWords = ['explode', 'explosion', 'debris', 'shards', 'sparks flying', 'destroy', 'violence'];
    const promptLower = falInput.prompt?.toLowerCase() || '';
    const foundDangerousWords = dangerousWords.filter(word => promptLower.includes(word));

    if (foundDangerousWords.length > 0) {
      console.warn('[FAL CORRECT] WARNING: Prompt contains potentially dangerous words:', foundDangerousWords);
      console.warn('[FAL CORRECT] This may cause FAL API to reject the request');
    }

    // Отправляем запрос в FAL API используя правильный метод
    console.log('[FAL CORRECT] Sending request to FAL API:', { model, input: falInput });
    console.log('[FAL CORRECT] FAL API URL:', `https://queue.fal.run/${model}`);
    console.log('[FAL CORRECT] Request body:', JSON.stringify({ input: falInput }, null, 2));

    let result;
    try {
      // Пробуем разные варианты запроса к FAL API
      console.log('[FAL CORRECT] Trying FAL API request...');

      // Вариант 1: Стандартный queue endpoint
      let submitResponse = await fetch(`https://queue.fal.run/${model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Key ${falKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'FAL-Client/1.0'
        },
        body: JSON.stringify(falInput)
      });

      // Если не сработало, пробуем без слэша в конце
      if (!submitResponse.ok && submitResponse.status === 404) {
        console.log('[FAL CORRECT] Trying alternative endpoint format...');
        submitResponse = await fetch(`https://queue.fal.run/${model}/`, {
          method: 'POST',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'FAL-Client/1.0'
          },
          body: JSON.stringify(falInput)
        });
      }

      console.log(`[FAL CORRECT] FAL API response status: ${submitResponse.status}`);
      console.log(`[FAL CORRECT] FAL API response headers:`, Object.fromEntries(submitResponse.headers.entries()));

      if (!submitResponse.ok) {
        let errorData;
        const responseText = await submitResponse.text();
        console.error('[FAL CORRECT] FAL API raw response:', responseText);

        try {
          errorData = JSON.parse(responseText);
        } catch (parseError) {
          errorData = responseText;
          console.error('[FAL CORRECT] Could not parse error response as JSON');
        }

        console.error('[FAL CORRECT] FAL API error response:', errorData);
        console.error('[FAL CORRECT] FAL API error status:', submitResponse.status);
        console.error('[FAL CORRECT] FAL API error headers:', Object.fromEntries(submitResponse.headers.entries()));

        // Возвращаем кредиты при ошибке
        try {
          await updateUserCredits(userId, cost);
        } catch (refundError) {
          console.error('[FAL CORRECT] Error refunding credits:', refundError);
        }

        return res.status(submitResponse.status).json({
          error: 'FAL API error',
          details: errorData,
          status: submitResponse.status,
          rawResponse: responseText
        });
      }

      result = await submitResponse.json();
      console.log('[FAL CORRECT] FAL API response:', result);
    } catch (falError) {
      console.error('[FAL CORRECT] FAL API request failed:', falError);
      console.error('[FAL CORRECT] Error name:', falError.name);
      console.error('[FAL CORRECT] Error message:', falError.message);
      console.error('[FAL CORRECT] Error stack:', falError.stack);

      // Возвращаем кредиты при ошибке
      await updateUserCredits(userId, cost);

      return res.status(500).json({
        error: 'FAL API request failed',
        details: falError.message,
        errorName: falError.name
      });
    }

    // ОТКЛЮЧАЕМ ФОНОВЫЙ ПОЛЛИНГ - делаем все в реальном времени
    console.log(`[FAL CORRECT] Request submitted successfully: ${result.request_id}`);

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL CORRECT] Handler error:', error);
    console.error('[FAL CORRECT] Error name:', error.name);
    console.error('[FAL CORRECT] Error message:', error.message);
    console.error('[FAL CORRECT] Error stack:', error.stack);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message,
      errorName: error.name
    });
  }
}

// Функция для получения размеров по соотношению сторон
function getSizeByAspect(aspectRatio) {
  const aspectMap = {
    "16:9": { width: 576, height: 1024 },
    "9:16": { width: 576, height: 1024 },
    "1:1": { width: 768, height: 768 }
  };
  return aspectMap[aspectRatio] || aspectMap["16:9"];
}

// Функция для обработки и кропа изображения
async function downloadAndCropImage(imageUrl, targetWidth, targetHeight) {
  try {
    console.log(`[FAL CORRECT] Downloading image from: ${imageUrl}`);

    // Простая загрузка без сложной обработки
    const response = await fetch(imageUrl, {
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; FAL-API/1.0)',
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.buffer();
    console.log(`[FAL CORRECT] Downloaded image, size: ${buffer.length} bytes`);

    // Определяем формат по заголовкам или URL
    const contentType = response.headers.get('content-type') || '';
    let format = 'jpg';
    if (contentType.includes('png')) format = 'png';
    else if (contentType.includes('webp')) format = 'webp';
    else if (imageUrl.includes('.png')) format = 'png';
    else if (imageUrl.includes('.webp')) format = 'webp';

    return {
      buffer: buffer,
      format: format,
      width: targetWidth,
      height: targetHeight
    };
  } catch (error) {
    console.error('[FAL CORRECT] Error processing image:', error);
    throw new Error(`Image processing failed: ${error.message}`);
  }
}
