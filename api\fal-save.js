// Simple FAL save endpoint - immediately tries to get and save result
// POST /api/fal-save

import fetch from "node-fetch";
import { saveVideoGeneration, downloadFile, uploadVideoToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL SAVE] ${req.method} request received`);

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, prompt, visibility = 'public' } = req.body;

    console.log('[FAL SAVE] Processing:', { requestId, model, userId });

    if (!requestId || !model || !userId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL SAVE] FAL_KEY not found');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Get status first
    const statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    console.log('[FAL SAVE] Checking status:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL SAVE] Status error:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Status check failed',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL SAVE] Status:', statusData.status);

    if (statusData.status !== 'COMPLETED') {
      console.log('[FAL SAVE] Generation not completed yet, will retry later');
      return res.status(202).json({
        message: 'Generation not completed yet',
        status: statusData.status
      });
    }

    // Get result using response_url
    const resultUrl = statusData.response_url;
    console.log('[FAL SAVE] Getting result from:', resultUrl);

    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL SAVE] FAL error:', errorText);
      return res.status(falResponse.status).json({
        error: 'Failed to get result',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL SAVE] Got result, has video:', !!result.video);

    if (!result.video || !result.video.url) {
      console.error('[FAL SAVE] No video URL in result');
      return res.status(400).json({ error: 'No video URL in result' });
    }

    // Save to database
    try {
      console.log('[FAL SAVE] Saving to database...');
      const savedGeneration = await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility, requestId);
      console.log('[FAL SAVE] Saved successfully with ID:', savedGeneration.id);

      const responseData = {
        success: true,
        generationId: savedGeneration.id,
        videoUrl: savedGeneration.video_url,
        url: savedGeneration.video_url // Добавляем дублирующее поле для совместимости
      };

      console.log('[FAL SAVE] Returning response:', responseData);
      return res.status(200).json(responseData);
    } catch (saveError) {
      console.error('[FAL SAVE] Save error:', saveError);
      return res.status(500).json({
        error: 'Failed to save to database',
        details: saveError.message
      });
    }

  } catch (error) {
    console.error('[FAL SAVE] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[FAL SAVE] Downloading video: ${videoUrl}`);
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[FAL SAVE] Downloaded ${videoBuffer.length} bytes`);

    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[FAL SAVE] Uploaded to: ${supabaseVideoUrl}`);

    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[FAL SAVE] Thumbnail: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[FAL SAVE] Thumbnail error:`, thumbnailError);
    }

    let cost = 100;
    if (model.includes('standard')) cost = 75;
    else if (model.includes('pro')) cost = 120;
    else if (model.includes('master')) cost = 320;

    const savedGeneration = await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      duration: 5,
      cost: cost,
      visibility: visibility
    });

    console.log(`[FAL SAVE] Saved with ID: ${savedGeneration.id}`);

    // Start GIF creation
    createGifAsync(supabaseVideoUrl, savedGeneration.id, userId).catch(error => {
      console.error(`[FAL SAVE] GIF error:`, error);
    });

    return savedGeneration;

  } catch (error) {
    console.error(`[FAL SAVE] Database error:`, error);
    throw error;
  }
}

async function createGifAsync(videoUrl, generationId, userId) {
  try {
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'https://umaai.site';
    await fetch(`${baseUrl}/api/video/create-gif`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ videoUrl, generationId, userId })
    });
    console.log(`[FAL SAVE] GIF creation started for ${generationId}`);
  } catch (error) {
    console.error(`[FAL SAVE] GIF request error:`, error);
  }
}
