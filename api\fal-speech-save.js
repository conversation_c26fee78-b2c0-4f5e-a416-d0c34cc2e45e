// Simple FAL speech save endpoint - immediately tries to get and save result
// POST /api/fal-speech-save

import fetch from "node-fetch";
import { saveSpeechGeneration, downloadFile, uploadAudioToSupabase } from "./utils/database.js";

export default async function handler(req, res) {
  console.log(`[FAL SPEECH SAVE] ${req.method} request received`);

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, text, voice, visibility = 'public' } = req.body;

    console.log('[FAL SPEECH SAVE] Processing:', { requestId, model, userId });

    if (!requestId || !model || !userId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL SPEECH SAVE] FAL_KEY not found');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Сначала получаем статус, чтобы узнать response_url
    // Для ElevenLabs используем сокращенную версию модели как в ответах FAL API
    const modelForStatus = model.includes('elevenlabs/tts')
      ? model.replace('/tts/turbo-v2.5', '').replace('/tts/multilingual-v2', '')
      : model;
    const statusUrl = `https://queue.fal.run/${modelForStatus}/requests/${requestId}/status`;
    console.log('[FAL SPEECH SAVE] Checking status at:', statusUrl);
    console.log('[FAL SPEECH SAVE] Original model:', model, 'Model for status:', modelForStatus);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL SPEECH SAVE] Status check failed:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Status check failed',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL SPEECH SAVE] Status data:', statusData);

    if (statusData.status !== 'COMPLETED') {
      console.log('[FAL SPEECH SAVE] Generation not completed yet, status:', statusData.status);
      return res.status(202).json({
        message: 'Generation not completed yet',
        status: statusData.status
      });
    }

    // Используем response_url из статуса для получения результата
    const resultUrl = statusData.response_url;
    console.log('[FAL SPEECH SAVE] Getting result from response_url:', resultUrl);

    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL SPEECH SAVE] FAL API error:', {
        status: falResponse.status,
        statusText: falResponse.statusText,
        url: resultUrl,
        error: errorText
      });
      return res.status(falResponse.status).json({
        error: 'Failed to get result',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL SPEECH SAVE] Got result:', result);

    // Проверяем наличие аудио в результате
    const audioData = result.data || result;
    console.log('[FAL SPEECH SAVE] Audio data:', audioData);

    if (!audioData.audio || !audioData.audio.url) {
      console.error('[FAL SPEECH SAVE] No audio URL in result');
      return res.status(400).json({ error: 'No audio URL in result' });
    }

    // Save to database
    try {
      console.log('[FAL SPEECH SAVE] Saving to database...');
      const savedGeneration = await saveSpeechToDatabase(audioData.audio.url, model, userId, text, voice, visibility, requestId);
      console.log('[FAL SPEECH SAVE] Saved successfully with ID:', savedGeneration.id);

      const responseData = {
        success: true,
        generationId: savedGeneration.id,
        audioUrl: savedGeneration.audio_url,
        url: savedGeneration.audio_url, // Добавляем дублирующее поле для совместимости
        audio: {
          url: savedGeneration.audio_url,
          content_type: audioData.audio.content_type,
          file_name: audioData.audio.file_name,
          file_size: audioData.audio.file_size
        }
      };

      console.log('[FAL SPEECH SAVE] Returning response:', responseData);
      return res.status(200).json(responseData);
    } catch (saveError) {
      console.error('[FAL SPEECH SAVE] Save error:', saveError);
      return res.status(500).json({
        error: 'Failed to save to database',
        details: saveError.message
      });
    }

  } catch (error) {
    console.error('[FAL SPEECH SAVE] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

async function saveSpeechToDatabase(audioUrl, model, userId, text, voice, visibility, falRequestId) {
  try {
    console.log(`[FAL SPEECH SAVE] Downloading audio: ${audioUrl}`);
    const audioBuffer = await downloadFile(audioUrl);
    console.log(`[FAL SPEECH SAVE] Downloaded ${audioBuffer.length} bytes`);

    const { v4: uuidv4 } = await import('uuid');
    const audioId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${audioId}/${timestamp}-audio.mp3`;

    const supabaseAudioUrl = await uploadAudioToSupabase(audioBuffer, fileName);
    console.log(`[FAL SPEECH SAVE] Uploaded to: ${supabaseAudioUrl}`);

    // Рассчитываем стоимость
    let cost = 5; // Минимум
    if (model.includes('turbo-v2.5')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 20));
    } else if (model.includes('multilingual-v2')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 40));
    }

    const savedGeneration = await saveSpeechGeneration({
      user_id: userId,
      text: text || '',
      audio_url: supabaseAudioUrl,
      voice_id: voice,
      voice_name: voice,
      model: model,
      parameters: {
        voice: voice,
        fal_request_id: falRequestId
      },
      cost: cost,
      public: visibility === 'public'
    });

    console.log(`[FAL SPEECH SAVE] Saved with ID: ${savedGeneration.id}`);

    return savedGeneration;

  } catch (error) {
    console.error(`[FAL SPEECH SAVE] Database error:`, error);
    throw error;
  }
}
