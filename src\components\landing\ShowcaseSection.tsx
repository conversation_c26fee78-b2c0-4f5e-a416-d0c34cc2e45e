
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface ShowcaseItemProps {
  title: string;
  prompt: string;
  imageSrc: string;
  index: number;
}

const ShowcaseItem = ({ title, prompt, imageSrc, index }: ShowcaseItemProps) => {
  return (
    <Link to="/dashboard" className="block group">
      <div
        className={cn(
          "neo-glass rounded-xl overflow-hidden animate-scale-in transition-transform duration-300 hover:-translate-y-1",
          index === 0 ? "animation-delay-200" : index === 1 ? "animation-delay-400" : "animation-delay-600"
        )}
      >
        <div className="relative aspect-[4/3] overflow-hidden">
          <img
            src={imageSrc}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
        </div>
        <div className="p-5">
          <h3 className="text-xl font-bold mb-2 text-foreground">{title}</h3>
          <p className="text-sm text-foreground/70 italic">"{prompt}"</p>
        </div>
      </div>
    </Link>
  );
};

const getShowcaseItems = (t: (key: string) => string) => [
  {
    title: t('showcase.item1Title'),
    prompt: t('showcase.item1Prompt'),
    imageSrc: "/lovable-uploads/60983631-be11-4304-b88b-b1d2124d636e.png"
  },
  {
    title: t('showcase.item2Title'),
    prompt: t('showcase.item2Prompt'),
    imageSrc: "/lovable-uploads/99e8a57b-82a6-4649-9cca-5bcf98794bed.png"
  },
  {
    title: t('showcase.item3Title'),
    prompt: t('showcase.item3Prompt'),
    imageSrc: "/lovable-uploads/33a9883b-f0c9-41a3-855e-17ebdce2350d.png"
  }
];

const ShowcaseSection = () => {
  const { t } = useTranslation();
  const showcaseItems = getShowcaseItems(t);

  return (
    <section className="py-20 px-6 relative">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-foreground">
            {t('showcase.sectionTitle')}
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            {t('showcase.sectionSubtitle')}
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          {showcaseItems.map((item, index) => (
            <ShowcaseItem 
              key={index}
              title={item.title}
              prompt={item.prompt}
              imageSrc={item.imageSrc}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ShowcaseSection;
