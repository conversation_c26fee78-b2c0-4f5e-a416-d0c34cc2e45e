// Vercel serverless function: /api/together/generate
import fetch from 'node-fetch';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY || "97742fce477abee08db8ca4510a4a955d5379e8e8a7c92fabb46d87d2a76cdcf";
  const togetherUrl = 'https://api.together.ai/v1/images/generations';

  try {
    const { model, num_outputs = 1, ...rest } = req.body || {};

    if (model === "black-forest-labs/FLUX.1-schnell-Free" && num_outputs > 1) {
      const promises = [];
      for (let i = 0; i < num_outputs; i++) {
        promises.push(
          fetch(togetherUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bear<PERSON> ${TOGETHER_API_KEY}`
            },
            // Always request 1 image per call for this model in the loop
            body: JSON.stringify({ model, num_outputs: 1, ...rest }) 
          }).then(async (response) => {
            const data = await response.json();
            if (!response.ok) {
              const error = new Error(`Together API request failed with status ${response.status}`);
              error.status = response.status;
              error.data = data; // Attach original error data
              throw error;
            }
            return data; // Expected structure: { data: [imageData] }
          })
        );
      }

      const results = await Promise.all(promises);
      // Consolidate image data from all successful responses
      const images = results.reduce((acc, currentResult) => {
        if (currentResult.data && currentResult.data.length > 0) {
          acc.push(...currentResult.data);
        }
        return acc;
      }, []);
      
      return res.status(200).json({ data: images });

    } else {
      // For other models or single output, use the original logic
      const togetherRes = await fetch(togetherUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TOGETHER_API_KEY}`
        },
        // Send original req.body which includes the intended num_outputs for these models
        body: JSON.stringify(req.body || {}) 
      });

      const data = await togetherRes.json();
      // Forward the status and data from Together API
      res.status(togetherRes.status).json(data);
    }
  } catch (error) {
    console.error("Error in /api/together/generate:", error.message, error.status ? `Status: ${error.status}` : '', error.data ? `Data: ${JSON.stringify(error.data)}` : '');
    if (error.status && error.data) {
        // If it's an error we constructed from a failed fetch (with status and data)
        res.status(error.status).json({ error: error.data.error || error.data, message: error.message });
    } else {
        // Generic internal server error
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
  }
}
