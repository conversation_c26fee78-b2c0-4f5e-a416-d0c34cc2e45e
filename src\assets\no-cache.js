// Этот файл используется для предотвращения кэширования в браузере
// Он будет добавлен в каждый запрос с временной меткой

export const clearCache = () => {
  // Добавляем случайный параметр к URL
  const links = document.querySelectorAll('link[rel="stylesheet"]');
  links.forEach(link => {
    if (link.href && !link.href.includes('?v=')) {
      link.href = `${link.href}?v=${Date.now()}`;
    }
  });

  // Добавляем случайный параметр к скриптам
  const scripts = document.querySelectorAll('script[src]');
  scripts.forEach(script => {
    if (script.src && !script.src.includes('?v=')) {
      script.src = `${script.src}?v=${Date.now()}`;
    }
  });

  // Добавляем мета-теги для предотвращения кэширования
  const meta = document.createElement('meta');
  meta.httpEquiv = 'Cache-Control';
  meta.content = 'no-store, no-cache, must-revalidate, proxy-revalidate';
  document.head.appendChild(meta);

  const pragma = document.createElement('meta');
  pragma.httpEquiv = 'Pragma';
  pragma.content = 'no-cache';
  document.head.appendChild(pragma);

  const expires = document.createElement('meta');
  expires.httpEquiv = 'Expires';
  expires.content = '0';
  document.head.appendChild(expires);

  console.log('Cache clearing activated at', new Date().toISOString());
};

// Экспортируем временную метку, которая будет уникальной при каждой сборке
export const buildTimestamp = Date.now(); 