// Fal API result endpoint for getting completed generation results
// GET /api/fal/result?requestId=<request_id>&model=<model_id>&userId=<user_id>&prompt=<prompt>&visibility=<visibility>

import fetch from "node-fetch";
import { saveVideoGeneration, saveSpeechGeneration, downloadFile, uploadVideoToSupabase, uploadAudioToSupabase, uploadThumbnailToSupabase, generateThumbnailFromVideo } from "../../utils/database.js";

export default async function handler(req, res) {
  // Устанавливаем CORS заголовки
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  console.log(`[FAL RESULT] Received ${req.method} request to ${req.url}`);
  console.log(`[FAL RESULT] Headers:`, req.headers);
  console.log(`[FAL RESULT] Body:`, req.body);
  console.log(`[FAL RESULT] Query:`, req.query);

  if (req.method === 'OPTIONS') {
    console.log(`[FAL RESULT] Handling OPTIONS request`);
    return res.status(200).end();
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    console.log(`[FAL RESULT] Method not allowed: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Поддерживаем параметры как в query (GET), так и в body (POST)
    let requestId, model, userId, prompt, visibility, text, voice;

    if (req.method === 'GET') {
      ({ requestId, model, userId, prompt, visibility, text, voice } = req.query);
    } else if (req.method === 'POST') {
      ({ requestId, model, userId, prompt, visibility, text, voice } = req.body);
    }

    console.log('[FAL RESULT] Getting result for:', { requestId, model, userId, method: req.method });

    if (!requestId || !model) {
      return res.status(400).json({ error: 'requestId and model are required' });
    }

    // Проверяем API ключ
    const falKey = process.env.FAL_KEY;
    if (!falKey) {
      console.error('[FAL RESULT] FAL_KEY not found in environment variables');
      return res.status(500).json({ error: 'FAL API key not configured' });
    }

    // Сначала получаем статус, чтобы получить response_url
    let statusUrl;
    if (model.includes('elevenlabs/tts')) {
      // Для речевых моделей ElevenLabs
      statusUrl = `https://queue.fal.run/${model}/requests/${requestId}/status`;
    } else {
      // Для видео моделей (по умолчанию Kling)
      statusUrl = `https://queue.fal.run/fal-ai/kling-video/requests/${requestId}/status`;
    }
    console.log('[FAL RESULT] Getting status first from:', statusUrl);

    const statusResponse = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      console.error('[FAL RESULT] Status API error:', errorText);
      return res.status(statusResponse.status).json({
        error: 'Fal API status error',
        details: errorText
      });
    }

    const statusData = await statusResponse.json();
    console.log('[FAL RESULT] Status data:', statusData);

    if (statusData.status !== 'COMPLETED') {
      return res.status(400).json({
        error: 'Generation not completed',
        status: statusData.status
      });
    }

    // Согласно документации FAL API, результат получается через специальный эндпоинт
    // Используем правильный URL для получения результата
    const resultUrl = `https://queue.fal.run/${model}/requests/${requestId}/result`;
    console.log('[FAL RESULT] Requesting result from:', resultUrl);

    // Отправляем GET запрос для получения результата
    const falResponse = await fetch(resultUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Key ${falKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!falResponse.ok) {
      const errorText = await falResponse.text();
      console.error('[FAL RESULT] API error:', errorText);
      return res.status(falResponse.status).json({
        error: 'Fal API result error',
        details: errorText
      });
    }

    const result = await falResponse.json();
    console.log('[FAL RESULT] Result response:', result);

    // Определяем тип контента и сохраняем в БД
    if (model.includes('elevenlabs/tts')) {
      // Обработка речевых моделей
      if (result.audio && result.audio.url && userId && text) {
        try {
          console.log('[FAL RESULT] Saving audio to database...');
          const savedGeneration = await saveSpeechToDatabase(result.audio.url, model, userId, text, voice || 'Rachel', visibility || 'public', requestId);
          console.log('[FAL RESULT] Audio saved to database successfully');

          // Возвращаем результат с URL из нашей БД
          return res.status(200).json({
            ...result,
            audio: {
              ...result.audio,
              url: savedGeneration.audio_url
            },
            saved: true,
            generationId: savedGeneration.id
          });
        } catch (saveError) {
          console.error('[FAL RESULT] Error saving audio to database:', saveError);
          // Не возвращаем ошибку, так как аудио все равно сгенерировано
        }
      }
    } else {
      // Обработка видео моделей
      if (result.video && result.video.url && userId && prompt) {
        try {
          console.log('[FAL RESULT] Saving video to database...');
          await saveVideoToDatabase(result.video.url, model, userId, prompt, visibility || 'public', requestId);
          console.log('[FAL RESULT] Video saved to database successfully');
        } catch (saveError) {
          console.error('[FAL RESULT] Error saving video to database:', saveError);
          // Не возвращаем ошибку, так как видео все равно сгенерировано
        }
      }
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('[FAL RESULT] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Сохраняет видео в базу данных
 */
async function saveVideoToDatabase(videoUrl, model, userId, prompt, visibility, falRequestId) {
  try {
    console.log(`[FAL RESULT] Saving video to database: ${videoUrl}`);

    // Скачиваем видео
    const videoBuffer = await downloadFile(videoUrl);
    console.log(`[FAL RESULT] Downloaded video, size: ${videoBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const videoId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${videoId}/${timestamp}-video.mp4`;

    // Загружаем видео в Supabase Storage
    const supabaseVideoUrl = await uploadVideoToSupabase(videoBuffer, fileName);
    console.log(`[FAL RESULT] Video uploaded to Storage: ${supabaseVideoUrl}`);

    // Генерируем thumbnail
    let thumbnailUrl = null;
    try {
      const thumbnailBuffer = await generateThumbnailFromVideo(videoBuffer);
      if (thumbnailBuffer) {
        const thumbnailFileName = `${videoId}/${timestamp}-thumbnail.jpg`;
        thumbnailUrl = await uploadThumbnailToSupabase(thumbnailBuffer, thumbnailFileName);
        console.log(`[FAL RESULT] Thumbnail uploaded: ${thumbnailUrl}`);
      }
    } catch (thumbnailError) {
      console.error(`[FAL RESULT] Error generating thumbnail:`, thumbnailError);
    }

    // Определяем стоимость
    let cost = 100; // По умолчанию
    if (model.includes('standard')) {
      cost = 75; // 5s = 75, 10s = 150
    } else if (model.includes('pro')) {
      cost = 120; // 5s = 120, 10s = 240
    } else if (model.includes('master')) {
      cost = 320; // 5s = 320, 10s = 640
    }

    // Сохраняем в БД
    await saveVideoGeneration({
      user_id: userId,
      prompt: prompt || '',
      video_url: supabaseVideoUrl,
      thumbnail_url: thumbnailUrl,
      model: model,
      fal_request_id: falRequestId, // Используем fal_request_id вместо replicate_prediction_id
      duration: 5, // По умолчанию
      cost: cost,
      visibility: visibility
    });

    console.log(`[FAL RESULT] Video generation saved to database with ID: ${videoId}`);

  } catch (error) {
    console.error(`[FAL RESULT] Error saving video to database:`, error);
    throw error;
  }
}

/**
 * Сохраняет аудио в базу данных
 */
async function saveSpeechToDatabase(audioUrl, model, userId, text, voice, visibility, falRequestId) {
  try {
    console.log(`[FAL RESULT] Saving audio to database: ${audioUrl}`);

    // Скачиваем аудио
    const audioBuffer = await downloadFile(audioUrl);
    console.log(`[FAL RESULT] Downloaded audio, size: ${audioBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const audioId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${audioId}/${timestamp}-audio.mp3`;

    // Загружаем аудио в Supabase Storage
    const supabaseAudioUrl = await uploadAudioToSupabase(audioBuffer, fileName);
    console.log(`[FAL RESULT] Audio uploaded to Storage: ${supabaseAudioUrl}`);

    // Рассчитываем стоимость
    let cost = 5; // Минимум
    if (model.includes('turbo-v2.5')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 20));
    } else if (model.includes('multilingual-v2')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 40));
    }

    // Сохраняем в БД
    const savedGeneration = await saveSpeechGeneration({
      user_id: userId,
      text: text || '',
      audio_url: supabaseAudioUrl,
      voice_id: voice,
      voice_name: voice,
      model: model,
      parameters: {
        voice: voice,
        fal_request_id: falRequestId
      },
      cost: cost,
      public: visibility === 'public'
    });

    console.log(`[FAL RESULT] Speech generation saved to database with ID: ${audioId}`);

    return savedGeneration;

  } catch (error) {
    console.error(`[FAL RESULT] Error saving audio to database:`, error);
    throw error;
  }
}
