import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { Languages } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Define language options
const languages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ru', name: 'Русский', nativeName: 'Русский' },
];

interface LanguageSwitcherProps extends React.HTMLAttributes<HTMLButtonElement> {}

const LanguageSwitcher = React.forwardRef<HTMLButtonElement, LanguageSwitcherProps>((props, ref) => {
  const { i18n, t } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  const currentLanguage = i18n.language;
  const currentLangDetails = languages.find(lang => currentLanguage.startsWith(lang.code)) || languages[0];

  return (
    <div className="relative">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              ref={ref}
              variant="ghost"
              className="relative flex items-center gap-2 text-black/70 dark:text-white/70 hover:text-black dark:hover:text-white"
              aria-label={t('language.switch')}
              {...props}
            >
              <Languages className="h-5 w-5" />
              <span className="hidden sm:inline">{currentLangDetails.name}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{t('language.switch')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="absolute inset-0" />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="bg-white dark:bg-zinc-900 border border-black/10 dark:border-white/10 mt-2 w-48"
          sideOffset={8}
        >
          <DropdownMenuLabel className="text-xs text-muted-foreground">
            {t('language.select')}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {languages.map((lang) => (
            <DropdownMenuItem
              key={lang.code}
              onClick={() => changeLanguage(lang.code)}
              className={cn(
                "flex items-center justify-between dark:text-white/90 text-foreground/90 hover:bg-foreground/5 dark:hover:bg-white/10",
                currentLanguage.startsWith(lang.code) && "font-bold bg-foreground/5 dark:bg-white/10"
              )}
            >
              <span className="dark:text-white text-foreground">{lang.name}</span>
              <span className="text-xs text-muted-foreground">{lang.nativeName}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
});

LanguageSwitcher.displayName = 'LanguageSwitcher';

export default LanguageSwitcher;
