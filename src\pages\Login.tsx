import React from 'react';
import { useTranslation } from 'react-i18next'; // Import useTranslation
import AuthForm from '@/components/auth/AuthForm';
import FloatingParticles from '@/components/ui/floating-particles';
import { useAuth } from '@/context/AuthContext';
import { Navigate } from 'react-router-dom';
import { Bot } from 'lucide-react';
import { Link } from 'react-router-dom';

const Login = () => {
  const { t } = useTranslation(); // Initialize useTranslation
  const { user } = useAuth();
  
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return (
    <div className="min-h-screen bg-white dark:bg-[#101010] flex items-center justify-center p-4 relative overflow-hidden">
      {/* Keep original FloatingParticles background */}
      {/* Animated background */}
      <FloatingParticles count={40} className="opacity-50" />
      
      <div className="relative z-10 max-w-md w-full">
        {/* Apply neo-glass styling similar to other cards */}
        <div className="neo-glass p-8 rounded-2xl border border-black/10 dark:border-white/20 shadow-lg max-w-md w-full bg-white/90 dark:bg-black/70 backdrop-blur-md">
          <Link to="/" className="block">
            <div className="flex items-center justify-center mb-6">
              {/* Adjust logo styling for light/dark theme */}
              <div className="h-12 w-12 rounded-full bg-black/10 dark:bg-white/20 flex items-center justify-center mr-3">
                <Bot size={26} className="text-black dark:text-white" />
              </div>
              <h1 className="text-3xl font-bold text-black dark:text-white">Uma AI</h1>
            </div>
          </Link>
          
          {/* Use translation keys */}
          <h2 className="text-xl font-semibold text-black dark:text-white text-center mb-2">{t('login.title')}</h2>
          <p className="text-black/70 dark:text-white/80 mb-6 text-center">{t('login.subtitle')}</p>
          
          <AuthForm />
        </div>
        
        <div className="mt-6 text-center text-black/60 dark:text-white/70 text-xs">
          {t('login.footer', { year: new Date().getFullYear() })}
        </div>
      </div>
    </div>
  );
};

export default Login;
