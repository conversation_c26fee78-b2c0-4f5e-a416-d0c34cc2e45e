// Vercel serverless function для сохранения видео в базу данных
// POST /api/video-save

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  console.log(`[VIDEO SAVE] ${req.method} request received`);

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      userId,
      videoUrl,
      isPrivate,
      prompt,
      duration,
      resolution,
      aspectRatio,
      fragmentDuration,
      transitionType,
      scenes
    } = req.body;

    if (!userId || !videoUrl || !prompt) {
      return res.status(400).json({ error: 'userId, videoUrl, and prompt are required' });
    }

    console.log('[VIDEO SAVE] Saving video to database:', { userId, videoUrl, isPrivate, prompt });

    // Сохраняем в таблицу video_generations
    const { data: videoData, error: videoError } = await supabase
      .from('video_generations')
      .insert({
        user_id: userId,
        prompt: prompt,
        video_url: videoUrl,
        model: 'create-mode-pixverse',
        duration: duration,
        resolution: resolution,
        aspect_ratio: aspectRatio,
        fragment_duration: fragmentDuration,
        transition_type: transitionType,
        is_private: isPrivate || false,
        scenes: scenes || [],
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (videoError) {
      console.error('[VIDEO SAVE] Error saving video generation:', videoError);
      return res.status(500).json({ error: 'Failed to save video generation', details: videoError.message });
    }

    // Если видео публичное, добавляем в дашборд
    if (!isPrivate) {
      const { error: dashboardError } = await supabase
        .from('dashboard_videos')
        .insert({
          user_id: userId,
          video_url: videoUrl,
          prompt: prompt,
          model: 'create-mode-pixverse',
          duration: duration,
          resolution: resolution,
          aspect_ratio: aspectRatio,
          likes: 0,
          created_at: new Date().toISOString()
        });

      if (dashboardError) {
        console.error('[VIDEO SAVE] Error saving to dashboard:', dashboardError);
        // Не возвращаем ошибку, так как основное сохранение прошло успешно
      }
    }

    console.log('[VIDEO SAVE] Success:', videoData.id);

    return res.status(200).json({
      success: true,
      videoId: videoData.id,
      message: 'Video saved successfully'
    });

  } catch (error) {
    console.error('[VIDEO SAVE] Error:', error);
    return res.status(500).json({ 
      error: 'Failed to save video',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
