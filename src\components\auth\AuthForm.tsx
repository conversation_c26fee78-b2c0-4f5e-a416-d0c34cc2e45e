import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { FiUser, FiMail, FiLock, FiEye, FiEyeOff } from 'react-icons/fi';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

type AuthMode = 'login' | 'register' | 'reset';

// Проверка email на валидность
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return !!email && emailRegex.test(email.trim());
};

// Проверка пароля на минимальную длину
const isValidPassword = (password: string): boolean => {
  return !!password && password.length >= 6;
};

export const AuthForm: React.FC = () => {
  const { t } = useTranslation();
  const { signIn, signUp, resetPassword, isLoading } = useAuth();

  const [mode, setMode] = useState<AuthMode>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    try {
      setLoading(true);

      // Валидация email
      if (!isValidEmail(email)) {
        toast.error(t('auth.invalidEmail'));
        setLoading(false);
        return;
      }

      // Валидация пароля
      if (mode !== 'reset' && !isValidPassword(password)) {
        toast.error(t('auth.passwordTooShort'));
        setLoading(false);
        return;
      }

      // Убираем возможные лишние пробелы
      const cleanEmail = email.trim();
      console.log(`Попытка ${mode === 'login' ? 'авторизации' : mode === 'register' ? 'регистрации' : 'сброса пароля'}: ${cleanEmail}`);

      if (mode === 'login') {
        await signIn(cleanEmail, password);
      } else if (mode === 'register') {
        await signUp(cleanEmail, password, name);
      } else if (mode === 'reset') {
        await resetPassword(cleanEmail);
        toast.success(t('auth.resetEmailSent'));
        setMode('login');
      }
    } catch (error) {
      console.error('Ошибка авторизации:', error);
      if (error instanceof Error) {
        // Проверяем специфические ошибки
        if (error.message.includes('Email') && error.message.includes('invalid')) {
          toast.error(t('auth.invalidEmail'));
        } else if (error.message.includes('password') || error.message.includes('Password')) {
          toast.error(t('auth.passwordTooShort'));
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error(t('auth.generalError'));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md p-6 shadow-md relative">
      <Tabs value={mode} onValueChange={(value) => setMode(value as AuthMode)} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="login">{t('auth.login')}</TabsTrigger>
          <TabsTrigger value="register">{t('auth.register')}</TabsTrigger>
        </TabsList>

        <form onSubmit={handleSubmit} className="space-y-4">
          {mode === 'reset' ? (
            <div className="text-center mb-4">
              <h2 className="text-xl font-semibold">{t('auth.resetPassword')}</h2>
              <p className="text-sm text-gray-500 mt-1">{t('auth.resetPasswordInstructions')}</p>
            </div>
          ) : null}

          {mode === 'register' && (
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center gap-2">
                <FiUser className="text-gray-400" />
                {t('auth.name')}
              </Label>
              <Input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={t('auth.namePlaceholder')}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <FiMail className="text-gray-400" />
              {t('auth.email')}
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={t('auth.emailPlaceholder')}
              required
            />
          </div>

          {mode !== 'reset' && (
            <div className="space-y-2 relative">
              <Label htmlFor="password" className="flex items-center gap-2">
                <FiLock className="text-gray-400" />
                {t('auth.password')}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={t('auth.passwordPlaceholder')}
                  required={mode === 'login' || mode === 'register'}
                  className="pr-10"
                  minLength={6}
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
              {mode === 'register' && (
                <p className="text-xs text-gray-500 mt-1">
                  {t('auth.passwordTooShort')}
                </p>
              )}
            </div>
          )}

          {mode === 'login' && (
            <div className="text-right">
              <button
                type="button"
                onClick={() => setMode('reset')}
                className="text-sm text-blue-600 hover:underline"
              >
                {t('auth.forgotPassword')}
              </button>
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            disabled={loading || isLoading}
          >
            {loading || isLoading
              ? t('common.loading')
              : mode === 'login'
              ? t('auth.login')
              : mode === 'register'
              ? t('auth.register')
              : t('auth.sendResetLink')}
          </Button>

          {mode === 'reset' && (
            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => setMode('login')}
                className="text-sm text-blue-600 hover:underline"
              >
                {t('auth.backToLogin')}
              </button>
            </div>
          )}
        </form>
      </Tabs>
    </Card>
  );
};

export default AuthForm;
