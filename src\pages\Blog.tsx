import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Calendar, User, ArrowRight, Clock, Plus } from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import SEOHead from '@/components/SEOHead';
import { useAuth } from '@/context/AuthContext';

// Типы для статей блога
interface BlogPost {
  id: string;
  title: string;
  titleEn: string;
  excerpt: string;
  excerptEn: string;
  content: string;
  contentEn: string;
  author: string;
  date: string;
  readTime: number;
  image?: string;
  tags: string[];
  published: boolean;
}

// Реальные статьи для блога
const mockPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Как создать максимально крутое видео с помощью Veo3',
    titleEn: 'How to Create Amazing Videos with Veo3',
    excerpt: 'Полное руководство по использованию Veo3 - самой продвинутой модели генерации видео от Google. Узнайте секреты создания профессиональных видео с аудио.',
    excerptEn: 'Complete guide to using Veo3 - Google\'s most advanced video generation model. Learn the secrets of creating professional videos with audio.',
    content: `
      <h2>Что такое Veo3 и почему это революция</h2>
      <p>Veo3 от Google - это прорыв в области генерации видео с помощью ИИ. Эта модель не просто создает видео, она генерирует полноценные 8-секундные ролики с профессиональным звуковым сопровождением, включая звуковые эффекты и даже голосовое сопровождение.</p>

      <h3>Ключевые преимущества Veo3:</h3>
      <ul>
        <li><strong>Высочайшее качество:</strong> Разрешение до 1280p с невероятной детализацией</li>
        <li><strong>Автоматическое аудио:</strong> Генерация звуков, музыки и голоса</li>
        <li><strong>Понимание контекста:</strong> Модель отлично понимает сложные сцены</li>
        <li><strong>Стабильность движения:</strong> Плавные и реалистичные анимации</li>
      </ul>

      <h2>Пошаговое руководство по созданию видео</h2>

      <h3>Шаг 1: Подготовка промпта</h3>
      <p>Качественный промпт - основа успешного видео. Для Veo3 используйте следующую структуру:</p>
      <ul>
        <li>Описание сцены (что происходит)</li>
        <li>Стиль съемки (крупный план, панорама, движение камеры)</li>
        <li>Настроение и атмосфера</li>
        <li>Детали освещения и времени суток</li>
      </ul>

      <p><strong>Пример хорошего промпта:</strong><br>
      "Cinematic shot of a majestic eagle soaring over snow-capped mountains at golden hour, slow motion, dramatic lighting, professional wildlife documentary style"</p>

      <h3>Шаг 2: Настройка параметров</h3>
      <p>В Uma AI для Veo3 доступны следующие настройки:</p>
      <ul>
        <li><strong>Улучшение промпта:</strong> Включите для автоматической оптимизации</li>
        <li><strong>Генерация аудио:</strong> Оставьте включенной для полного эффекта</li>
        <li><strong>Соотношение сторон:</strong> Только 16:9 (оптимально для большинства платформ)</li>
      </ul>

      <h3>Шаг 3: Секреты профессиональных результатов</h3>
      <p>Чтобы получить максимум от Veo3, следуйте этим советам:</p>

      <h4>Для экшн-сцен:</h4>
      <ul>
        <li>Используйте слова "dynamic", "fast-paced", "energetic"</li>
        <li>Указывайте направление движения</li>
        <li>Добавляйте детали о скорости действия</li>
      </ul>

      <h4>Для атмосферных видео:</h4>
      <ul>
        <li>Описывайте освещение детально</li>
        <li>Используйте эмоциональные прилагательные</li>
        <li>Указывайте погодные условия</li>
      </ul>

      <h4>Для технических кадров:</h4>
      <ul>
        <li>Указывайте тип камеры и объектива</li>
        <li>Описывайте движение камеры</li>
        <li>Добавляйте профессиональные термины</li>
      </ul>

      <h2>Примеры успешных промптов</h2>

      <h3>Природа и пейзажи:</h3>
      <p>"Aerial drone shot of a pristine lake surrounded by autumn forest, morning mist rising from water, golden sunlight filtering through trees, peaceful and serene atmosphere"</p>

      <h3>Городские сцены:</h3>
      <p>"Time-lapse of busy city intersection at night, neon lights reflecting on wet pavement, cars and people moving in fast motion, cyberpunk aesthetic"</p>

      <h3>Портреты и люди:</h3>
      <p>"Close-up portrait of an elderly craftsman working with wood, warm workshop lighting, focused expression, hands in motion, documentary style"</p>

      <h2>Оптимизация стоимости</h2>
      <p>Veo3 стоит 1400 кредитов с аудио и 1280 без аудио. Чтобы получить максимальную отдачу:</p>
      <ul>
        <li>Тщательно продумывайте промпт перед генерацией</li>
        <li>Используйте функцию улучшения промпта</li>
        <li>Отключайте аудио только если оно точно не нужно</li>
        <li>Экспериментируйте с разными стилями описания</li>
      </ul>

      <h2>Заключение</h2>
      <p>Veo3 открывает невероятные возможности для создания профессионального видеоконтента. Следуя этим рекомендациям, вы сможете создавать видео, которые будут выглядеть как работа профессиональной студии. Помните: качество результата напрямую зависит от качества промпта и понимания возможностей модели.</p>
    `,
    contentEn: `
      <h2>What is Veo3 and Why It's Revolutionary</h2>
      <p>Veo3 from Google is a breakthrough in AI video generation. This model doesn't just create videos - it generates complete 8-second clips with professional audio accompaniment, including sound effects and even voiceovers.</p>

      <h3>Key Advantages of Veo3:</h3>
      <ul>
        <li><strong>Highest Quality:</strong> Up to 1280p resolution with incredible detail</li>
        <li><strong>Automatic Audio:</strong> Generation of sounds, music, and voice</li>
        <li><strong>Context Understanding:</strong> Excellent comprehension of complex scenes</li>
        <li><strong>Motion Stability:</strong> Smooth and realistic animations</li>
      </ul>

      <h2>Step-by-Step Video Creation Guide</h2>

      <h3>Step 1: Prompt Preparation</h3>
      <p>A quality prompt is the foundation of successful video. For Veo3, use this structure:</p>
      <ul>
        <li>Scene description (what's happening)</li>
        <li>Shooting style (close-up, panorama, camera movement)</li>
        <li>Mood and atmosphere</li>
        <li>Lighting and time of day details</li>
      </ul>

      <p><strong>Example of a good prompt:</strong><br>
      "Cinematic shot of a majestic eagle soaring over snow-capped mountains at golden hour, slow motion, dramatic lighting, professional wildlife documentary style"</p>

      <h3>Step 2: Parameter Settings</h3>
      <p>In Uma AI for Veo3, the following settings are available:</p>
      <ul>
        <li><strong>Prompt Enhancement:</strong> Enable for automatic optimization</li>
        <li><strong>Audio Generation:</strong> Keep enabled for full effect</li>
        <li><strong>Aspect Ratio:</strong> Only 16:9 (optimal for most platforms)</li>
      </ul>

      <h3>Step 3: Professional Results Secrets</h3>
      <p>To get the most out of Veo3, follow these tips:</p>

      <h4>For Action Scenes:</h4>
      <ul>
        <li>Use words like "dynamic", "fast-paced", "energetic"</li>
        <li>Specify direction of movement</li>
        <li>Add details about action speed</li>
      </ul>

      <h4>For Atmospheric Videos:</h4>
      <ul>
        <li>Describe lighting in detail</li>
        <li>Use emotional adjectives</li>
        <li>Specify weather conditions</li>
      </ul>

      <h4>For Technical Shots:</h4>
      <ul>
        <li>Specify camera and lens type</li>
        <li>Describe camera movement</li>
        <li>Add professional terminology</li>
      </ul>

      <h2>Examples of Successful Prompts</h2>

      <h3>Nature and Landscapes:</h3>
      <p>"Aerial drone shot of a pristine lake surrounded by autumn forest, morning mist rising from water, golden sunlight filtering through trees, peaceful and serene atmosphere"</p>

      <h3>Urban Scenes:</h3>
      <p>"Time-lapse of busy city intersection at night, neon lights reflecting on wet pavement, cars and people moving in fast motion, cyberpunk aesthetic"</p>

      <h3>Portraits and People:</h3>
      <p>"Close-up portrait of an elderly craftsman working with wood, warm workshop lighting, focused expression, hands in motion, documentary style"</p>

      <h2>Cost Optimization</h2>
      <p>Veo3 costs 1400 credits with audio and 1280 without audio. To get maximum value:</p>
      <ul>
        <li>Carefully think through your prompt before generation</li>
        <li>Use the prompt enhancement feature</li>
        <li>Disable audio only if definitely not needed</li>
        <li>Experiment with different description styles</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Veo3 opens incredible possibilities for creating professional video content. Following these recommendations, you'll be able to create videos that look like professional studio work. Remember: result quality directly depends on prompt quality and understanding the model's capabilities.</p>
    `,
    author: 'Uma AI Team',
    date: '2024-12-15',
    readTime: 8,
    image: '/veo3-banner.webm',
    tags: ['Veo3', 'Видео', 'Руководство', 'Google'],
    published: true
  },
  {
    id: '2',
    title: 'Полное руководство по Uma AI: от регистрации до профессиональных результатов',
    titleEn: 'Complete Uma AI Guide: From Registration to Professional Results',
    excerpt: 'Исчерпывающее руководство по использованию платформы Uma AI. Узнайте, как максимально эффективно использовать все возможности для генерации изображений, видео и текста.',
    excerptEn: 'Comprehensive guide to using the Uma AI platform. Learn how to make the most of all capabilities for generating images, videos, and text.',
    content: `
      <h2>Добро пожаловать в Uma AI</h2>
      <p>Uma AI - это универсальная платформа для генерации контента с помощью искусственного интеллекта. Здесь вы можете создавать изображения, видео, текст и даже речь, используя самые современные модели ИИ.</p>

      <h2>Начало работы</h2>

      <h3>Регистрация и первые шаги</h3>
      <p>Для начала работы с Uma AI:</p>
      <ol>
        <li>Перейдите на главную страницу и нажмите "Начать бесплатно"</li>
        <li>Зарегистрируйтесь через email или социальные сети</li>
        <li>Подтвердите email (если регистрировались через почту)</li>
        <li>Получите стартовые кредиты для тестирования</li>
      </ol>

      <h3>Система кредитов</h3>
      <p>Uma AI использует систему кредитов для оплаты генераций:</p>
      <ul>
        <li><strong>Генерация изображений:</strong> от 5 до 25 кредитов</li>
        <li><strong>Генерация видео:</strong> от 75 до 1400 кредитов</li>
        <li><strong>Генерация речи:</strong> от 20 до 40 кредитов</li>
        <li><strong>Генерация текста:</strong> бесплатно</li>
      </ul>

      <h2>Генерация изображений</h2>

      <h3>Выбор модели</h3>
      <p>Uma AI предлагает несколько мощных моделей для создания изображений:</p>

      <h4>Ideogram 3 (10 кредитов)</h4>
      <ul>
        <li>Отлично работает с текстом на изображениях</li>
        <li>Поддерживает img2img</li>
        <li>Высокое качество и детализация</li>
        <li>Идеально для логотипов и дизайна</li>
      </ul>

      <h4>Flux Kontext Pro (10 кредитов)</h4>
      <ul>
        <li>Превосходное следование промптам</li>
        <li>Поддержка редактирования изображений</li>
        <li>Высокое качество фотореализма</li>
        <li>Отлично для портретов</li>
      </ul>

      <h4>Flux Kontext Max (25 кредитов)</h4>
      <ul>
        <li>Максимальная производительность</li>
        <li>Улучшенная генерация типографики</li>
        <li>Премиум качество</li>
        <li>Для самых требовательных задач</li>
      </ul>

      <h3>Советы по созданию промптов для изображений</h3>
      <p>Для получения лучших результатов:</p>
      <ul>
        <li>Будьте конкретны в описании</li>
        <li>Указывайте стиль (фотореализм, арт, аниме)</li>
        <li>Добавляйте детали освещения</li>
        <li>Используйте качественные прилагательные</li>
        <li>Экспериментируйте с негативными промптами</li>
      </ul>

      <h2>Генерация видео</h2>

      <h3>Доступные модели</h3>

      <h4>Veo3 (1280-1400 кредитов)</h4>
      <ul>
        <li>Лучшее качество на рынке</li>
        <li>8 секунд с аудио</li>
        <li>Только 16:9 формат</li>
        <li>Профессиональные результаты</li>
      </ul>

      <h4>Kling 2.1 (75-320 кредитов)</h4>
      <ul>
        <li>Разные версии: Standard, Pro, Master</li>
        <li>5-10 секунд видео</li>
        <li>Хорошее соотношение цена/качество</li>
        <li>Поддержка img2video</li>
      </ul>

      <h4>Ray 2 Flash (100 кредитов)</h4>
      <ul>
        <li>Быстрая генерация</li>
        <li>Поддержка зацикливания</li>
        <li>Хорошее качество</li>
        <li>Экономичный вариант</li>
      </ul>

      <h3>Продвинутые функции</h3>
      <p>Используйте дополнительные возможности:</p>
      <ul>
        <li><strong>Image to Video:</strong> Загрузите изображение для анимации</li>
        <li><strong>Начальный/конечный кадр:</strong> Контролируйте начало и конец</li>
        <li><strong>Зацикливание:</strong> Создавайте бесшовные петли</li>
        <li><strong>Эффекты:</strong> Используйте предустановленные эффекты</li>
      </ul>

      <h2>Генерация текста</h2>

      <h3>Возможности чат-бота</h3>
      <p>Текстовый ИИ Uma AI может:</p>
      <ul>
        <li>Отвечать на вопросы</li>
        <li>Писать статьи и контент</li>
        <li>Помогать с кодом</li>
        <li>Переводить тексты</li>
        <li>Анализировать изображения</li>
      </ul>

      <h3>Советы по работе с чатом</h3>
      <ul>
        <li>Задавайте конкретные вопросы</li>
        <li>Используйте контекст предыдущих сообщений</li>
        <li>Загружайте изображения для анализа</li>
        <li>Просите уточнения при необходимости</li>
      </ul>

      <h2>Генерация речи</h2>

      <h3>Доступные модели</h3>

      <h4>ElevenLabs Turbo v2.5 (20 кредитов/1000 символов)</h4>
      <ul>
        <li>Быстрая генерация</li>
        <li>Хорошее качество</li>
        <li>Экономичный вариант</li>
      </ul>

      <h4>ElevenLabs Multilingual v2 (40 кредитов/1000 символов)</h4>
      <ul>
        <li>Поддержка 20+ языков</li>
        <li>Высочайшее качество</li>
        <li>Естественное звучание</li>
      </ul>

      <h3>Настройки голоса</h3>
      <p>Настраивайте параметры для идеального звучания:</p>
      <ul>
        <li><strong>Stability:</strong> Стабильность голоса</li>
        <li><strong>Similarity Boost:</strong> Усиление схожести</li>
        <li><strong>Style:</strong> Стиль речи</li>
        <li><strong>Speed:</strong> Скорость речи</li>
      </ul>

      <h2>Приватность и безопасность</h2>

      <h3>Функция "Приватная генерация"</h3>
      <p>Используйте переключатель "Приватная" для:</p>
      <ul>
        <li>Скрытия генераций от общего дашборда</li>
        <li>Конфиденциальных проектов</li>
        <li>Личного контента</li>
      </ul>

      <h2>Оптимизация расходов</h2>

      <h3>Экономия кредитов</h3>
      <ul>
        <li>Тестируйте промпты на более дешевых моделях</li>
        <li>Используйте функцию улучшения промптов</li>
        <li>Покупайте кредиты большими пакетами</li>
        <li>Отключайте аудио в видео, если не нужно</li>
      </ul>

      <h3>Планы подписки</h3>
      <p>Выберите подходящий план:</p>
      <ul>
        <li><strong>Базовый (300₽):</strong> 600 кредитов</li>
        <li><strong>Стандартный (750₽):</strong> 1500 кредитов</li>
        <li><strong>Профессиональный (2000₽):</strong> 4000 кредитов</li>
      </ul>

      <h2>Заключение</h2>
      <p>Uma AI предоставляет мощные инструменты для создания контента любого типа. Экспериментируйте с разными моделями, изучайте возможности платформы и создавайте удивительный контент. Помните: качество результата зависит от качества промпта и понимания особенностей каждой модели.</p>
    `,
    contentEn: `
      <h2>Welcome to Uma AI</h2>
      <p>Uma AI is a universal platform for AI-powered content generation. Here you can create images, videos, text, and even speech using the most advanced AI models.</p>

      <h2>Getting Started</h2>

      <h3>Registration and First Steps</h3>
      <p>To start working with Uma AI:</p>
      <ol>
        <li>Go to the homepage and click "Get Started Free"</li>
        <li>Register via email or social networks</li>
        <li>Confirm email (if registered via email)</li>
        <li>Receive starter credits for testing</li>
      </ol>

      <h3>Credit System</h3>
      <p>Uma AI uses a credit system for generation payments:</p>
      <ul>
        <li><strong>Image Generation:</strong> 5 to 25 credits</li>
        <li><strong>Video Generation:</strong> 75 to 1400 credits</li>
        <li><strong>Speech Generation:</strong> 20 to 40 credits</li>
        <li><strong>Text Generation:</strong> free</li>
      </ul>

      <h2>Image Generation</h2>

      <h3>Model Selection</h3>
      <p>Uma AI offers several powerful models for image creation:</p>

      <h4>Ideogram 3 (10 credits)</h4>
      <ul>
        <li>Excellent with text in images</li>
        <li>Supports img2img</li>
        <li>High quality and detail</li>
        <li>Perfect for logos and design</li>
      </ul>

      <h4>Flux Kontext Pro (10 credits)</h4>
      <ul>
        <li>Excellent prompt following</li>
        <li>Image editing support</li>
        <li>High photorealism quality</li>
        <li>Great for portraits</li>
      </ul>

      <h4>Flux Kontext Max (25 credits)</h4>
      <ul>
        <li>Maximum performance</li>
        <li>Improved typography generation</li>
        <li>Premium quality</li>
        <li>For the most demanding tasks</li>
      </ul>

      <h3>Image Prompt Tips</h3>
      <p>For best results:</p>
      <ul>
        <li>Be specific in descriptions</li>
        <li>Specify style (photorealism, art, anime)</li>
        <li>Add lighting details</li>
        <li>Use quality adjectives</li>
        <li>Experiment with negative prompts</li>
      </ul>

      <h2>Video Generation</h2>

      <h3>Available Models</h3>

      <h4>Veo3 (1280-1400 credits)</h4>
      <ul>
        <li>Best quality on the market</li>
        <li>8 seconds with audio</li>
        <li>16:9 format only</li>
        <li>Professional results</li>
      </ul>

      <h4>Kling 2.1 (75-320 credits)</h4>
      <ul>
        <li>Different versions: Standard, Pro, Master</li>
        <li>5-10 second videos</li>
        <li>Good price/quality ratio</li>
        <li>img2video support</li>
      </ul>

      <h4>Ray 2 Flash (100 credits)</h4>
      <ul>
        <li>Fast generation</li>
        <li>Loop support</li>
        <li>Good quality</li>
        <li>Economical option</li>
      </ul>

      <h3>Advanced Features</h3>
      <p>Use additional capabilities:</p>
      <ul>
        <li><strong>Image to Video:</strong> Upload image for animation</li>
        <li><strong>Start/End Frame:</strong> Control beginning and end</li>
        <li><strong>Looping:</strong> Create seamless loops</li>
        <li><strong>Effects:</strong> Use preset effects</li>
      </ul>

      <h2>Text Generation</h2>

      <h3>Chatbot Capabilities</h3>
      <p>Uma AI's text AI can:</p>
      <ul>
        <li>Answer questions</li>
        <li>Write articles and content</li>
        <li>Help with code</li>
        <li>Translate texts</li>
        <li>Analyze images</li>
      </ul>

      <h3>Chat Tips</h3>
      <ul>
        <li>Ask specific questions</li>
        <li>Use context from previous messages</li>
        <li>Upload images for analysis</li>
        <li>Ask for clarification when needed</li>
      </ul>

      <h2>Speech Generation</h2>

      <h3>Available Models</h3>

      <h4>ElevenLabs Turbo v2.5 (20 credits/1000 chars)</h4>
      <ul>
        <li>Fast generation</li>
        <li>Good quality</li>
        <li>Economical option</li>
      </ul>

      <h4>ElevenLabs Multilingual v2 (40 credits/1000 chars)</h4>
      <ul>
        <li>20+ language support</li>
        <li>Highest quality</li>
        <li>Natural sound</li>
      </ul>

      <h3>Voice Settings</h3>
      <p>Adjust parameters for perfect sound:</p>
      <ul>
        <li><strong>Stability:</strong> Voice stability</li>
        <li><strong>Similarity Boost:</strong> Similarity enhancement</li>
        <li><strong>Style:</strong> Speech style</li>
        <li><strong>Speed:</strong> Speech speed</li>
      </ul>

      <h2>Privacy and Security</h2>

      <h3>"Private Generation" Feature</h3>
      <p>Use the "Private" toggle for:</p>
      <ul>
        <li>Hiding generations from public dashboard</li>
        <li>Confidential projects</li>
        <li>Personal content</li>
      </ul>

      <h2>Cost Optimization</h2>

      <h3>Credit Savings</h3>
      <ul>
        <li>Test prompts on cheaper models</li>
        <li>Use prompt enhancement feature</li>
        <li>Buy credits in larger packages</li>
        <li>Disable audio in videos if not needed</li>
      </ul>

      <h3>Subscription Plans</h3>
      <p>Choose the right plan:</p>
      <ul>
        <li><strong>Basic ($3):</strong> 600 credits</li>
        <li><strong>Standard ($7.50):</strong> 1500 credits</li>
        <li><strong>Professional ($20):</strong> 4000 credits</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Uma AI provides powerful tools for creating any type of content. Experiment with different models, explore platform capabilities, and create amazing content. Remember: result quality depends on prompt quality and understanding each model's features.</p>
    `,
    author: 'Uma AI Team',
    date: '2024-12-10',
    readTime: 12,
    image: '/umaicon.png',
    tags: ['Руководство', 'Uma AI', 'Обучение', 'Платформа'],
    published: true
  },
  {
    id: '3',
    title: 'Секреты создания идеальных изображений с Flux Kontext и Ideogram',
    titleEn: 'Secrets of Creating Perfect Images with Flux Kontext and Ideogram',
    excerpt: 'Подробное сравнение лучших моделей генерации изображений на Uma AI. Узнайте, когда использовать Flux Kontext Pro/Max и Ideogram 3 для достижения профессиональных результатов.',
    excerptEn: 'Detailed comparison of the best image generation models on Uma AI. Learn when to use Flux Kontext Pro/Max and Ideogram 3 to achieve professional results.',
    content: `
      <h2>Введение в мир профессиональной генерации изображений</h2>
      <p>В мире ИИ-генерации изображений качество модели определяет успех вашего проекта. Uma AI предлагает три топовые модели: Ideogram 3, Flux Kontext Pro и Flux Kontext Max. Каждая имеет свои сильные стороны и оптимальные сценарии использования.</p>

      <h2>Ideogram 3: Король текста и дизайна</h2>

      <h3>Когда выбирать Ideogram 3</h3>
      <ul>
        <li><strong>Логотипы и брендинг:</strong> Непревзойденная точность в создании текста</li>
        <li><strong>Постеры и флаеры:</strong> Идеальное сочетание текста и графики</li>
        <li><strong>Инфографика:</strong> Четкие диаграммы и схемы</li>
        <li><strong>Социальные сети:</strong> Посты с текстовыми элементами</li>
        <li><strong>Редактирование:</strong> Функция img2img для доработки</li>
      </ul>

      <h3>Секреты работы с Ideogram 3</h3>

      <h4>Для текстовых элементов:</h4>
      <p>Используйте кавычки для точного воспроизведения текста:</p>
      <p><strong>Пример:</strong> 'Modern logo design with text "CREATIVE STUDIO" in bold sans-serif font, minimalist style, black and white'</p>

      <h4>Для сложных композиций:</h4>
      <ul>
        <li>Описывайте расположение элементов</li>
        <li>Указывайте пропорции и размеры</li>
        <li>Добавляйте детали о цветовой схеме</li>
        <li>Используйте термины дизайна</li>
      </ul>

      <h4>Стили, которые лучше всего работают:</h4>
      <ul>
        <li>Minimalist design</li>
        <li>Corporate style</li>
        <li>Vintage poster</li>
        <li>Modern typography</li>
        <li>Clean layout</li>
      </ul>

      <h2>Flux Kontext Pro: Мастер фотореализма</h2>

      <h3>Оптимальные сценарии использования</h3>
      <ul>
        <li><strong>Портреты:</strong> Невероятно реалистичные лица</li>
        <li><strong>Продуктовая фотография:</strong> Коммерческие снимки</li>
        <li><strong>Архитектура:</strong> Фотореалистичные здания</li>
        <li><strong>Природа:</strong> Детализированные пейзажи</li>
        <li><strong>Редактирование фото:</strong> Профессиональная обработка</li>
      </ul>

      <h3>Продвинутые техники промптинга</h3>

      <h4>Для портретов:</h4>
      <p><strong>Структура промпта:</strong></p>
      <ul>
        <li>Описание человека (возраст, пол, этничность)</li>
        <li>Выражение лица и эмоции</li>
        <li>Освещение (soft lighting, dramatic shadows)</li>
        <li>Стиль съемки (headshot, full body, close-up)</li>
        <li>Качество (professional photography, high resolution)</li>
      </ul>

      <p><strong>Пример:</strong> "Professional headshot of a 30-year-old confident businesswoman, warm smile, soft natural lighting, shot with 85mm lens, shallow depth of field, corporate style"</p>

      <h4>Для продуктов:</h4>
      <ul>
        <li>Указывайте материалы и текстуры</li>
        <li>Описывайте фон и окружение</li>
        <li>Добавляйте детали освещения</li>
        <li>Используйте коммерческие термины</li>
      </ul>

      <h4>Магические слова для качества:</h4>
      <ul>
        <li>"shot with professional camera"</li>
        <li>"studio lighting"</li>
        <li>"high resolution"</li>
        <li>"sharp focus"</li>
        <li>"professional photography"</li>
        <li>"commercial quality"</li>
      </ul>

      <h2>Flux Kontext Max: Премиум без компромиссов</h2>

      <h3>Когда стоит доплачивать за Max</h3>
      <ul>
        <li><strong>Коммерческие проекты:</strong> Максимальное качество для клиентов</li>
        <li><strong>Печатная продукция:</strong> Высокое разрешение для полиграфии</li>
        <li><strong>Сложная типографика:</strong> Идеальный текст в изображениях</li>
        <li><strong>Детализированные сцены:</strong> Множество элементов</li>
        <li><strong>Художественные работы:</strong> Максимальная творческая свобода</li>
      </ul>

      <h3>Эксклюзивные возможности Max</h3>
      <ul>
        <li>Улучшенная генерация мелких деталей</li>
        <li>Более точное следование сложным промптам</li>
        <li>Лучшая работа с множественными объектами</li>
        <li>Превосходная типографика</li>
        <li>Максимальная стабильность результатов</li>
      </ul>

      <h2>Сравнительная таблица моделей</h2>

      <h3>По типам задач:</h3>

      <h4>Текст и логотипы:</h4>
      <ul>
        <li><strong>1 место:</strong> Ideogram 3 ⭐⭐⭐⭐⭐</li>
        <li><strong>2 место:</strong> Flux Kontext Max ⭐⭐⭐⭐</li>
        <li><strong>3 место:</strong> Flux Kontext Pro ⭐⭐⭐</li>
      </ul>

      <h4>Фотореализм:</h4>
      <ul>
        <li><strong>1 место:</strong> Flux Kontext Max ⭐⭐⭐⭐⭐</li>
        <li><strong>2 место:</strong> Flux Kontext Pro ⭐⭐⭐⭐⭐</li>
        <li><strong>3 место:</strong> Ideogram 3 ⭐⭐⭐⭐</li>
      </ul>

      <h4>Соотношение цена/качество:</h4>
      <ul>
        <li><strong>1 место:</strong> Ideogram 3 (10 кредитов)</li>
        <li><strong>2 место:</strong> Flux Kontext Pro (10 кредитов)</li>
        <li><strong>3 место:</strong> Flux Kontext Max (25 кредитов)</li>
      </ul>

      <h2>Практические примеры промптов</h2>

      <h3>Для бизнеса и маркетинга</h3>

      <h4>Корпоративный портрет (Flux Kontext Pro):</h4>
      <p>"Professional business portrait of a confident CEO in modern office, wearing navy blue suit, natural lighting from large windows, shot with 85mm lens, corporate photography style"</p>

      <h4>Логотип стартапа (Ideogram 3):</h4>
      <p>'Modern tech startup logo with text "INNOVATE" in clean sans-serif font, geometric elements, blue and white color scheme, minimalist design'</p>

      <h4>Продуктовое фото (Flux Kontext Max):</h4>
      <p>"Luxury watch product photography, premium steel bracelet, white background, professional studio lighting, macro lens detail, commercial quality, high-end jewelry style"</p>

      <h3>Для творческих проектов</h3>

      <h4>Художественный портрет (Flux Kontext Max):</h4>
      <p>"Artistic portrait of a young artist in her studio, paint-stained apron, dramatic side lighting, surrounded by colorful paintings, creative atmosphere, fine art photography"</p>

      <h4>Винтажный постер (Ideogram 3):</h4>
      <p>'Vintage travel poster with text "VISIT PARIS" in art deco style, Eiffel Tower silhouette, warm color palette, retro typography, 1930s design aesthetic'</p>

      <h2>Оптимизация рабочего процесса</h2>

      <h3>Стратегия выбора модели</h3>
      <ol>
        <li><strong>Определите приоритет:</strong> Текст → Ideogram 3, Фото → Flux Kontext</li>
        <li><strong>Оцените бюджет:</strong> Тестируйте на Pro, финализируйте на Max</li>
        <li><strong>Учитывайте назначение:</strong> Соцсети → Pro, Печать → Max</li>
        <li><strong>Экспериментируйте:</strong> Одинаковый промпт на разных моделях</li>
      </ol>

      <h3>Экономия кредитов</h3>
      <ul>
        <li>Начинайте с Ideogram 3 для тестирования концепции</li>
        <li>Используйте Flux Kontext Pro для большинства задач</li>
        <li>Переходите на Max только для финальных версий</li>
        <li>Сохраняйте успешные промпты для повторного использования</li>
      </ul>

      <h2>Частые ошибки и как их избежать</h2>

      <h3>Ошибки в промптах</h3>
      <ul>
        <li><strong>Слишком общие описания:</strong> Будьте конкретны</li>
        <li><strong>Противоречивые стили:</strong> Выбирайте один стиль</li>
        <li><strong>Игнорирование освещения:</strong> Всегда описывайте свет</li>
        <li><strong>Забытые технические детали:</strong> Указывайте качество</li>
      </ul>

      <h3>Ошибки в выборе модели</h3>
      <ul>
        <li>Использование Flux для простого текста</li>
        <li>Выбор Ideogram для сложного фотореализма</li>
        <li>Переплата за Max без необходимости</li>
        <li>Игнорирование функции img2img</li>
      </ul>

      <h2>Заключение</h2>
      <p>Мастерство в генерации изображений приходит с опытом и пониманием сильных сторон каждой модели. Ideogram 3 - ваш выбор для текста и дизайна, Flux Kontext Pro - для качественного фотореализма, а Flux Kontext Max - для проектов без компромиссов. Экспериментируйте, изучайте результаты и создавайте удивительные изображения!</p>
    `,
    contentEn: `
      <h2>Introduction to Professional Image Generation</h2>
      <p>In the world of AI image generation, model quality determines your project's success. Uma AI offers three top models: Ideogram 3, Flux Kontext Pro, and Flux Kontext Max. Each has its strengths and optimal use cases.</p>

      <h2>Ideogram 3: King of Text and Design</h2>

      <h3>When to Choose Ideogram 3</h3>
      <ul>
        <li><strong>Logos and Branding:</strong> Unmatched text accuracy</li>
        <li><strong>Posters and Flyers:</strong> Perfect text-graphics combination</li>
        <li><strong>Infographics:</strong> Clear diagrams and charts</li>
        <li><strong>Social Media:</strong> Posts with text elements</li>
        <li><strong>Editing:</strong> img2img function for refinement</li>
      </ul>

      <h3>Secrets of Working with Ideogram 3</h3>

      <h4>For Text Elements:</h4>
      <p>Use quotes for precise text reproduction:</p>
      <p><strong>Example:</strong> 'Modern logo design with text "CREATIVE STUDIO" in bold sans-serif font, minimalist style, black and white'</p>

      <h4>For Complex Compositions:</h4>
      <ul>
        <li>Describe element placement</li>
        <li>Specify proportions and sizes</li>
        <li>Add color scheme details</li>
        <li>Use design terminology</li>
      </ul>

      <h4>Best Working Styles:</h4>
      <ul>
        <li>Minimalist design</li>
        <li>Corporate style</li>
        <li>Vintage poster</li>
        <li>Modern typography</li>
        <li>Clean layout</li>
      </ul>

      <h2>Flux Kontext Pro: Master of Photorealism</h2>

      <h3>Optimal Use Cases</h3>
      <ul>
        <li><strong>Portraits:</strong> Incredibly realistic faces</li>
        <li><strong>Product Photography:</strong> Commercial shots</li>
        <li><strong>Architecture:</strong> Photorealistic buildings</li>
        <li><strong>Nature:</strong> Detailed landscapes</li>
        <li><strong>Photo Editing:</strong> Professional processing</li>
      </ul>

      <h3>Advanced Prompting Techniques</h3>

      <h4>For Portraits:</h4>
      <p><strong>Prompt Structure:</strong></p>
      <ul>
        <li>Person description (age, gender, ethnicity)</li>
        <li>Facial expression and emotions</li>
        <li>Lighting (soft lighting, dramatic shadows)</li>
        <li>Shooting style (headshot, full body, close-up)</li>
        <li>Quality (professional photography, high resolution)</li>
      </ul>

      <p><strong>Example:</strong> "Professional headshot of a 30-year-old confident businesswoman, warm smile, soft natural lighting, shot with 85mm lens, shallow depth of field, corporate style"</p>

      <h4>For Products:</h4>
      <ul>
        <li>Specify materials and textures</li>
        <li>Describe background and environment</li>
        <li>Add lighting details</li>
        <li>Use commercial terms</li>
      </ul>

      <h4>Magic Words for Quality:</h4>
      <ul>
        <li>"shot with professional camera"</li>
        <li>"studio lighting"</li>
        <li>"high resolution"</li>
        <li>"sharp focus"</li>
        <li>"professional photography"</li>
        <li>"commercial quality"</li>
      </ul>

      <h2>Flux Kontext Max: Premium Without Compromise</h2>

      <h3>When to Pay Extra for Max</h3>
      <ul>
        <li><strong>Commercial Projects:</strong> Maximum quality for clients</li>
        <li><strong>Print Products:</strong> High resolution for printing</li>
        <li><strong>Complex Typography:</strong> Perfect text in images</li>
        <li><strong>Detailed Scenes:</strong> Multiple elements</li>
        <li><strong>Artistic Works:</strong> Maximum creative freedom</li>
      </ul>

      <h3>Exclusive Max Capabilities</h3>
      <ul>
        <li>Enhanced fine detail generation</li>
        <li>More accurate complex prompt following</li>
        <li>Better handling of multiple objects</li>
        <li>Superior typography</li>
        <li>Maximum result stability</li>
      </ul>

      <h2>Model Comparison Table</h2>

      <h3>By Task Type:</h3>

      <h4>Text and Logos:</h4>
      <ul>
        <li><strong>1st Place:</strong> Ideogram 3 ⭐⭐⭐⭐⭐</li>
        <li><strong>2nd Place:</strong> Flux Kontext Max ⭐⭐⭐⭐</li>
        <li><strong>3rd Place:</strong> Flux Kontext Pro ⭐⭐⭐</li>
      </ul>

      <h4>Photorealism:</h4>
      <ul>
        <li><strong>1st Place:</strong> Flux Kontext Max ⭐⭐⭐⭐⭐</li>
        <li><strong>2nd Place:</strong> Flux Kontext Pro ⭐⭐⭐⭐⭐</li>
        <li><strong>3rd Place:</strong> Ideogram 3 ⭐⭐⭐⭐</li>
      </ul>

      <h4>Price/Quality Ratio:</h4>
      <ul>
        <li><strong>1st Place:</strong> Ideogram 3 (10 credits)</li>
        <li><strong>2nd Place:</strong> Flux Kontext Pro (10 credits)</li>
        <li><strong>3rd Place:</strong> Flux Kontext Max (25 credits)</li>
      </ul>

      <h2>Practical Prompt Examples</h2>

      <h3>For Business and Marketing</h3>

      <h4>Corporate Portrait (Flux Kontext Pro):</h4>
      <p>"Professional business portrait of a confident CEO in modern office, wearing navy blue suit, natural lighting from large windows, shot with 85mm lens, corporate photography style"</p>

      <h4>Startup Logo (Ideogram 3):</h4>
      <p>'Modern tech startup logo with text "INNOVATE" in clean sans-serif font, geometric elements, blue and white color scheme, minimalist design'</p>

      <h4>Product Photo (Flux Kontext Max):</h4>
      <p>"Luxury watch product photography, premium steel bracelet, white background, professional studio lighting, macro lens detail, commercial quality, high-end jewelry style"</p>

      <h3>For Creative Projects</h3>

      <h4>Artistic Portrait (Flux Kontext Max):</h4>
      <p>"Artistic portrait of a young artist in her studio, paint-stained apron, dramatic side lighting, surrounded by colorful paintings, creative atmosphere, fine art photography"</p>

      <h4>Vintage Poster (Ideogram 3):</h4>
      <p>'Vintage travel poster with text "VISIT PARIS" in art deco style, Eiffel Tower silhouette, warm color palette, retro typography, 1930s design aesthetic'</p>

      <h2>Workflow Optimization</h2>

      <h3>Model Selection Strategy</h3>
      <ol>
        <li><strong>Determine Priority:</strong> Text → Ideogram 3, Photo → Flux Kontext</li>
        <li><strong>Assess Budget:</strong> Test on Pro, finalize on Max</li>
        <li><strong>Consider Purpose:</strong> Social Media → Pro, Print → Max</li>
        <li><strong>Experiment:</strong> Same prompt on different models</li>
      </ol>

      <h3>Credit Savings</h3>
      <ul>
        <li>Start with Ideogram 3 for concept testing</li>
        <li>Use Flux Kontext Pro for most tasks</li>
        <li>Switch to Max only for final versions</li>
        <li>Save successful prompts for reuse</li>
      </ul>

      <h2>Common Mistakes and How to Avoid Them</h2>

      <h3>Prompt Mistakes</h3>
      <ul>
        <li><strong>Too General Descriptions:</strong> Be specific</li>
        <li><strong>Contradictory Styles:</strong> Choose one style</li>
        <li><strong>Ignoring Lighting:</strong> Always describe light</li>
        <li><strong>Forgotten Technical Details:</strong> Specify quality</li>
      </ul>

      <h3>Model Selection Mistakes</h3>
      <ul>
        <li>Using Flux for simple text</li>
        <li>Choosing Ideogram for complex photorealism</li>
        <li>Overpaying for Max unnecessarily</li>
        <li>Ignoring img2img function</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Mastery in image generation comes with experience and understanding each model's strengths. Ideogram 3 is your choice for text and design, Flux Kontext Pro for quality photorealism, and Flux Kontext Max for uncompromising projects. Experiment, study results, and create amazing images!</p>
    `,
    author: 'Uma AI Team',
    date: '2024-12-05',
    readTime: 10,
    image: '/FluxKontextMaxLight.webp',
    tags: ['Flux Kontext', 'Ideogram', 'Изображения', 'Сравнение'],
    published: true
  }
];

const Blog = () => {
  const { i18n, t } = useTranslation();
  const { user } = useAuth();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  // Проверка прав администратора
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  const isAdmin = Boolean(user?.email && adminEmails.includes(user.email));

  useEffect(() => {
    // Загрузка данных из localStorage (в реальном проекте - из API)
    setTimeout(() => {
      try {
        const savedPosts = localStorage.getItem('blog_posts');
        if (savedPosts) {
          const allPosts = JSON.parse(savedPosts);
          setPosts(allPosts.filter((post: BlogPost) => post.published));
        } else {
          // Если нет сохраненных постов, инициализируем моковыми данными и сохраняем
          const publishedPosts = mockPosts.filter(post => post.published);
          localStorage.setItem('blog_posts', JSON.stringify(mockPosts));
          setPosts(publishedPosts);
        }
      } catch (error) {
        console.error('Error loading posts:', error);
        const publishedPosts = mockPosts.filter(post => post.published);
        setPosts(publishedPosts);
      }
      setLoading(false);
    }, 500);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return i18n.language === 'ru' 
      ? date.toLocaleDateString('ru-RU', { year: 'numeric', month: 'long', day: 'numeric' })
      : date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <>
      <SEOHead 
        title={i18n.language === 'ru' ? 'Блог - Uma AI' : 'Blog - Uma AI'}
        description={i18n.language === 'ru' 
          ? 'Читайте последние новости и статьи о генерации контента с помощью ИИ, советы по использованию и обновления платформы Uma AI.'
          : 'Read the latest news and articles about AI content generation, usage tips, and Uma AI platform updates.'
        }
      />
      
      <div className="min-h-screen bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-black dark:via-gray-900/50 dark:to-gray-800/30">
        <Header />
        
        {/* Hero Section с градиентом */}
        <section className="pt-24 pb-16 px-6 relative overflow-hidden">
          {/* Мягкий градиентный фон - убираем отдельные слои */}
          
          <div className="max-w-4xl mx-auto text-center relative z-10">
            <div className="flex justify-between items-start mb-6">
              <div className="flex-1">
                <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gradient">
                  {i18n.language === 'ru' ? 'Блог Uma AI' : 'Uma AI Blog'}
                </h1>
              </div>
              {isAdmin && (
                <Link to="/blog-admin" className="mt-4">
                  <Button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="w-4 h-4" />
                    {i18n.language === 'ru' ? 'Создать статью' : 'Create Article'}
                  </Button>
                </Link>
              )}
            </div>
            <p className="text-xl text-foreground/70 mb-8 max-w-2xl mx-auto">
              {i18n.language === 'ru'
                ? 'Последние новости, советы и инсайты о генерации контента с помощью искусственного интеллекта'
                : 'Latest news, tips, and insights about AI-powered content generation'
              }
            </p>
          </div>
        </section>

        {/* Articles Section */}
        <section className="pb-20 px-6">
          <div className="max-w-6xl mx-auto">
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="glass-panel rounded-2xl p-6 animate-pulse">
                    <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 rounded-xl mb-4" />
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {posts.map((post) => (
                  <Link key={post.id} to={`/blog/${post.id}`} className="block">
                    <article className="glass-panel rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-300 group">
                    {post.image && (
                      <div className="relative overflow-hidden">
                        {post.image.endsWith('.webm') || post.image.endsWith('.mp4') ? (
                          <video
                            src={post.image}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                            autoPlay
                            loop
                            muted
                            playsInline
                            preload="metadata"
                          />
                        ) : (
                          <img
                            src={post.image}
                            alt={i18n.language === 'ru' ? post.title : post.titleEn}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>
                    )}
                    
                    <div className="p-6">
                      <div className="flex items-center gap-4 text-sm text-foreground/60 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(post.date)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {post.readTime} {i18n.language === 'ru' ? 'мин' : 'min'}
                        </div>
                      </div>
                      
                      <h2 className="text-xl font-semibold mb-3 text-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {i18n.language === 'ru' ? post.title : post.titleEn}
                      </h2>
                      
                      <p className="text-foreground/70 mb-4 line-clamp-3">
                        {i18n.language === 'ru' ? post.excerpt : post.excerptEn}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-sm text-foreground/60">
                          <User className="w-4 h-4" />
                          {post.author}
                        </div>

                        <div className="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300">
                          {i18n.language === 'ru' ? 'Читать' : 'Read'}
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                      
                      {post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-black/10 dark:border-white/10">
                          {post.tags.map((tag, index) => (
                            <span 
                              key={index}
                              className="px-2 py-1 text-xs bg-black/5 dark:bg-white/5 rounded-full text-foreground/60"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    </article>
                  </Link>
                ))}
              </div>
            )}
            
            {!loading && posts.length === 0 && (
              <div className="text-center py-16">
                <h3 className="text-2xl font-semibold mb-4 text-foreground/70">
                  {i18n.language === 'ru' ? 'Пока нет статей' : 'No articles yet'}
                </h3>
                <p className="text-foreground/50">
                  {i18n.language === 'ru' 
                    ? 'Скоро здесь появятся интересные статьи о генерации контента с ИИ'
                    : 'Interesting articles about AI content generation will appear here soon'
                  }
                </p>
              </div>
            )}
          </div>
        </section>
        
        <Footer />
      </div>
    </>
  );
};

export default Blog;
