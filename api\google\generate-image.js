// Импортируем необходимые модули
import axios from 'axios';

// Константы для Google AI API
const GOOGLE_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models';
const GOOGLE_API_KEY = process.env.GOOGLE_AI_API_KEY;

export default async function handler(req, res) {
  // Разрешаем только POST-запросы для генерации изображений
  if (req.method === 'POST') {
    try {
      const { 
        prompt, 
        negative_prompt,
        size = '1024x1024',
        model = 'imagegeneration@005',
        userId 
      } = req.body;

      if (!prompt) {
        return res.status(400).json({ error: 'Необходимо указать prompt для генерации изображения' });
      }

      if (!GOOGLE_API_KEY) {
        return res.status(500).json({ error: 'API ключ Google AI не настроен на сервере' });
      }

      // Проверка и списание кредитов - здесь можно добавить позже при необходимости

      // Формируем запрос к Google AI API
      const apiUrl = `${GOOGLE_API_URL}/${model}:generateContent?key=${GOOGLE_API_KEY}`;
      const response = await axios.post(apiUrl, {
        contents: [
          {
            role: "user",
            parts: [
              {
                text: prompt + (negative_prompt ? ` Do not include: ${negative_prompt}` : "")
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.4,
          topP: 1,
          topK: 32,
          maxOutputTokens: 2048,
          stopSequences: [],
          ...(size && { size })
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      });

      return res.status(200).json(response.data);
    } catch (error) {
      console.error('Ошибка при генерации изображения:', error.response?.data || error.message);
      return res.status(error.response?.status || 500).json({
        error: 'Ошибка при генерации изображения',
        details: error.response?.data || error.message
      });
    }
  }
  // Если метод запроса не поддерживается
  else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 