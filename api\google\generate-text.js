// Импортируем необходимые модули
import axios from 'axios';

// Константы для Google AI API
const GOOGLE_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models';
const GOOGLE_API_KEY = process.env.GOOGLE_AI_API_KEY;

const rateLimits = {
  'gemini-2.5-flash-preview-05-20': { rpm: 10 },
  'gemini-2.5-pro-preview-05-06': { rpm: 5 },
  'gemini-2.5-pro-exp-03-25': { rpm: 5 },
  'gemini-2.0-flash-preview-image-generation': { rpm: 10 },
  'models/gemini-2.0-flash-preview-image-generation': { rpm: 10 },
  'models/gemini-2.5-flash-preview-05-20': { rpm: 10 },
};
const requestTimestamps = new Map();

export default async function handler(req, res) {
  // Разрешаем только POST-запросы для генерации текста
  if (req.method === 'POST') {
    try {
      const {
        prompt,
        model = 'gemini-2.5-pro-exp-03-25',
        temperature = 0.7,
        maxOutputTokens = 8192,
        userId
      } = req.body;

      // Rate limiting
      const now = Date.now();
      const limit = rateLimits[model]?.rpm || 5;
      if (!requestTimestamps.has(model)) requestTimestamps.set(model, []);
      const timestamps = requestTimestamps.get(model).filter(ts => now - ts < 60000);
      if (timestamps.length >= limit) {
        return res.status(429).json({ error: `Rate limit exceeded for model ${model} (${limit} requests per minute)` });
      }
      timestamps.push(now);
      requestTimestamps.set(model, timestamps);

      if (!prompt) {
        return res.status(400).json({ error: 'Необходимо указать prompt для генерации текста' });
      }

      if (!GOOGLE_API_KEY) {
        return res.status(500).json({ error: 'API ключ Google AI не настроен на сервере' });
      }

      // Проверка и списание кредитов - здесь можно добавить позже при необходимости

      // Формируем запрос к Google AI API
      const apiUrl = `${GOOGLE_API_URL}/${model}:generateContent?key=${GOOGLE_API_KEY}`;
      const response = await axios.post(apiUrl, {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature,
          maxOutputTokens,
          topP: 1,
          topK: 32
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      });

      return res.status(200).json(response.data);
    } catch (error) {
      console.error('Ошибка при генерации текста:', error.response?.data || error.message);
      return res.status(error.response?.status || 500).json({
        error: 'Ошибка при генерации текста',
        details: error.response?.data || error.message
      });
    }
  }
  // Если метод запроса не поддерживается
  else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 