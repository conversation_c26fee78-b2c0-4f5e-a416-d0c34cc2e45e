{"openapi":"3.0.4","info":{"title":"Queue OpenAPI for fal-ai/kling-video/v2.1/pro/image-to-video","version":"1.0.0","description":"","x-fal-metadata":{"endpointId":"fal-ai/kling-video/v2.1/pro/image-to-video","category":"image-to-video","thumbnailUrl":"https://storage.googleapis.com/fal_cdn/fal/Upscale-1.jpeg","playgroundUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/pro/image-to-video","documentationUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/pro/image-to-video/api"}},"components":{"securitySchemes":{"apiKeyAuth":{"type":"apiKey","in":"header","name":"Authorization","description":"Fal Key"}},"schemas":{"QueueStatus":{"type":"object","properties":{"status":{"type":"string","enum":["IN_QUEUE","IN_PROGRESS","COMPLETED"]},"request_id":{"type":"string","description":"The request id."},"response_url":{"type":"string","description":"The response url."},"status_url":{"type":"string","description":"The status url."},"cancel_url":{"type":"string","description":"The cancel url."},"logs":{"type":"object","description":"The logs.","additionalProperties":true},"metrics":{"type":"object","description":"The metrics.","additionalProperties":true},"queue_position":{"type":"integer","description":"The queue position."}},"required":["status","request_id"]},"KlingVideoV21ProImageToVideoInput":{"title":"ImageToVideoV21ProRequest","type":"object","properties":{"prompt":{"examples":["Warm, incandescent streetlights paint the rain-slicked cobblestones in pools of amber light as a couple walks hand-in-hand, their silhouettes stark against the blurry backdrop of a city shrouded in a gentle downpour; the camera lingers on the subtle textures of their rain-soaked coats and the glistening reflections dancing on the wet pavement, creating a sense of intimate vulnerability and shared quietude."],"maxLength":2500,"type":"string","title":"Prompt"},"duration":{"enum":["5","10"],"title":"Duration","type":"string","description":"The duration of the generated video in seconds","default":"5"},"aspect_ratio":{"enum":["16:9","9:16","1:1"],"title":"Aspect Ratio","type":"string","description":"The aspect ratio of the generated video frame","default":"16:9"},"cfg_scale":{"minimum":0,"maximum":1,"type":"number","title":"Cfg Scale","description":"\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ","default":0.5},"negative_prompt":{"maxLength":2500,"type":"string","title":"Negative Prompt","default":"blur, distort, and low quality"},"image_url":{"format":"uri","description":"URL of the image to be used for the video","type":"string","examples":["https://v3.fal.media/files/lion/_I_io6Gtk83c72d-afXf8_image.webp"],"maxLength":2083,"minLength":1,"title":"Image Url"}},"x-fal-order-properties":["prompt","image_url","duration","aspect_ratio","negative_prompt","cfg_scale"],"required":["prompt","image_url"]},"KlingVideoV21ProImageToVideoOutput":{"title":"ImageToVideoV21ProOutput","type":"object","properties":{"video":{"examples":[{"url":"https://v3.fal.media/files/rabbit/Y5I8-7u3e7ogVSvPin1TS_output.mp4"}],"title":"Video","description":"The generated video","allOf":[{"$ref":"#/components/schemas/File"}]}},"x-fal-order-properties":["video"],"required":["video"]},"File":{"title":"File","type":"object","properties":{"file_size":{"examples":[4404019],"title":"File Size","type":"integer","description":"The size of the file in bytes."},"file_name":{"examples":["z9RV14K95DvU.png"],"title":"File Name","type":"string","description":"The name of the file. It will be auto-generated if not provided."},"content_type":{"examples":["image/png"],"title":"Content Type","type":"string","description":"The mime type of the file."},"url":{"title":"Url","type":"string","description":"The URL where the file can be downloaded from."},"file_data":{"format":"binary","title":"File Data","type":"string","description":"File data"}},"x-fal-order-properties":["url","content_type","file_name","file_size","file_data"],"required":["url"]}}},"paths":{"/fal-ai/kling-video/v2.1/pro/image-to-video/requests/{request_id}/status":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}},{"name":"logs","in":"query","required":false,"schema":{"type":"number","description":"Whether to include logs (`1`) in the response or not (`0`)."}}],"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/pro/image-to-video/requests/{request_id}/cancel":{"put":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"The request was cancelled.","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean","description":"Whether the request was cancelled successfully."}}}}}}}}},"/fal-ai/kling-video/v2.1/pro/image-to-video":{"post":{"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21ProImageToVideoInput"}}}},"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/pro/image-to-video/requests/{request_id}":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"Result of the request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21ProImageToVideoOutput"}}}}}}}},"servers":[{"url":"https://queue.fal.run"}],"security":[{"apiKeyAuth":[]}]}

{"openapi":"3.0.4","info":{"title":"Queue OpenAPI for fal-ai/kling-video/v2.1/master/image-to-video","version":"1.0.0","description":"","x-fal-metadata":{"endpointId":"fal-ai/kling-video/v2.1/master/image-to-video","category":"image-to-video","thumbnailUrl":"https://storage.googleapis.com/fal_cdn/fal/Upscale-5.jpeg","playgroundUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/master/image-to-video","documentationUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/master/image-to-video/api"}},"components":{"securitySchemes":{"apiKeyAuth":{"type":"apiKey","in":"header","name":"Authorization","description":"Fal Key"}},"schemas":{"QueueStatus":{"type":"object","properties":{"status":{"type":"string","enum":["IN_QUEUE","IN_PROGRESS","COMPLETED"]},"request_id":{"type":"string","description":"The request id."},"response_url":{"type":"string","description":"The response url."},"status_url":{"type":"string","description":"The status url."},"cancel_url":{"type":"string","description":"The cancel url."},"logs":{"type":"object","description":"The logs.","additionalProperties":true},"metrics":{"type":"object","description":"The metrics.","additionalProperties":true},"queue_position":{"type":"integer","description":"The queue position."}},"required":["status","request_id"]},"KlingVideoV21MasterImageToVideoInput":{"title":"ImageToVideoV21MasterRequest","type":"object","properties":{"prompt":{"examples":["Sunlight dapples through budding branches, illuminating a vibrant tapestry of greens and browns as a pair of robins meticulously weave twigs and mud into a cradle of life, their tiny forms a whirlwind of activity against a backdrop of blossoming spring.  The scene unfolds with a gentle, observational pace, allowing the viewer to fully appreciate the intricate details of nest construction, the soft textures of downy feathers contrasted against the rough bark of the branches, the delicate balance of strength and fragility in their creation."],"maxLength":2500,"type":"string","title":"Prompt"},"duration":{"enum":["5","10"],"title":"Duration","type":"string","description":"The duration of the generated video in seconds","default":"5"},"aspect_ratio":{"enum":["16:9","9:16","1:1"],"title":"Aspect Ratio","type":"string","description":"The aspect ratio of the generated video frame","default":"16:9"},"cfg_scale":{"minimum":0,"maximum":1,"type":"number","title":"Cfg Scale","description":"\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ","default":0.5},"negative_prompt":{"maxLength":2500,"type":"string","title":"Negative Prompt","default":"blur, distort, and low quality"},"image_url":{"format":"uri","description":"URL of the image to be used for the video","type":"string","examples":["https://v3.fal.media/files/rabbit/NEvlE6DqEks4afmShK9tY_image.webp"],"maxLength":2083,"minLength":1,"title":"Image Url"}},"x-fal-order-properties":["prompt","image_url","duration","aspect_ratio","negative_prompt","cfg_scale"],"required":["prompt","image_url"]},"KlingVideoV21MasterImageToVideoOutput":{"title":"ImageToVideoV21MasterOutput","type":"object","properties":{"video":{"examples":[{"url":"https://v3.fal.media/files/lion/HmIOSFgmINjNR57YC05iH_output.mp4"}],"title":"Video","description":"The generated video","allOf":[{"$ref":"#/components/schemas/File"}]}},"x-fal-order-properties":["video"],"required":["video"]},"File":{"title":"File","type":"object","properties":{"file_size":{"examples":[4404019],"title":"File Size","type":"integer","description":"The size of the file in bytes."},"file_name":{"examples":["z9RV14K95DvU.png"],"title":"File Name","type":"string","description":"The name of the file. It will be auto-generated if not provided."},"content_type":{"examples":["image/png"],"title":"Content Type","type":"string","description":"The mime type of the file."},"url":{"title":"Url","type":"string","description":"The URL where the file can be downloaded from."},"file_data":{"format":"binary","title":"File Data","type":"string","description":"File data"}},"x-fal-order-properties":["url","content_type","file_name","file_size","file_data"],"required":["url"]}}},"paths":{"/fal-ai/kling-video/v2.1/master/image-to-video/requests/{request_id}/status":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}},{"name":"logs","in":"query","required":false,"schema":{"type":"number","description":"Whether to include logs (`1`) in the response or not (`0`)."}}],"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/master/image-to-video/requests/{request_id}/cancel":{"put":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"The request was cancelled.","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean","description":"Whether the request was cancelled successfully."}}}}}}}}},"/fal-ai/kling-video/v2.1/master/image-to-video":{"post":{"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21MasterImageToVideoInput"}}}},"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/master/image-to-video/requests/{request_id}":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"Result of the request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21MasterImageToVideoOutput"}}}}}}}},"servers":[{"url":"https://queue.fal.run"}],"security":[{"apiKeyAuth":[]}]}

"openapi":"3.0.4","info":{"title":"Queue OpenAPI for fal-ai/kling-video/v2.1/master/text-to-video","version":"1.0.0","description":"","x-fal-metadata":{"endpointId":"fal-ai/kling-video/v2.1/master/text-to-video","category":"text-to-video","thumbnailUrl":"https://storage.googleapis.com/fal_cdn/fal/Upscale-5.jpeg","playgroundUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/master/text-to-video","documentationUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/master/text-to-video/api"}},"components":{"securitySchemes":{"apiKeyAuth":{"type":"apiKey","in":"header","name":"Authorization","description":"Fal Key"}},"schemas":{"QueueStatus":{"type":"object","properties":{"status":{"type":"string","enum":["IN_QUEUE","IN_PROGRESS","COMPLETED"]},"request_id":{"type":"string","description":"The request id."},"response_url":{"type":"string","description":"The response url."},"status_url":{"type":"string","description":"The status url."},"cancel_url":{"type":"string","description":"The cancel url."},"logs":{"type":"object","description":"The logs.","additionalProperties":true},"metrics":{"type":"object","description":"The metrics.","additionalProperties":true},"queue_position":{"type":"integer","description":"The queue position."}},"required":["status","request_id"]},"KlingVideoV21MasterTextToVideoInput":{"title":"TextToVideoV21MasterRequest","type":"object","properties":{"prompt":{"examples":["Warm, earthy tones bathe the scene as the potter's hands, rough and calloused, coax a shapeless lump of clay into a vessel of elegant curves, the slow, deliberate movements highlighted by the subtle shifting light; the clay’s cool, damp texture contrasts sharply with the warmth of the potter's touch, creating a captivating interplay between material and maker."],"maxLength":2500,"type":"string","title":"Prompt"},"duration":{"enum":["5","10"],"title":"Duration","type":"string","description":"The duration of the generated video in seconds","default":"5"},"aspect_ratio":{"enum":["16:9","9:16","1:1"],"title":"Aspect Ratio","type":"string","description":"The aspect ratio of the generated video frame","default":"16:9"},"negative_prompt":{"maxLength":2500,"type":"string","title":"Negative Prompt","default":"blur, distort, and low quality"},"cfg_scale":{"minimum":0,"maximum":1,"type":"number","title":"Cfg Scale","description":"\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ","default":0.5}},"x-fal-order-properties":["prompt","duration","aspect_ratio","negative_prompt","cfg_scale"],"required":["prompt"]},"KlingVideoV21MasterTextToVideoOutput":{"title":"TextToVideoV21MasterOutput","type":"object","properties":{"video":{"examples":[{"url":"https://v3.fal.media/files/lion/0wTlhR7GCXFI-_BZXGy99_output.mp4"}],"title":"Video","description":"The generated video","allOf":[{"$ref":"#/components/schemas/File"}]}},"x-fal-order-properties":["video"],"required":["video"]},"File":{"title":"File","type":"object","properties":{"file_size":{"examples":[4404019],"title":"File Size","type":"integer","description":"The size of the file in bytes."},"file_name":{"examples":["z9RV14K95DvU.png"],"title":"File Name","type":"string","description":"The name of the file. It will be auto-generated if not provided."},"content_type":{"examples":["image/png"],"title":"Content Type","type":"string","description":"The mime type of the file."},"url":{"title":"Url","type":"string","description":"The URL where the file can be downloaded from."},"file_data":{"format":"binary","title":"File Data","type":"string","description":"File data"}},"x-fal-order-properties":["url","content_type","file_name","file_size","file_data"],"required":["url"]}}},"paths":{"/fal-ai/kling-video/v2.1/master/text-to-video/requests/{request_id}/status":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}},{"name":"logs","in":"query","required":false,"schema":{"type":"number","description":"Whether to include logs (`1`) in the response or not (`0`)."}}],"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/master/text-to-video/requests/{request_id}/cancel":{"put":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"The request was cancelled.","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean","description":"Whether the request was cancelled successfully."}}}}}}}}},"/fal-ai/kling-video/v2.1/master/text-to-video":{"post":{"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21MasterTextToVideoInput"}}}},"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/master/text-to-video/requests/{request_id}":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"Result of the request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21MasterTextToVideoOutput"}}}}}}}},"servers":[{"url":"https://queue.fal.run"}],"security":[{"apiKeyAuth":[]}]}

{"openapi":"3.0.4","info":{"title":"Queue OpenAPI for fal-ai/kling-video/v2.1/standard/image-to-video","version":"1.0.0","description":"","x-fal-metadata":{"endpointId":"fal-ai/kling-video/v2.1/standard/image-to-video","category":"image-to-video","thumbnailUrl":"https://storage.googleapis.com/fal_cdn/fal/for%20videos-3.jpg","playgroundUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/standard/image-to-video","documentationUrl":"https://fal.ai/models/fal-ai/kling-video/v2.1/standard/image-to-video/api"}},"components":{"securitySchemes":{"apiKeyAuth":{"type":"apiKey","in":"header","name":"Authorization","description":"Fal Key"}},"schemas":{"QueueStatus":{"type":"object","properties":{"status":{"type":"string","enum":["IN_QUEUE","IN_PROGRESS","COMPLETED"]},"request_id":{"type":"string","description":"The request id."},"response_url":{"type":"string","description":"The response url."},"status_url":{"type":"string","description":"The status url."},"cancel_url":{"type":"string","description":"The cancel url."},"logs":{"type":"object","description":"The logs.","additionalProperties":true},"metrics":{"type":"object","description":"The metrics.","additionalProperties":true},"queue_position":{"type":"integer","description":"The queue position."}},"required":["status","request_id"]},"KlingVideoV21StandardImageToVideoInput":{"title":"ImageToVideoV21StandardRequest","type":"object","properties":{"prompt":{"examples":["As the sun dips below the horizon, painting the sky in fiery hues of orange and purple, powerful waves relentlessly crash against jagged, dark rocks, their white foam a stark contrast to the deepening twilight; the textured surface of the rocks, wet and glistening, reflects the vibrant colors, creating a mesmerizing spectacle of nature's raw power and breathtaking beauty"],"maxLength":2500,"type":"string","title":"Prompt"},"duration":{"enum":["5","10"],"title":"Duration","type":"string","description":"The duration of the generated video in seconds","default":"5"},"aspect_ratio":{"enum":["16:9","9:16","1:1"],"title":"Aspect Ratio","type":"string","description":"The aspect ratio of the generated video frame","default":"16:9"},"cfg_scale":{"minimum":0,"maximum":1,"type":"number","title":"Cfg Scale","description":"\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ","default":0.5},"negative_prompt":{"maxLength":2500,"type":"string","title":"Negative Prompt","default":"blur, distort, and low quality"},"image_url":{"format":"uri","description":"URL of the image to be used for the video","type":"string","examples":["https://v3.fal.media/files/panda/W-_J46zuJDQnUhqkKm9Iv_image.webp"],"maxLength":2083,"minLength":1,"title":"Image Url"}},"x-fal-order-properties":["prompt","image_url","duration","aspect_ratio","negative_prompt","cfg_scale"],"required":["prompt","image_url"]},"KlingVideoV21StandardImageToVideoOutput":{"title":"ImageToVideoV21StandardOutput","type":"object","properties":{"video":{"examples":[{"url":"https://v3.fal.media/files/panda/aIxNQwcs5syCT5sYT5_HB_output.mp4"}],"title":"Video","description":"The generated video","allOf":[{"$ref":"#/components/schemas/File"}]}},"x-fal-order-properties":["video"],"required":["video"]},"File":{"title":"File","type":"object","properties":{"file_size":{"examples":[4404019],"title":"File Size","type":"integer","description":"The size of the file in bytes."},"file_name":{"examples":["z9RV14K95DvU.png"],"title":"File Name","type":"string","description":"The name of the file. It will be auto-generated if not provided."},"content_type":{"examples":["image/png"],"title":"Content Type","type":"string","description":"The mime type of the file."},"url":{"title":"Url","type":"string","description":"The URL where the file can be downloaded from."},"file_data":{"format":"binary","title":"File Data","type":"string","description":"File data"}},"x-fal-order-properties":["url","content_type","file_name","file_size","file_data"],"required":["url"]}}},"paths":{"/fal-ai/kling-video/v2.1/standard/image-to-video/requests/{request_id}/status":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}},{"name":"logs","in":"query","required":false,"schema":{"type":"number","description":"Whether to include logs (`1`) in the response or not (`0`)."}}],"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/standard/image-to-video/requests/{request_id}/cancel":{"put":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"The request was cancelled.","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean","description":"Whether the request was cancelled successfully."}}}}}}}}},"/fal-ai/kling-video/v2.1/standard/image-to-video":{"post":{"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21StandardImageToVideoInput"}}}},"responses":{"200":{"description":"The request status.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/QueueStatus"}}}}}}},"/fal-ai/kling-video/v2.1/standard/image-to-video/requests/{request_id}":{"get":{"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"string","description":"Request ID"}}],"responses":{"200":{"description":"Result of the request.","content":{"application/json":{"schema":{"$ref":"#/components/schemas/KlingVideoV21StandardImageToVideoOutput"}}}}}}}},"servers":[{"url":"https://queue.fal.run"}],"security":[{"apiKeyAuth":[]}]}