const express = require('express');
const cors = require('cors');
const path = require('path');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
const Replicate = require('replicate');
const fs = require('fs');
const multer = require('multer');
const axios = require('axios');

// Загружаем переменные окружения
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Создаем клиент Replicate с API ключом
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN || "****************************************",
});

// Создаем клиент Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ОШИБКА: Не заданы переменные окружения для Supabase');
  console.error('SUPABASE_URL:', supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseKey ? 'Установлен' : 'Не установлен');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Создаем клиент Supabase с сервисным ролевым ключом - тот же самый ключ, что и выше
const supabaseAdmin = supabase;

// Middleware
app.use(cors());
app.use(express.json());

// Статика для загруженных файлов
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
app.use('/uploads', express.static(uploadsDir));

// Настройка Multer для загрузки файлов
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir)
  },
  filename: function (req, file, cb) {
    const uniquePrefix = uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, uniquePrefix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB лимит
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Разрешены только изображения!'), false);
    }
  }
});

// Логирование запросов
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Проверка здоровья API
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'API server is running' });
});

// Эндпоинт для выдачи тестовых токенов
app.post('/api/test-credits', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Проверяем текущий баланс пользователя
    const { data: credits, error: creditError } = await supabase
      .from('user_credits')
      .select('amount')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (creditError) {
      console.error('Ошибка при проверке баланса:', creditError);
      return res.status(500).json({ error: 'Не удалось проверить баланс' });
    }
    
    // Определяем новый баланс
    let newBalance = 300; // Выдаем 300 тестовых токенов
    
    if (credits && credits.length > 0) {
      newBalance = credits[0].amount + 300;
    }
    
    // Добавляем токены
    const { error: updateError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount: newBalance,
        transaction_type: 'test_credits',
        description: 'Выдача тестовых токенов: +300 токенов',
        created_at: new Date().toISOString()
      });
    
    if (updateError) {
      console.error('Ошибка при выдаче тестовых токенов:', updateError);
      return res.status(500).json({ error: 'Не удалось выдать тестовые токены' });
    }
    
    console.log(`Пользователю ${userId} выдано 300 тестовых токенов. Новый баланс: ${newBalance}`);
    
    return res.json({ 
      success: true, 
      message: 'Тестовые токены выданы успешно',
      balance: newBalance
    });
  } catch (error) {
    console.error('Ошибка при выдаче тестовых токенов:', error);
    return res.status(500).json({
      error: 'Произошла ошибка при выдаче тестовых токенов',
      details: error.message
    });
  }
});

// Эндпоинт для пополнения баланса на 300 токенов
app.post('/api/add-credits', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Проверяем текущий баланс пользователя
    const { data: credits, error: creditError } = await supabase
      .from('user_credits')
      .select('amount')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (creditError) {
      console.error('Ошибка при проверке баланса:', creditError);
      return res.status(500).json({ error: 'Не удалось проверить баланс' });
    }
    
    // Определяем новый баланс
    let newBalance = 300; // Добавляем 300 токенов
    
    if (credits && credits.length > 0) {
      newBalance = credits[0].amount + 300;
    }
    
    // Добавляем токены
    const { error: updateError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount: newBalance,
        transaction_type: 'credit_addition',
        description: 'Пополнение баланса: +300 токенов',
        created_at: new Date().toISOString()
      });
    
    if (updateError) {
      console.error('Ошибка при пополнении баланса:', updateError);
      return res.status(500).json({ error: 'Не удалось пополнить баланс' });
    }
    
    console.log(`Баланс пользователя ${userId} пополнен на 300 токенов. Новый баланс: ${newBalance}`);
    
    return res.json({ 
      success: true, 
      message: 'Баланс успешно пополнен',
      balance: newBalance
    });
  } catch (error) {
    console.error('Ошибка при пополнении баланса:', error);
    return res.status(500).json({
      error: 'Произошла ошибка при пополнении баланса',
      details: error.message
    });
  }
});

// Списываем кредиты
const deductCredits = async (userId, mode, model, duration) => {
  // Определяем стоимость генерации в зависимости от модели и режима
  let creditAmount = 0;
  
  // Определяем тип генерации и соответствующую стоимость
  if (mode === 'image') {
    creditAmount = 2; // 2 токена для обычной генерации изображений
  } else if (mode === 'img2img') {
    creditAmount = 10; // 10 токенов для img2img генерации изображений
  } else if (mode === 'text2video' || mode === 'img2video') {
    // Стоимость генерации видео зависит от модели и длительности
    if (model === 'luma/ray-flash-2-540p') {
      creditAmount = duration === 9 ? 160 : 100; // 100 токенов за 5с, 160 за 9с
    } else if (model === 'kwaivgi/kling-v1.6-standard') {
      creditAmount = duration === 10 ? 300 : 150; // 150 токенов за 5с, 300 за 10с
    } else if (model === 'kwaivgi/kling-v1.6-pro') {
      creditAmount = duration === 10 ? 600 : 300; // 300 токенов за 5с, 600 за 10с
    }
  }
  
  console.log(`Списание токенов: ${creditAmount} для режима ${mode}, модель ${model}, длительность ${duration}`);
  
  try {
    // Проверяем текущий баланс пользователя
    const { data: credits, error: creditError } = await supabase
      .from('user_credits')
      .select('amount')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (creditError) {
      console.error('Ошибка при проверке баланса:', creditError);
      return { success: false, error: 'Не удалось проверить баланс' };
    }
    
    // Если у пользователя нет записей, создаем начальный баланс
    let currentBalance = 100; // Начальный баланс
    
    if (credits && credits.length > 0) {
      currentBalance = credits[0].amount;
    }
    
    // Проверяем, достаточно ли у пользователя токенов
    if (currentBalance < creditAmount) {
      return { 
        success: false, 
        error: `Недостаточно токенов. Требуется: ${creditAmount}, доступно: ${currentBalance}` 
      };
    }
    
    // Списываем токены
    const newBalance = currentBalance - creditAmount;
    const { error: updateError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount: newBalance,
        transaction_type: mode.includes('video') ? 'video_generation' : 'image_generation',
        description: `Генерация ${mode.includes('video') ? 'видео' : 'изображения'} (${mode}, ${model}): -${creditAmount} токенов`,
        created_at: new Date().toISOString()
      });
    
    if (updateError) {
      console.error('Ошибка при списании токенов:', updateError);
      return { success: false, error: 'Не удалось списать токены' };
    }
    
    return { success: true, balance: newBalance };
  } catch (error) {
    console.error('Ошибка в процессе списания токенов:', error);
    return { success: false, error: 'Произошла ошибка при обработке токенов' };
  }
};

// Эндпоинт для Replicate API (генерация видео)
app.post('/api/replicate', async (req, res) => {
  try {
    const { 
      prompt, 
      width, 
      height, 
      duration, 
      userId, 
      model = 'luma/ray-flash-2-540p',
      startImageUrl,
      endImageUrl,
      mode = 'text2video'
    } = req.body;
    
    console.log('Received video generation request:', { 
      prompt, 
      width, 
      height, 
      duration, 
      model, 
      mode,
      hasStartImage: !!startImageUrl,
      hasEndImage: !!endImageUrl
    });
    
    if (mode === 'text2video' && !prompt) {
      return res.status(400).json({ error: 'Prompt is required for text-to-video mode' });
    }
    
    if (mode === 'img2video' && !startImageUrl) {
      return res.status(400).json({ error: 'Start image is required for image-to-video mode' });
    }
    
    // Проверяем и списываем токены, если передан userId
    if (userId && userId !== 'anonymous') {
      const creditResult = await deductCredits(userId, mode, model, duration);
      
      if (!creditResult.success) {
        return res.status(400).json({ 
          error: 'Credit check failed', 
          details: creditResult.error 
        });
      }
    }
    
    // Подготовка параметров в зависимости от модели и режима
    let input = {};
    
    if (model === 'luma/ray-flash-2-540p') {
      // Модель Ray Flash 2
      input = {
        prompt: prompt,
        width: width || 1024,
        height: height || 576,
        num_frames: duration ? duration * 24 : 120,
        fps: 24
      };
    } else if (model === 'kwaivgi/kling-v1.6-standard') {
      // Модель Kling v1.6 Standard
      input = {
        prompt: prompt || "Reflections in crystal mirrors, rainbow light, geometric world",
      };
      
      // Добавляем изображение, если оно есть
      if (startImageUrl) {
        input.start_image = startImageUrl;
      }
      
      // Стандартная версия не поддерживает конечное изображение
    } else if (model === 'kwaivgi/kling-v1.6-pro') {
      // Модель Kling v1.6 Pro
      input = {
        prompt: prompt || "Reflections in crystal mirrors, rainbow light, geometric world",
      };
      
      // Добавляем изображения, если они есть
      if (startImageUrl) {
        input.start_image = startImageUrl;
      }
      
      if (endImageUrl) {
        input.end_image = endImageUrl;
      }
    }
    
    console.log('Sending request to Replicate API with params:', {
      model, 
      input
    });

    try {
      // Запускаем генерацию с помощью SDK
      const output = await replicate.run(
        model,
        { input }
      );
      
      console.log('Video generated successfully, output type:', typeof output, 'value:', output);
      
      // Получаем URL видео из результата
      let videoUrl = '';
      
      // Обработка разных форматов вывода
      if (typeof output === 'string') {
        videoUrl = output;
      } else if (Array.isArray(output)) {
        videoUrl = output[0] || '';
      } else if (output && typeof output === 'object') {
        // Если это объект, проверяем распространенные свойства для URL
        if (typeof output.url === 'function') {
          // Если output.url - это функция, вызываем её
          videoUrl = output.url();
        } else {
          videoUrl = output.url || output.video_url || output.output || '';
        }
        
        // Проверяем, является ли videoUrl функцией
        if (typeof videoUrl === 'function') {
          try {
            videoUrl = videoUrl();
          } catch (e) {
            console.error('Error calling URL function:', e);
            videoUrl = '';
          }
        }
        
        // Преобразуем в строку для логирования
        console.log('Video URL extracted from object:', videoUrl);
      } else {
        console.error('Unexpected output format from Replicate:', output);
        return res.status(500).json({
          error: 'Unexpected output format from Replicate',
          details: 'Could not extract video URL from response'
        });
      }
      
      if (!videoUrl) {
        console.error('No video URL found in Replicate response');
        return res.status(500).json({
          error: 'Failed to generate video',
          details: 'No video URL found in response'
        });
      }
      
      console.log('Final video URL:', videoUrl);
      
      // Сохраняем в базу данных Supabase, если передан userId
      if (userId && userId !== 'anonymous') {
        try {
          // Генерируем ID для записи
          const generationId = uuidv4();
          
          // Создаем запись в базе данных
          const { error } = await supabase
            .from('video_generations')
            .insert({
              id: generationId,
              user_id: userId,
              prompt: prompt,
              video_url: videoUrl,
              thumbnail_url: videoUrl, // Используем тот же URL в качестве миниатюры
              style: null,
              duration: duration || 4,
              aspect_ratio: `${width || 1024}:${height || 576}`,
              model: model,
              created_at: new Date().toISOString()
            });
            
          if (error) {
            console.error('Error saving video to Supabase:', error);
          } else {
            console.log('Video saved to Supabase successfully');
          }
        } catch (dbError) {
          console.error('Database error:', dbError);
        }
      }
      
      res.json({ 
        success: true, 
        videoUrl: videoUrl
      });
    } catch (replicateError) {
      console.error('Error in Replicate API call:', replicateError);
      return res.status(500).json({
        error: 'Failed to generate video',
        details: replicateError.message
      });
    }
  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).json({
      error: 'Failed to generate video',
      details: error.message
    });
  }
});

// Эндпоинт для Replicate API (генерация img2img)
app.post('/api/replicate-img2img', async (req, res) => {
  try {
    const { 
      prompt, 
      image, 
      userId,
      aspectRatio = "1:1"
    } = req.body;
    
    console.log('Received img2img generation request:', { 
      prompt,
      hasImage: !!image,
      aspectRatio,
      userId
    });
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }
    
    if (!image) {
      return res.status(400).json({ error: 'Image is required for img2img' });
    }
    
    // Проверяем и списываем токены, если передан userId
    if (userId && userId !== 'anonymous') {
      const creditResult = await deductCredits(userId, 'img2img', 'ideogram', null);
      
      if (!creditResult.success) {
        return res.status(400).json({ 
          error: 'Credit check failed', 
          details: creditResult.error 
        });
      }
    }
    
    // Запрос к Replicate API для модели ideogram-v2a-turbo
    try {
      // Создаем запрос к API Replicate
      const prediction = await replicate.run(
        "ideogram-ai/ideogram-v2a-turbo",
        {
          input: {
            prompt: prompt,
            image: image,
            aspect_ratio: aspectRatio
          }
        }
      );
      
      console.log('img2img generated successfully, result:', prediction);
      
      let imageUrl = '';
      
      // Обработка различных форматов вывода
      if (typeof prediction === 'string') {
        imageUrl = prediction;
      } else if (Array.isArray(prediction)) {
        imageUrl = prediction[0] || '';
      } else if (prediction && typeof prediction === 'object') {
        imageUrl = prediction.output || prediction.url || '';
      }
      
      if (!imageUrl) {
        console.error('No image URL found in Replicate response');
        return res.status(500).json({
          error: 'Failed to generate image',
          details: 'No image URL found in response'
        });
      }
      
      res.json({ 
        success: true, 
        imageUrl: imageUrl
      });
    } catch (replicateError) {
      console.error('Error in Replicate API call:', replicateError);
      return res.status(500).json({
        error: 'Failed to generate image',
        details: replicateError.message
      });
    }
  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).json({
      error: 'Failed to generate image',
      details: error.message
    });
  }
});

// Эндпоинт для загрузки изображений
app.post('/api/upload', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    // Формируем URL для доступа к загруженному файлу
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const imageUrl = `${baseUrl}/uploads/${req.file.filename}`;
    
    console.log(`File uploaded successfully: ${imageUrl}`);
    return res.status(200).json({ 
      success: true, 
      imageUrl: imageUrl 
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({ 
      error: 'File upload failed', 
      details: error.message 
    });
  }
});

// API endpoint для создания профиля пользователя с полными правами
app.post('/api/create-profile', async (req, res) => {
  try {
    const { userId, email, name } = req.body;
    
    if (!userId || !email) {
      return res.status(400).json({ error: 'User ID и email обязательны' });
    }
    
    console.log(`Попытка создания профиля для пользователя: ${userId}, ${email}`);
    
    // Проверяем, существует ли профиль
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle();
      
    if (checkError) {
      console.error('Ошибка при проверке профиля:', checkError);
    }
    
    if (existingProfile) {
      console.log('Профиль уже существует:', existingProfile);
      return res.json({ success: true, profile: existingProfile });
    }
    
    // Базовые поля профиля
    const profileData = {
      id: userId,
      email: email
    };
    
    // Добавляем имя, если оно указано
    if (name) {
      profileData.name = name;
    }
    
    // Добавляем начальные кредиты
    profileData.credits = 100;
    
    // Пробуем сначала с полями created_at и updated_at
    try {
      profileData.created_at = new Date().toISOString();
      
      const { data: profile, error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert(profileData)
        .select()
        .single();
        
      if (insertError) {
        console.log('Ошибка при создании профиля с полем created_at, пробуем без него:', insertError);
        throw insertError;
      }
      
      console.log('Профиль успешно создан с полем created_at:', profile);
      return res.json({ success: true, profile });
    } catch (error) {
      // Удаляем поля, которые могут вызывать ошибки
      delete profileData.created_at;
      
      const { data: simpleProfile, error: simpleError } = await supabaseAdmin
        .from('profiles')
        .insert(profileData)
        .select()
        .single();
        
      if (simpleError) {
        console.error('Не удалось создать профиль даже с упрощенными данными:', simpleError);
        
        // Если не удалось создать профиль, возвращаем ошибку
        return res.status(500).json({ 
          error: 'Не удалось создать профиль', 
          details: simpleError.message,
          code: simpleError.code 
        });
      }
      
      console.log('Профиль успешно создан с упрощенными данными:', simpleProfile);
      return res.json({ success: true, profile: simpleProfile });
    }
  } catch (error) {
    console.error('Критическая ошибка при создании профиля:', error);
    return res.status(500).json({ 
      error: 'Критическая ошибка при создании профиля', 
      details: error.message 
    });
  }
});

// API endpoint для проверки существования профиля пользователя
app.post('/api/check-profile', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID обязателен' });
    }
    
    console.log(`Проверка существования профиля для пользователя: ${userId}`);
    
    // Проверяем, существует ли профиль
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .maybeSingle();
      
    if (checkError) {
      console.error('Ошибка при проверке профиля:', checkError);
      return res.status(500).json({ error: 'Ошибка при проверке профиля', details: checkError.message });
    }
    
    if (existingProfile) {
      console.log('Профиль существует:', existingProfile.id);
      return res.json({ exists: true, id: existingProfile.id });
    } else {
      console.log('Профиль не найден');
      return res.json({ exists: false });
    }
  } catch (error) {
    console.error('Критическая ошибка при проверке профиля:', error);
    return res.status(500).json({ 
      error: 'Критическая ошибка при проверке профиля', 
      details: error.message 
    });
  }
});

// -----------------------------------------------------------------------------
// PROXY ENDPOINTS FOR EXTERNAL APIS
// -----------------------------------------------------------------------------

// YooKassa API proxy
app.post('/api/yukassa/payments', async (req, res) => {
  try {
    const {
      amount,
      currency = 'RUB',
      description = 'Покупка кредитов UMA.AI',
      userId,
      creditsAmount,
      returnUrl
    } = req.body;

    const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
    const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
    const API_URL = 'https://api.yookassa.ru/v3';

    // Создаем уникальный ключ идемпотентности для запроса
    const idempotenceKey = uuidv4();
    
    // Формируем данные для запроса
    const paymentData = {
      amount: {
        value: amount.toFixed(2),
        currency
      },
      capture: true,
      confirmation: {
        type: 'redirect',
        return_url: returnUrl
      },
      description,
      metadata: {
        userId,
        creditsAmount
      }
    };
    
    // HTTP Basic Auth
    const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
    
    // Отправляем запрос к API ЮKassa
    const response = await axios.post(`${API_URL}/payments`, paymentData, {
      headers: {
        'Authorization': `Basic ${authHeader}`,
        'Idempotence-Key': idempotenceKey,
        'Content-Type': 'application/json'
      }
    });
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Ошибка при создании платежа:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      error: 'Ошибка при создании платежа',
      details: error.response?.data || error.message
    });
  }
});

app.get('/api/yukassa/payments/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
    const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
    const API_URL = 'https://api.yookassa.ru/v3';
    
    // HTTP Basic Auth
    const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
    
    // Отправляем запрос к API ЮKassa
    const response = await axios.get(`${API_URL}/payments/${paymentId}`, {
      headers: {
        'Authorization': `Basic ${authHeader}`,
        'Content-Type': 'application/json'
      }
    });
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Ошибка при получении информации о платеже:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      error: 'Ошибка при получении информации о платеже',
      details: error.response?.data || error.message
    });
  }
});

// Google AI Proxy
app.post('/api/google/image-generation', async (req, res) => {
  try {
    const { prompt, model, negative_prompt, size, userId } = req.body;
    
    const API_KEY = process.env.GOOGLE_API_KEY;
    if (!API_KEY) {
      return res.status(400).json({ error: 'Google API key is not configured' });
    }
    
    // Опционально: проверка и списание кредитов
    if (userId && userId !== 'anonymous') {
      const creditResult = await deductCredits(userId, 'image', model || 'google');
      
      if (!creditResult.success) {
        return res.status(400).json({ 
          error: 'Credit check failed', 
          details: creditResult.error 
        });
      }
    }
    
    const response = await axios.post(
      "https://generativelanguage.googleapis.com/v1beta/models/imagegeneration@005:generateContent", 
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          negativePrompt: negative_prompt,
          ...(size && { size })
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': API_KEY
        }
      }
    );
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Ошибка при генерации изображения Google AI:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      error: 'Ошибка при генерации изображения Google AI',
      details: error.response?.data || error.message
    });
  }
});

// Together.ai Proxy
app.post('/api/together/image-generation', async (req, res) => {
  try {
    const { prompt, negative_prompt, model, userId, samplerName, numInferenceSteps, width, height, guidance } = req.body;
    
    const API_KEY = process.env.TOGETHER_API_KEY;
    if (!API_KEY) {
      return res.status(400).json({ error: 'Together.ai API key is not configured' });
    }
    
    // Проверка и списание кредитов
    if (userId && userId !== 'anonymous') {
      const creditResult = await deductCredits(userId, 'image', model || 'together');
      
      if (!creditResult.success) {
        return res.status(400).json({ 
          error: 'Credit check failed', 
          details: creditResult.error 
        });
      }
    }
    
    const baseUrl = `https://api.together.xyz/v1/completions`;
    
    const response = await axios.post(
      baseUrl,
      {
        model: model || "stabilityai/stable-diffusion-xl-base-1.0",
        prompt,
        negative_prompt,
        sampler_name: samplerName,
        num_inference_steps: numInferenceSteps,
        width,
        height,
        guidance_scale: guidance
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        }
      }
    );
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Ошибка при генерации изображения Together.ai:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      error: 'Ошибка при генерации изображения Together.ai',
      details: error.response?.data || error.message
    });
  }
});

// Stability.ai Proxy
app.post('/api/stability/image-generation', async (req, res) => {
  try {
    const { prompt, engine, width, height, steps, cfg_scale, sampler, userId } = req.body;
    
    const API_KEY = process.env.STABILITY_API_KEY;
    if (!API_KEY) {
      return res.status(400).json({ error: 'Stability API key is not configured' });
    }
    
    // Проверка и списание кредитов
    if (userId && userId !== 'anonymous') {
      const creditResult = await deductCredits(userId, 'image', engine || 'stability');
      
      if (!creditResult.success) {
        return res.status(400).json({ 
          error: 'Credit check failed', 
          details: creditResult.error 
        });
      }
    }
    
    const response = await axios.post(
      `https://api.stability.ai/v1/generation/${engine}/text-to-image`,
      {
        text_prompts: [{ text: prompt }],
        cfg_scale: cfg_scale || 7,
        width: width || 1024,
        height: height || 1024,
        steps: steps || 50,
        sampler: sampler || 'K_EULER'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`,
          'Accept': 'application/json'
        }
      }
    );
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Ошибка при генерации изображения Stability:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      error: 'Ошибка при генерации изображения Stability',
      details: error.response?.data || error.message
    });
  }
});

// API endpoint для объединения видео
app.post('/api/video/combine', async (req, res) => {
  try {
    const { videoUrls, aspectRatio, resolution, userId } = req.body;

    if (!videoUrls || !Array.isArray(videoUrls) || videoUrls.length === 0) {
      return res.status(400).json({ error: 'videoUrls is required and must be a non-empty array' });
    }

    console.log('Combining videos:', { videoUrls, aspectRatio, resolution, userId });

    // TODO: Реализовать реальное объединение видео через FFmpeg
    // Пока что возвращаем первое видео как "объединенное"
    // В реальной реализации здесь будет:
    // 1. Скачивание всех видео
    // 2. Объединение через FFmpeg
    // 3. Загрузка результата в Supabase Storage
    // 4. Возврат URL объединенного видео

    // Временное решение: возвращаем первое видео
    const combinedVideoUrl = videoUrls[0];

    // Симуляция времени обработки
    await new Promise(resolve => setTimeout(resolve, 2000));

    res.status(200).json({
      success: true,
      videoUrl: combinedVideoUrl,
      message: 'Videos combined successfully (temporary implementation)'
    });

  } catch (error) {
    console.error('Error combining videos:', error);
    res.status(500).json({
      error: 'Failed to combine videos',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// API endpoint для сохранения видео в базу данных
app.post('/api/video/save', async (req, res) => {
  try {
    const {
      userId,
      videoUrl,
      isPrivate,
      prompt,
      duration,
      resolution,
      aspectRatio,
      fragmentDuration,
      transitionType,
      scenes
    } = req.body;

    if (!userId || !videoUrl || !prompt) {
      return res.status(400).json({ error: 'userId, videoUrl, and prompt are required' });
    }

    console.log('Saving video to database:', { userId, videoUrl, isPrivate, prompt });

    // Сохраняем в таблицу video_generations
    const { data: videoData, error: videoError } = await supabase
      .from('video_generations')
      .insert({
        user_id: userId,
        prompt: prompt,
        video_url: videoUrl,
        model: 'create-mode-pixverse',
        duration: duration,
        resolution: resolution,
        aspect_ratio: aspectRatio,
        fragment_duration: fragmentDuration,
        transition_type: transitionType,
        is_private: isPrivate || false,
        scenes: scenes || [],
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (videoError) {
      console.error('Error saving video generation:', videoError);
      return res.status(500).json({ error: 'Failed to save video generation', details: videoError.message });
    }

    // Если видео публичное, добавляем в дашборд
    if (!isPrivate) {
      const { error: dashboardError } = await supabase
        .from('dashboard_videos')
        .insert({
          user_id: userId,
          video_url: videoUrl,
          prompt: prompt,
          model: 'create-mode-pixverse',
          duration: duration,
          resolution: resolution,
          aspect_ratio: aspectRatio,
          likes: 0,
          created_at: new Date().toISOString()
        });

      if (dashboardError) {
        console.error('Error saving to dashboard:', dashboardError);
        // Не возвращаем ошибку, так как основное сохранение прошло успешно
      }
    }

    res.status(200).json({
      success: true,
      videoId: videoData.id,
      message: 'Video saved successfully'
    });

  } catch (error) {
    console.error('Error saving video:', error);
    res.status(500).json({
      error: 'Failed to save video',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Запуск сервера на порту 3000
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
  console.log(`API endpoints: http://localhost:${PORT}/api/replicate`);
  console.log(`Video API: http://localhost:${PORT}/api/video/combine`);
  console.log(`Video Save: http://localhost:${PORT}/api/video/save`);
});