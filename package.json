{"name": "umaai-platform", "description": "UMA.AI - Платформа искусственного интеллекта для генерации контента. Создавайте тексты, изображения и видео с помощью ИИ.", "keywords": ["ai", "artificial intelligence", "image generation", "video generation", "text generation", "neural networks"], "author": "UMA.AI", "private": true, "version": "0.1.0", "homepage": "https://umaai.site", "type": "module", "scripts": {"dev": "vite", "prebuild": "node -e \"const fs=require('fs');const path=require('path');const timestamp=Date.now();const cacheContent='// Generated at '+new Date().toISOString()+'\\nexport const CACHE_TIMESTAMP = '+timestamp+';\\n';const assetsDir=path.join(__dirname,'src','assets');if(!fs.existsSync(assetsDir)){fs.mkdirSync(assetsDir,{recursive:true});}const cacheBustFile=path.join(assetsDir,'cache-timestamp.js');fs.writeFileSync(cacheBustFile,cacheContent);console.log('Cache bust file created with timestamp: '+timestamp);\"", "build": "rimraf dist && vite build", "build:dev": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier . --write", "predeploy": "npm run build && echo 'Clearing Vercel cache...'", "deploy": "vercel --prod"}, "dependencies": {"@fal-ai/client": "^1.5.0", "@headlessui/react": "^2.2.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.56.2", "@types/pg": "^8.11.11", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.9.2", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "pg": "^8.14.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "replicate": "^0.25.0", "sharp": "^0.34.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "vite-plugin-remove-console": "^2.2.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "gh-pages": "^6.3.0", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "rimraf": "^6.0.1", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}