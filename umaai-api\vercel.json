{"version": 2, "builds": [{"src": "server.js", "use": "@vercel/node"}], "rewrites": [{"source": "/(.*)", "destination": "/server.js"}], "env": {"SUPABASE_URL": "${SUPABASE_URL}", "SUPABASE_SERVICE_ROLE_KEY": "${SUPABASE_SERVICE_ROLE_KEY}", "TOGETHER_API_KEY": "${TOGETHER_API_KEY}", "REPLICATE_API_TOKEN": "${REPLICATE_API_TOKEN}"}, "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data:; connect-src 'self' https://umaai-api.vercel.app https://*.supabase.co https://api.together.ai https://api.replicate.com https://generativelanguage.googleapis.com wss://*.supabase.co https://umaai.site https://umaai.site/api http://localhost:* https://localhost:*;"}, {"key": "Access-Control-Allow-Origin", "value": "https://umaai.vercel.app, https://umaai.site"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}]}