// Импортируем необходимые модули
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

// Константы с данными для аутентификации в ЮKassa
const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
const API_URL = 'https://api.yookassa.ru/v3';

// Инициализация клиента Supabase
const supabaseUrl = process.env.SUPABASE_URL || 'https://ehamdaltpbuicmggxhbn.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  // Разрешаем только POST-запросы для вебхуков
  if (req.method === 'POST') {
    try {
      console.log('Получен вебхук от ЮKassa:', req.body);
      
      // Проверяем, что это действительно уведомление о платеже
      const { event, object } = req.body;
      
      if (event !== 'payment.succeeded' || !object || !object.id) {
        console.log('Получено уведомление, но это не успешный платеж:', event);
        return res.status(200).end(); // Отвечаем 200 OK, чтобы ЮKassa не пыталась повторить отправку
      }
      
      const paymentId = object.id;
      console.log('Получено уведомление об успешном платеже:', paymentId);
      
      // HTTP Basic Auth для проверки платежа
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      // Получаем подробную информацию о платеже напрямую от ЮKassa
      const response = await axios.get(`${API_URL}/payments/${paymentId}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`,
          'Content-Type': 'application/json'
        }
      });
      
      const paymentInfo = response.data;
      console.log('Получена информация о платеже от ЮKassa:', paymentInfo);
      
      // Проверяем, что платеж действительно успешен
      if (paymentInfo.status !== 'succeeded') {
        console.log('Статус платежа не succeeded:', paymentInfo.status);
        return res.status(200).end();
      }
      
      // Получаем метаданные платежа
      const { userId, creditsAmount } = paymentInfo.metadata || {};
      
      if (!userId || !creditsAmount) {
        console.error('В метаданных платежа отсутствует userId или creditsAmount');
        return res.status(200).end();
      }
      
      // Конвертируем creditsAmount в число
      const credits = parseInt(creditsAmount, 10);
      
      // Обновляем кредиты пользователя в Supabase
      const { data, error } = await supabase
        .from('profiles')
        .select('credits')
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error('Ошибка при получении профиля пользователя:', error);
        return res.status(200).end();
      }
      
      const currentCredits = data?.credits || 0;
      const newCredits = currentCredits + credits;
      
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ credits: newCredits })
        .eq('id', userId);
      
      if (updateError) {
        console.error('Ошибка при обновлении кредитов пользователя:', updateError);
        return res.status(200).end();
      }
      
      // Добавляем запись в историю транзакций, если есть такая таблица
      try {
        await supabase.from('credit_transactions').insert({
          user_id: userId,
          amount: credits,
          type: 'payment',
          description: `Покупка ${credits} кредитов. Платеж #${paymentId}`,
          payment_id: paymentId
        });
      } catch (transactionError) {
        console.log('Не удалось добавить запись в историю транзакций:', transactionError);
        // Не прерываем обработку, если не удалось добавить запись в историю
      }
      
      console.log(`Успешно начислено ${credits} кредитов пользователю ${userId}`);
      return res.status(200).end();
    } catch (error) {
      console.error('Ошибка при обработке вебхука:', error);
      // Всегда отвечаем 200 OK, чтобы ЮKassa не пыталась повторно отправить уведомление
      return res.status(200).end();
    }
  } else {
    // Если метод запроса не поддерживается
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 