import { useState, useEffect } from 'react';
import { Home, History, MessageSquare, Image, Video, Mic, Languages, Gem, X, CreditCard, Moon, Sun, BarChart3, Edit } from 'lucide-react';
import { NavLink, Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { useSidebarState } from '@/context/SidebarContext';
import { getUserCredits } from '@/utils/database';
import { Button } from "@/components/ui/button";
import LanguageSwitcher from './LanguageSwitcher';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { createPayment } from '@/utils/yukassa';
import { toast } from 'sonner';

const getSidebarLinks = (t: (key: string) => string, hasSecretAccess: boolean = false, isAdmin: boolean = false) => {
  const baseLinks = [
    { icon: Home, label: t('sidebar.home'), path: '/dashboard' },
    { icon: History, label: t('sidebar.history'), path: '/history' },
    { icon: MessageSquare, label: t('sidebar.text'), path: '/text' },
    { icon: Image, label: t('sidebar.image'), path: '/image' },
    { icon: Video, label: t('sidebar.video'), path: '/video' },
    { icon: Mic, label: t('sidebar.speech'), path: '/speech' },
  ];

  // Добавляем аналитику только для пользователей с секретным доступом
  if (hasSecretAccess) {
    baseLinks.push({ icon: BarChart3, label: t('sidebar.analytics') || 'Аналитика', path: '/analytics' });
  }

  // Добавляем админку блога только для администратора
  if (isAdmin) {
    baseLinks.push({ icon: Edit, label: 'Админка блога', path: '/blog-admin' });
  }

  return baseLinks;
};

interface SidebarProps {
  className?: string;
}

const Sidebar = ({ className }: SidebarProps) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const { theme, setTheme } = useTheme();
  const { isExpanded: expanded, setIsExpanded: setExpanded } = useSidebarState();
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [plansOpen, setPlansOpen] = useState(false);
  const [credits, setCredits] = useState<number | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Проверяем секретный доступ и права администратора
  const hasSecretAccess = sessionStorage.getItem('secretAccess') === 'true';
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  const isAdmin = Boolean(user?.email && adminEmails.includes(user.email));
  const sidebarLinks = getSidebarLinks(t, hasSecretAccess, isAdmin);
  
  // Планы с переводами - перемещены внутрь компонента
  const creditPlans = [
    {
      id: 'basic',
      name: t('credits.plans.basic.name'),
      price: "300 ₽",
      credits: 600,
      details: [
        "text2img (5 cr.)",
        "img2img (10 cr.)",
        "video (5s) (100 cr.)",
        "video (9s) (150 cr.)"
      ]
    },
    {
      id: 'standard',
      name: t('credits.plans.standard.name'),
      price: "750 ₽",
      credits: 1500,
      details: [
        "text2img (5 cr.)",
        "img2img (10 cr.)",
        "video (5s) (100 cr.)",
        "video (9s) (150 cr.)"
      ]
    },
    {
      id: 'premium',
      name: t('credits.plans.premium.name'),
      price: "2000 ₽",
      credits: 4000,
      details: [
        "text2img (5 cr.)",
        "img2img (10 cr.)",
        "video (5s) (100 cr.)",
        "video (9s) (150 cr.)"
      ]
    }
  ];

  const handleMouseEnter = () => {
    if (hoverTimeout) clearTimeout(hoverTimeout);
    setExpanded(true);
  };

  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setExpanded(false);
    }, 300);
    setHoverTimeout(timeout);
  };

  useEffect(() => {
    return () => {
      if (hoverTimeout) clearTimeout(hoverTimeout);
    };
  }, [hoverTimeout]);

  // Загрузка и обновление кредитов пользователя
  useEffect(() => {
    const fetchCredits = async () => {
      if (user) {
        try {
          console.log('Запрос кредитов для пользователя:', user.id);
          
          // Сначала получаем кэшированное значение
          const cachedCredits = localStorage.getItem(`userCredits_${user.id}`);
          if (cachedCredits) {
            setCredits(parseInt(cachedCredits, 10));
          }
          
          // Затем запрашиваем актуальные данные
          const userCredits = await getUserCredits(user.id);
          console.log('Получены кредиты:', userCredits);
          setCredits(userCredits);
          
          // Кэшируем значение
          localStorage.setItem(`userCredits_${user.id}`, String(userCredits));
        } catch (error) {
          console.error('Ошибка при получении кредитов:', error);
          
          // Пытаемся использовать кэшированное значение, если есть ошибка
          const cachedCredits = localStorage.getItem(`userCredits_${user.id}`);
          if (cachedCredits) {
            console.log('Используем кэшированное значение кредитов:', cachedCredits);
            setCredits(parseInt(cachedCredits, 10));
          } else {
            // При сетевых ошибках не изменяем значение, при других - устанавливаем 0
            if (!(error instanceof Error && error.message.includes('Network error'))) {
              setCredits(0);
            }
          }
        }
      } else {
        console.log('Пользователь не авторизован, кредиты не запрашиваются');
        setCredits(0);
      }
    };
    
    fetchCredits();
    
    // Обработчик события обновления кредитов
    const handleCreditsUpdated = (event: any) => {
      const { userId, newBalance } = event.detail;
      if (user?.id === userId) {
        console.log(`Обновление баланса в боковой панели: ${newBalance} кредитов`);
        setCredits(newBalance);
        
        // Кэшируем значение
        localStorage.setItem(`userCredits_${userId}`, String(newBalance));
        
        // Принудительно запрашиваем обновление баланса для надежности
        setTimeout(() => {
          fetchCredits();
        }, 500);
      }
    };
    
    // Добавляем слушатель события
    window.addEventListener('creditsUpdated', handleCreditsUpdated);
    
    return () => {
      window.removeEventListener('creditsUpdated', handleCreditsUpdated);
    };
  }, [user]);

  // Функция для создания платежа и перехода на страницу оплаты
  const handleBuyCredits = async (plan: any) => {
    if (!user) {
      toast.error(i18n.language === 'ru' ? 'Пожалуйста, войдите в аккаунт' : 'Please log in');
      return;
    }
    if (!user.email) {
      toast.error(i18n.language === 'ru' ? 'Не найден email пользователя' : 'User email not found');
      return;
    }

    console.log('Нажата кнопка покупки кредитов в боковой панели:', plan);
    setIsProcessing(true);
    
    try {
      // Получаем цену из строки и конвертируем в число
      const priceStr = plan.price.replace(/[^\d]/g, '');
      const amount = parseInt(priceStr, 10);
      
      if (isNaN(amount)) {
        throw new Error('Некорректная сумма платежа');
      }
      
      // URL для возврата после оплаты
      const returnUrl = `${window.location.origin}/payment-callback`;

      console.log('Создание платежа:', {
        amount, 
        name: plan.name, 
        userId: user.id, 
        credits: plan.credits
      });

      // Индикатор загрузки
      toast.loading(i18n.language === 'ru' ? 'Создание платежа...' : 'Creating payment...');

      // Вызываем API для создания платежа
      const payment = await createPayment({
        amount: amount,
        description: `${plan.name}: ${plan.credits} кредитов`,
        userId: user.id,
        creditsAmount: plan.credits,
        returnUrl,
        email: user.email!
      });

      console.log('Ответ API платежа:', payment);
      toast.dismiss();

      // Проверяем наличие необходимых данных в ответе
      if (!payment || !payment.id) {
        throw new Error('Некорректный ответ от платежного API');
      }

      // Если платеж создан и имеет URL для оплаты
      if (payment.confirmation && payment.confirmation.confirmation_url) {
        // Сохраняем ID платежа для последующей проверки
        localStorage.setItem('pendingPaymentId', payment.id);
        
        console.log('Перенаправление на платежную страницу:', payment.confirmation.confirmation_url);
        toast.success(
          i18n.language === 'ru' 
            ? 'Переходим на страницу оплаты...' 
            : 'Redirecting to payment page...',
          { duration: 2000 }
        );
        
        // Небольшая задержка перед перенаправлением для показа сообщения
        setTimeout(() => {
          // Перенаправляем пользователя на страницу оплаты
          if (typeof payment.confirmation.confirmation_url === 'string') {
            window.location.href = payment.confirmation.confirmation_url;
          }
        }, 1000);
      } else {
        throw new Error('Не получен URL для подтверждения платежа');
      }
    } catch (error) {
      console.error('Ошибка при обработке платежа:', error);
      toast.dismiss();
      toast.error(
        i18n.language === 'ru'
          ? `Ошибка при создании платежа: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`
          : `Error creating payment: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Мобильная бургер-кнопка
  const BurgerButton = () => (
    <button
      className="fixed top-4 left-4 z-50 flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground shadow-lg sm:hidden"
      onClick={() => setMobileOpen(true)}
      aria-label="Открыть меню"
    >
      <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>
  );

  // Overlay Sidebar для мобильных
  const MobileSidebar = () => (
    <div
      className={cn(
        "fixed inset-0 z-50 flex sm:hidden transition-all",
        mobileOpen ? "visible" : "invisible pointer-events-none"
      )}
      style={{ background: mobileOpen ? "rgba(0,0,0,0.4)" : "transparent" }}
      onClick={() => setMobileOpen(false)}
    >
      <div
        className={cn(
          "relative bg-background shadow-md h-full w-4/5 max-w-xs flex flex-col transition-transform duration-300",
          mobileOpen ? "translate-x-0" : "-translate-x-full"
        )}
        onClick={e => e.stopPropagation()}
      >
        {/* Кнопка закрытия */}
        <button
          className="absolute top-4 right-4 z-10 flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground"
          onClick={() => setMobileOpen(false)}
          aria-label="Закрыть меню"
        >
          <X size={20} />
        </button>
        {/* Содержимое Sidebar */}
        {/* Вставим оригинальное содержимое ниже */}
        <div className="flex items-center justify-center p-4">
          <Link to="/">
            <h2 className="text-2xl font-bold text-foreground">Uma AI</h2>
          </Link>
        </div>
        <nav className="flex-1 py-6">
          <ul className="flex flex-col gap-2 px-4">
            {sidebarLinks.map((link) => (
              <li key={link.path}>
                <NavLink
                  to={link.path}
                  className={({ isActive }) => cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 transition-all duration-200",
                    isActive ? "bg-primary text-primary-foreground" : "text-foreground/70 hover:bg-foreground/5 hover:text-foreground"
                  )}
                  onClick={() => setMobileOpen(false)}
                >
                  <link.icon size={20} />
                  <span>{link.label}</span>
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
        {/* Остальные элементы (кредиты, язык, покупка) можно добавить по аналогии */}
        <div className="mt-auto pb-6 px-4 flex flex-col gap-4">
          {/* Счетчик кредитов */}
          <div className="flex items-center rounded-lg px-3 py-2 text-foreground bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700">
            <CreditCard size={20} className="text-foreground/70" />
            <div className="flex flex-col ml-3">
              <span className="text-sm font-semibold">{credits !== null ? `${credits}` : "0"}</span>
              <span className="text-xs text-foreground/60">{i18n.language === 'ru' ? 'токенов' : 'tokens'}</span>
            </div>
          </div>
          {/* Переключатели языка и темы */}
          <div className="flex gap-2">
            {/* Переключатель темы */}
            <button
              className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 hover:bg-foreground/5 opacity-80 hover:opacity-100 transition-opacity"
              onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
              aria-label={theme === 'light' ? 'Переключить на темную тему' : 'Переключить на светлую тему'}
            >
              {theme === 'light' ? (
                <Moon size={18} className="text-foreground/70" />
              ) : (
                <Sun size={18} className="text-foreground/70" />
              )}
            </button>
            {/* Переключатель языка */}
            <button
              className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 hover:bg-foreground/5 opacity-80 hover:opacity-100 transition-opacity"
              onClick={() => i18n.changeLanguage(i18n.language === 'ru' ? 'en' : 'ru')}
            >
              <Languages size={18} className="text-foreground/70" />
            </button>
          </div>
          {/* Кнопка покупки кредитов */}
          <button
            className="flex items-center gap-2 justify-center rounded-lg px-3 py-2 bg-primary text-white font-semibold shadow hover:bg-primary/80 transition"
            onClick={() => setPlansOpen(true)}
          >
            <Gem size={16} />
            <span className="text-white">{t('navigation.buyCredits')}</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <BurgerButton />
      <MobileSidebar />
      <div 
        className={cn(
          'fixed left-0 top-0 h-screen z-40 flex-col bg-background shadow-md transition-all duration-300 ease-in-out hidden sm:flex',
          expanded ? 'sidebar-expanded' : 'sidebar-collapsed',
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="flex items-center justify-center p-4">
          <div className={cn("flex items-center", !expanded && "justify-center w-full")}>
            {expanded ? (
              <Link to="/">
                <h2 className="text-2xl font-bold text-foreground">Uma AI</h2>
              </Link>
            ) : (
              <Link to="/">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center cursor-pointer">
                  <span className="text-sm font-bold text-primary-foreground">U</span>
                </div>
              </Link>
            )}
          </div>
        </div>
        <nav className="flex-1 py-6">
          <ul className="flex flex-col gap-2 px-4">
            {sidebarLinks.map((link) => (
              <li key={link.path}>
                <NavLink
                  to={link.path}
                  className={({ isActive }) => cn(
                    "flex items-center gap-3 transition-all duration-200",
                    expanded ? "rounded-lg px-3 py-2" : "flex justify-center items-center h-10 w-10 rounded-lg",
                    isActive ? "bg-primary text-primary-foreground" : "text-foreground/70 hover:bg-foreground/5 hover:text-foreground"
                  )}
                >
                  <link.icon size={20} />
                  {expanded && <span>{link.label}</span>}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
        <div className="mt-auto pb-6">
          {/* Кнопка Buy Credits с модальным окном */}
          <Dialog open={plansOpen} onOpenChange={setPlansOpen} modal={true}>
            <DialogTrigger asChild>
              <div
                data-testid="buy-credits-sidebar"
                onClick={() => setPlansOpen(true)}
                className={cn(
                  "mb-6 mx-4 flex justify-center items-center cursor-pointer",
                  expanded ? "p-3 rounded-lg bg-foreground/5 border border-border" : "h-10 w-10 rounded-full bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700"
                )}
              >
                {expanded ? (
                  <div className="w-full">
                    <div className="flex items-center gap-2">
                      <Gem size={16} className="text-gray-700 dark:text-gray-300" />
                      <span className="text-sm font-medium text-foreground">{t('navigation.buyCredits')}</span>
                    </div>
                    <p className="text-xs text-foreground/70 mt-1">{t('credits.buyMore')}</p>
                  </div>
                ) : (
                  <Gem size={20} className="text-gray-700 dark:text-gray-300" />
                )}
              </div>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[850px] p-0 bg-white/20 backdrop-blur-xl border border-white/20 shadow-xl rounded-xl max-h-screen sm:max-h-[80vh] overflow-y-auto" hideCloseButton>
              <div className="p-6">
                <div className="flex justify-between items-center mb-6 sticky top-0 z-20">
                  <div>
                    <h2 className="text-xl font-bold">{t('credits.title')}</h2>
                    <p className="text-sm text-foreground/70">{t('credits.description')}</p>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => setPlansOpen(false)}
                    className="h-8 w-8 rounded-full ml-auto"
                  >
                    <X size={18} />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {creditPlans.map((plan, index) => (
                    <div
                      key={index}
                      className="border border-white/40 rounded-xl p-5 bg-white/25 shadow-sm hover:shadow-md transition-shadow backdrop-blur-md"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-bold text-black flex items-center gap-2">
                          {plan.name}
                        </h3>
                        <span className="font-bold text-black">{plan.price}</span>
                      </div>
                      
                      <div className="flex items-center gap-1.5 mb-3 border-b border-white/30 pb-3">
                        <Gem size={14} className="text-black" />
                        <span className="text-sm font-semibold text-black">{plan.credits} {t('buyCredits.credits')}</span>
                      </div>
                      
                      <div className="space-y-2 text-xs font-medium text-black mb-4">
                        {plan.details.map((detail, i) => (
                          <div key={i} className="flex items-start gap-2">
                            <div className="w-1 h-1 rounded-full bg-black mt-1.5"></div>
                            <span>{detail}</span>
                          </div>
                        ))}
                      </div>
                      
                      <Button
                        className="w-full mt-auto bg-primary hover:bg-primary/80 text-primary-foreground font-semibold transition-colors duration-200"
                        disabled={isProcessing}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Click на кнопке покупки кредитов');
                          handleBuyCredits(plan);
                        }}
                      >
                        {isProcessing
                          ? (i18n.language === 'ru' ? 'Загрузка...' : 'Loading...')
                          : t('credits.action')
                        }
                      </Button>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 text-center">
                  <span className="text-xs font-medium text-black">* {t('textGen.pageTitle')} - {t('credits.unlimitedCredits')}</span>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <div className="space-y-2 px-4">
            {/* Счетчик кредитов */}
            <div className={cn(
              "flex items-center transition-all duration-200 mb-3",
              expanded ? "rounded-lg px-3 py-2 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700" : "justify-center items-center h-10 w-10 rounded-lg",
              "text-foreground"
            )}>
              <CreditCard size={20} className="text-foreground/70" />
              {expanded && (
                <div className="flex flex-col ml-3">
                  <span className="text-sm font-semibold">{credits !== null ? `${credits}` : "0"}</span>
                  <span className="text-xs text-foreground/60">{i18n.language === 'ru' ? 'токенов' : 'tokens'}</span>
                </div>
              )}
            </div>
            
            {!expanded && (
              <div className="flex justify-center items-center">
                <div className="text-xs font-semibold text-center text-foreground mb-2">
                  {credits !== null ? `${credits}` : "0"}
                </div>
              </div>
            )}
            
            {/* Переключатели темы и языка */}
            <div className={cn(
              "transition-all duration-200 space-y-2",
              expanded ? "px-1" : "flex flex-col justify-center items-center gap-2"
            )}>
              {/* Переключатель темы */}
              {expanded ? (
                <div className="flex items-center gap-2 rounded-lg px-3 py-2 hover:bg-foreground/5 opacity-80 hover:opacity-100 transition-opacity">
                  <button
                    className="flex items-center gap-2 w-full"
                    onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                    aria-label={theme === 'light' ? 'Переключить на темную тему' : 'Переключить на светлую тему'}
                  >
                    {theme === 'light' ? (
                      <Moon size={18} className="text-foreground/70" />
                    ) : (
                      <Sun size={18} className="text-foreground/70" />
                    )}
                    <span className="text-sm text-foreground/70">
                      {theme === 'light' ? t('sidebar.darkTheme') : t('sidebar.lightTheme')}
                    </span>
                  </button>
                </div>
              ) : (
                <button
                  className="flex h-10 w-10 items-center justify-center rounded-lg hover:bg-foreground/5 opacity-70 hover:opacity-100 transition-opacity"
                  onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                  aria-label={theme === 'light' ? 'Переключить на темную тему' : 'Переключить на светлую тему'}
                >
                  {theme === 'light' ? (
                    <Moon size={18} className="text-foreground/70" />
                  ) : (
                    <Sun size={18} className="text-foreground/70" />
                  )}
                </button>
              )}
              
              {/* Переключатель языка */}
              {expanded ? (
                <div className="rounded-lg opacity-80 hover:opacity-100 transition-opacity">
                  <LanguageSwitcher />
                </div>
              ) : (
                <button
                  className="flex h-10 w-10 items-center justify-center rounded-lg hover:bg-foreground/5 opacity-70 hover:opacity-100 transition-opacity"
                  onClick={() => i18n.changeLanguage(i18n.language === 'ru' ? 'en' : 'ru')}
                >
                  <Languages size={18} className="text-foreground/70" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
