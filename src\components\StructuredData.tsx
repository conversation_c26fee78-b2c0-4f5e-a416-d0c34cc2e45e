import { useEffect } from 'react';

interface StructuredDataProps {
  type: 'website' | 'organization' | 'software' | 'service';
  data?: any;
}

const StructuredData = ({ type, data }: StructuredDataProps) => {
  useEffect(() => {
    let structuredData: any = {};

    switch (type) {
      case 'website':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "UMA.AI",
          "description": "Платформа искусственного интеллекта для генерации контента",
          "url": "https://umaai.site",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://umaai.site/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "UMA.AI",
            "url": "https://umaai.site"
          }
        };
        break;

      case 'organization':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "UMA.AI",
          "description": "Платформа искусственного интеллекта для генерации контента",
          "url": "https://umaai.site",
          "logo": "https://umaai.site/umaicon.png",
          "sameAs": [
            "https://umaai.site"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Russian", "English"]
          }
        };
        break;

      case 'software':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "UMA.AI",
          "description": "Платформа искусственного интеллекта для генерации текста, изображений и видео",
          "url": "https://umaai.site",
          "applicationCategory": "AI Platform",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "RUB",
            "availability": "https://schema.org/InStock"
          },
          "author": {
            "@type": "Organization",
            "name": "UMA.AI"
          },
          "featureList": [
            "Генерация изображений с помощью ИИ",
            "Генерация видео с помощью ИИ", 
            "Генерация текста с помощью ИИ",
            "Генерация речи с помощью ИИ"
          ]
        };
        break;

      case 'service':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": "UMA.AI - Генерация контента с помощью ИИ",
          "description": "Профессиональная платформа для создания текстов, изображений и видео с использованием искусственного интеллекта",
          "provider": {
            "@type": "Organization",
            "name": "UMA.AI",
            "url": "https://umaai.site"
          },
          "areaServed": "Worldwide",
          "availableLanguage": ["ru", "en"],
          "serviceType": "AI Content Generation",
          "category": "Artificial Intelligence",
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "AI Generation Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Генерация изображений",
                  "description": "Создание изображений с помощью ИИ"
                }
              },
              {
                "@type": "Offer", 
                "itemOffered": {
                  "@type": "Service",
                  "name": "Генерация видео",
                  "description": "Создание видео с помощью ИИ"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service", 
                  "name": "Генерация текста",
                  "description": "Создание текстов с помощью ИИ"
                }
              }
            ]
          }
        };
        break;

      default:
        return;
    }

    // Объединяем с дополнительными данными если они переданы
    if (data) {
      structuredData = { ...structuredData, ...data };
    }

    // Удаляем существующий script если есть
    const existingScript = document.querySelector(`script[data-schema-type="${type}"]`);
    if (existingScript) {
      existingScript.remove();
    }

    // Создаем новый script с структурированными данными
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-schema-type', type);
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    // Cleanup функция для удаления script при размонтировании
    return () => {
      const scriptToRemove = document.querySelector(`script[data-schema-type="${type}"]`);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [type, data]);

  return null;
};

export default StructuredData;
