import * as React from "react";
import { Dialog, DialogContent, DialogOverlay, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { X, Copy } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: { url: string; aspectRatio?: string; type?: "image" | "video"; prompt?: string }[];
}

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, images }) => {
  const [copied, setCopied] = React.useState(false);

  // Генерируем уникальные ID для доступности
  const titleId = React.useId();
  const descriptionId = React.useId();

  console.log('[ImageModal] Render', { isOpen, images });

  if (!isOpen || !images || images.length === 0) {
    console.log('[ImageModal] Not rendering: isOpen/empty images', { isOpen, images });
    return null;
  }

  const prompt = images[0]?.prompt || "";

  const handleCopy = () => {
    if (prompt) {
      navigator.clipboard.writeText(prompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 1200);
    }
  };

  // Определяем классы для контейнера изображений
  // Используем flexbox для гибкого расположения
  const mediaContainerClasses = cn(
    "relative w-full h-full gap-4",
    images.length === 1
      ? "flex justify-center items-center"
      : images.length === 4
      ? "grid grid-cols-2"
      : "flex flex-row justify-center items-center flex-wrap",
    // Для мобильных: всегда flex-col и w-full при >1 изображении, отключить grid и col-2 (включая 2 изображения)
    images.length > 1 && "max-sm:flex max-sm:flex-col max-sm:w-full max-sm:overflow-y-auto max-sm:h-[60vh] max-sm:items-stretch max-sm:grid-cols-1 max-sm:grid-none"
  );

  // Определяем классы для отдельных изображений/видео в зависимости от общего количества
  // Используем flex-basis и max-width/max-height для адаптивного размера
  const mediaItemClasses = (count: number) => cn(
    "flex flex-col items-center justify-center flex-shrink-0",
    count === 1
      ? "w-auto h-auto"
      : count === 2
      ? "w-[calc(50%-0.5rem)] h-auto max-h-[calc(96vh-150px)]"
      : count === 4
      ? "w-full h-full max-h-[calc((96vh-150px)/2)]"
      : "w-auto h-auto max-w-[45vw] max-h-[35vh]",
    // Для мобильных: ширина 100%, авто-высота, без ограничений по ширине
    "max-sm:w-full max-sm:max-w-none max-sm:h-auto max-sm:flex-shrink"
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm" />
      <DialogContent
        className={cn(
          "fixed left-1/2 top-1/2 z-50 w-auto max-w-[96vw] max-h-[96vh] bg-transparent border-none flex flex-col items-center justify-center",
          "py-12 px-4" // Фиксированные вертикальные отступы
        )}
        style={{ transform: "translate(-50%, -50%)" }}
        aria-labelledby={titleId}
        aria-describedby={descriptionId}
      >
        {/* Accessibility: hidden title and description */}
        <VisuallyHidden asChild>
          <DialogTitle id={titleId}>Generated Media Preview</DialogTitle>
        </VisuallyHidden>
        <VisuallyHidden asChild>
          <DialogDescription id={descriptionId}>Preview of generated images or videos.</DialogDescription>
        </VisuallyHidden>

        {/* Медиа-контент */}
        <div className={mediaContainerClasses} style={{ maxHeight: 'calc(96vh - 150px)' }}> {/* Больше вертикальный отступ */}
          {/* Кнопка закрытия */}
          <button
            onClick={onClose}
            className="absolute right-4 top-4 z-20 rounded-full bg-black/80 hover:bg-black text-white p-2 shadow-lg transition"
            aria-label="Закрыть"
          >
            <X size={24} />
          </button>

          {images.map((media, idx) => (
            <div key={idx} className={mediaItemClasses(images.length)}> {/* Use dynamic classes */}
              {media.type === "video" ? (
                <video
                  src={media.url}
                  controls
                  className={cn(
                    "object-contain rounded-lg shadow-lg bg-black", // Общие классы
                    images.length === 1
                      ? "max-w-[80vw] max-h-[calc(96vh-150px)]"
                      : "w-full h-full max-h-[calc(96vh-150px)]",
                    // Для мобильных: максимальная ширина, без переполнения
                    "max-sm:w-full max-sm:max-w-full max-sm:h-auto max-sm:mx-0"
                  )}
                  poster=""
                />
              ) : (
                <img
                  src={media.url}
                  alt={prompt ? `Сгенерированное изображение: ${prompt}` : `Сгенерированное изображение ${idx + 1}`}
                  className={cn(
                    "object-contain rounded-lg shadow-lg", // Общие классы
                    images.length === 1
                      ? "max-w-[80vw] max-h-[calc(96vh-150px)]"
                      : "w-full h-full max-h-[calc(96vh-150px)]",
                    // Для мобильных: ширина 100%, без max-w и mx, чтобы занимать всю ширину контейнера
                    "max-sm:w-full max-sm:max-w-none max-sm:h-auto max-sm:mx-0"
                  )}
                />
              )}
            </div>
          ))}
        </div>

        {/* Промпт и копирование */}
        {prompt && (
          <div className="absolute bottom-0 left-0 right-0 px-4 pb-4 flex justify-center items-center">
            <div className="flex items-center gap-2">
              <span className="text-sm text-white truncate max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl">
                {prompt}
              </span>
              <button
                onClick={handleCopy}
                className="p-1 rounded hover:bg-white/20 transition text-white"
                title="Скопировать промт"
              >
                <Copy size={18} />
              </button>
              {copied && (
                <span className="ml-2 text-xs text-green-400">Скопировано!</span>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImageModal;