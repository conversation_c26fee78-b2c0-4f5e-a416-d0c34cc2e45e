
@import url('https://fonts.googleapis.com/css2?family=Geologica:wght@400;500;600;700&family=Unbounded:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,700;1,400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 1rem;
  }

  .dark {
    --background: 240 6% 6%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }

  * {
    @apply border-border selection:bg-black/10 selection:text-black;
  }

  body {
    @apply bg-background text-foreground antialiased overflow-x-hidden;
    font-family: 'Geologica', 'Inter', sans-serif;
  }

  html {
    @apply scroll-smooth;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Unbounded', 'Playfair Display', serif;
    @apply text-foreground;
  }

  p, span, a, li {
    @apply text-foreground;
  }
}

@layer utilities {
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .glass-panel {
    @apply backdrop-blur-lg bg-white/90 dark:bg-black/90 border border-black/10 dark:border-white/10 shadow-[0_4px_12px_rgba(0,0,0,0.1)] dark:shadow-none;
  }

  .neo-glass {
    @apply backdrop-blur-2xl bg-white/95 dark:bg-black/95 border border-black/10 dark:border-white/10 shadow-[0_4px_20px_rgba(0,0,0,0.15)] dark:shadow-none;
  }

  .neo-input {
    @apply backdrop-blur-xl bg-white dark:bg-black border border-black/10 dark:border-white/10 rounded-2xl w-full max-w-[500px] mx-auto shadow-[0_2px_10px_rgba(0,0,0,0.1)] dark:shadow-none;
  }

  .text-gradient {
    @apply bg-gradient-to-br from-black via-black/90 to-black/70 dark:from-white dark:via-white/90 dark:to-white/70 bg-clip-text text-transparent;
  }

  .text-gradient-black {
    @apply bg-gradient-to-br from-black via-black to-black/80 dark:from-white dark:via-white dark:to-white/80 bg-clip-text text-transparent;
  }

  .bg-gradient-white {
    @apply bg-gradient-to-br from-white/80 via-white/90 to-white dark:from-black/80 dark:via-black/90 dark:to-black;
  }

  .dotted-bg {
    background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0;
  }

  .dark .dotted-bg {
    background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }

  .grid-bg {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                     linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0;
  }

  .dark .grid-bg {
    background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                     linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  .animation-delay-800 {
    animation-delay: 800ms;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Стили для контента блога */
  .prose {
    max-width: none;
  }

  .prose h2 {
    @apply text-2xl font-bold mt-8 mb-4 text-gradient;
  }

  .prose h3 {
    @apply text-xl font-semibold mt-6 mb-3 text-foreground;
  }

  .prose p {
    @apply mb-4 text-foreground/80 leading-relaxed;
  }

  .prose ul, .prose ol {
    @apply mb-4 pl-6;
  }

  .prose li {
    @apply mb-2 text-foreground/80;
  }

  .prose strong {
    @apply font-semibold text-foreground;
  }

  .prose a {
    @apply text-blue-600 dark:text-blue-400 hover:underline;
  }

  /* Shimmer effect для генерации */
  .shimmer-container {
    position: relative;
    overflow: hidden;
    border-radius: inherit;
  }

  .shimmer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
      110deg,
      transparent,
      rgba(255, 255, 255, 0.06) 20%,
      rgba(255, 255, 255, 0.12) 50%,
      rgba(255, 255, 255, 0.06) 80%,
      transparent
    );
    animation: shimmer 3s infinite ease-in-out;
    z-index: 1;
    pointer-events: none;
    clip-path: inset(0 0 0 0 round 0.75rem);
  }

  .dark .shimmer-container::before {
    background: linear-gradient(
      110deg,
      transparent,
      rgba(255, 255, 255, 0.03) 20%,
      rgba(255, 255, 255, 0.08) 50%,
      rgba(255, 255, 255, 0.03) 80%,
      transparent
    );
    clip-path: inset(0 0 0 0 round 0.75rem);
  }

  @keyframes shimmer {
    0% {
      left: -50%;
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    80% {
      opacity: 1;
    }
    100% {
      left: 100%;
      opacity: 0;
    }
  }
}

#root {
  @apply w-full max-w-none m-0 p-0;
}

.sidebar-expanded {
  width: 240px;
  transition: width 0.3s ease;
}

.sidebar-collapsed {
  width: 70px;
  transition: width 0.3s ease;
}
