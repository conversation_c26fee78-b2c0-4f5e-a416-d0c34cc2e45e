<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="icon" type="image/jpeg" href="/icona.jpeg" />
    <meta http-equiv="Content-Type" content="text/javascript; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="UMA AI - Генерируйте изображения и видео с помощью ИИ"
    />
    <!-- Обновленный CSP для разрешения необходимых скриптов -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self' https://uma-ai.vercel.app https://*.supabase.co;
      script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' 'inline-speculation-rules' https://*.googleapis.com https://*.gstatic.com chrome-extension: https://cdn.gpteng.co;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com https://*.googleapis.com;
      img-src 'self' data: blob: https://*.githubusercontent.com https://*.replicate.delivery https://*.supabase.co https://*.together.xyz https://*.googleapis.com https:;
      font-src 'self' data: https://fonts.gstatic.com https://*.gstatic.com https://*.googleapis.com;
      connect-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://*.supabase.co https://*.together.xyz https://api.together.ai https://generativelanguage.googleapis.com wss://*.supabase.co https://*.googleapis.com https://*.replicate.delivery https://api.replicate.com https://uma-ai.vercel.app https://uma-ai.vercel.app/api/replicate https://uma-ai-backend.vercel.app https://umaai-api.vercel.app https://umaaiapi.vercel.app http://localhost:* https://localhost:*;
      frame-src 'self' https://*.supabase.co;
      media-src 'self' blob: https://*.replicate.delivery;
      worker-src 'self' blob:;
    ">
    <title>UMA AI - Генерация контента</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 