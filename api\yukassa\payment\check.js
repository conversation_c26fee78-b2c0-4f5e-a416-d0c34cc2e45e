// Импортируем необходимые модули
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

// Константы с данными для аутентификации в ЮKassa
const SHOP_ID = process.env.YUKASSA_SHOP_ID || '1067016';
const SECRET_KEY = process.env.YUKASSA_SECRET_KEY || 'live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw';
const API_URL = 'https://api.yookassa.ru/v3';

// Инициализация клиента Supabase с явным указанием ключа
const supabaseUrl = process.env.SUPABASE_URL || 'https://ehamdaltpbuicmggxhbn.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzg4Mjg3MiwiZXhwIjoyMDU5NDU4ODcyfQ.nhTTkxS8gam_1PejxDX93nsVgKmy61BJdc_ss8xuAYc';

if (!supabaseUrl || !supabaseKey) {
  console.error('ОШИБКА: Не настроены переменные окружения для Supabase');
  throw new Error('Supabase configuration error');
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false
  }
});

export default async function handler(req, res) {
  // Явно устанавливаем заголовок Content-Type для JSON
  res.setHeader('Content-Type', 'application/json');
  
  // Разрешаем только GET-запросы для проверки платежа
  if (req.method === 'GET') {
    try {
      const { paymentId } = req.query;
      
      if (!paymentId) {
        return res.status(400).json({ 
          success: false, 
          message: 'Не указан ID платежа'
        });
      }
      
      console.log('Получен запрос на проверку платежа:', paymentId);
      
      // HTTP Basic Auth
      const authHeader = Buffer.from(`${SHOP_ID}:${SECRET_KEY}`).toString('base64');
      
      // Отправляем запрос к API ЮKassa для получения информации о платеже
      const response = await axios.get(`${API_URL}/payments/${paymentId}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`,
          'Content-Type': 'application/json'
        }
      });
      
      const paymentInfo = response.data;
      console.log('Получена информация о платеже от ЮKassa:', paymentInfo);
      
      // Проверяем статус платежа
      if (paymentInfo.status !== 'succeeded') {
        return res.status(200).json({
          success: false,
          message: `Платеж не завершен, текущий статус: ${paymentInfo.status}`
        });
      }
      
      // Получаем метаданные платежа
      const { userId, creditsAmount } = paymentInfo.metadata || {};
      
      if (!userId || !creditsAmount) {
        return res.status(200).json({
          success: false,
          message: 'В метаданных платежа отсутствует userId или creditsAmount'
        });
      }
      
      // Начисляем кредиты пользователю
      const creditAmount = parseInt(creditsAmount, 10);
      
      if (isNaN(creditAmount) || creditAmount <= 0) {
        return res.status(200).json({
          success: false,
          message: 'Некорректное количество кредитов'
        });
      }
      
      try {
        // Проверяем, была ли уже обработана транзакция с этим paymentId
        const { data: existingTx, error: txCheckError } = await supabase
          .from('credit_transactions')
          .select('id')
          .eq('payment_id', paymentId)
          .maybeSingle();

        if (txCheckError) {
          console.error('Ошибка при проверке существования транзакции:', txCheckError);
          return res.status(200).json({
            success: false,
            message: 'Ошибка при проверке существования транзакции'
          });
        }

        if (existingTx) {
          console.warn('Транзакция с этим paymentId уже была обработана, повторное начисление не производится');
          return res.status(200).json({
            success: true,
            userId,
            creditsAmount: creditAmount,
            message: 'Кредиты уже были начислены ранее'
          });
        }

        // Получаем текущий баланс пользователя ДО вставки транзакции
        const { data: profile, error: fetchError } = await supabase
          .from('profiles')
          .select('credits')
          .eq('id', userId)
          .single();

        if (fetchError) {
          console.error('Ошибка при получении профиля пользователя:', fetchError);
          return res.status(200).json({
            success: false,
            message: 'Ошибка при получении профиля пользователя'
          });
        }

        const currentCredits = profile?.credits || 0;
        const newCredits = currentCredits + creditAmount;

        // Вставляем транзакцию с balance_after
        const { error: transactionError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: creditAmount,
            type: 'payment',
            transaction_type: 'payment',
            description: `Пополнение баланса через ЮKassa (ID платежа: ${paymentId})`,
            payment_id: paymentId,
            balance_after: newCredits
          });

        if (transactionError) {
          // Если ошибка уникальности — транзакция уже была, не начисляем кредиты повторно
          if (
            transactionError.code === '23505' ||
            (transactionError.details && transactionError.details.includes('already exists'))
          ) {
            console.warn('Транзакция уже была, баланс не обновляем');
            return res.status(200).json({
              success: true,
              userId,
              creditsAmount: 0,
              message: 'Кредиты уже были начислены ранее'
            });
          }
          // Иная ошибка
          console.error('Ошибка при записи транзакции:', transactionError);
          return res.status(200).json({
            success: false,
            message: 'Ошибка при записи транзакции'
          });
        }

        // Только если транзакция успешно записана — обновляем баланс
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            credits: newCredits,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Ошибка при обновлении кредитов пользователя:', updateError);
          return res.status(200).json({
            success: false,
            message: 'Ошибка при обновлении кредитов пользователя'
          });
        }

        return res.status(200).json({
          success: true,
          userId,
          creditsAmount: creditAmount,
          newBalance: newCredits,
          message: 'Кредиты успешно начислены'
        });
        
      } catch (dbError) {
        console.error('Ошибка базы данных при начислении кредитов:', dbError);
        return res.status(200).json({
          success: false,
          message: 'Ошибка при начислении кредитов пользователю'
        });
      }
      
    } catch (error) {
      console.error('Payment check error:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        response: error.response?.data
      });

      // Всегда возвращаем JSON с деталями ошибки
      return res.status(500).json({
        success: false,
        error: {
          type: 'PAYMENT_CHECK_ERROR',
          message: 'Failed to check payment status',
          details: {
            yukassaError: error.response?.data || null,
            internalError: error.message
          },
          paymentId: paymentId
        }
      });
    }
  } else {
    // Если метод запроса не поддерживается
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      message: `Метод ${req.method} не поддерживается`
    });
  }
}
