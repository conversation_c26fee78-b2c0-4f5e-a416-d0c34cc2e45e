# Инструкция по управлению блогом Uma AI

## Как публиковать статьи в блоге

### 1. Доступ к админке блога

Для управления блогом у вас есть специальная админка, доступная только вам как администратору:

1. **Войдите в систему** с вашим админским аккаунтом (<EMAIL> или <EMAIL>)
2. **Перейдите в админку блога** одним из способов:
   - Через сайдбар: найдите пункт "Админка блога" в левом меню
   - Прямая ссылка: `/blog-admin`

### 2. Создание новой статьи

1. **Нажмите кнопку "Новая статья"** в правом верхнем углу админки
2. **Заполните все поля:**

#### Основная информация:
- **Заголовок (RU)** - название статьи на русском языке
- **Заголовок (EN)** - название статьи на английском языке
- **Краткое описание (RU)** - краткое описание для превью (2-3 предложения)
- **Краткое описание (EN)** - краткое описание на английском

#### Контент:
- **Контент (RU)** - полный текст статьи на русском в HTML формате
- **Контент (EN)** - полный текст статьи на английском в HTML формате

#### Дополнительные параметры:
- **Автор** - обычно "Uma AI Team"
- **Время чтения (мин)** - примерное время чтения статьи
- **Изображение (URL)** - ссылка на изображение из папки `/public/`
- **Теги** - теги через запятую (например: "Veo3, Видео, Руководство")

### 3. Форматирование контента

Контент статей использует HTML разметку. Основные теги:

```html
<h2>Заголовок раздела</h2>
<h3>Подзаголовок</h3>
<p>Обычный текст параграфа</p>
<ul>
  <li>Элемент списка</li>
  <li>Другой элемент</li>
</ul>
<ol>
  <li>Нумерованный список</li>
  <li>Второй пункт</li>
</ol>
<strong>Жирный текст</strong>
```

### 4. Выбор изображений

Изображения должны быть из папки `/public/`. Доступные изображения:

#### Для статей о видео:
- `/veo3-banner.webm` - для статей о Veo3 (рекомендуется)
- `/veo3.webm` - альтернативное видео Veo3
- `/Kling2.webm` - для статей о Kling
- `/Ray2flash.webm` - для статей о Ray 2 Flash
- `/Pixverse.webm` - для статей о Pixverse

#### Для статей об изображениях:
- `/FluxKontextMaxLight.webp` - Flux Kontext Max
- `/FluxKontextProLight.webp` - Flux Kontext Pro
- `/Ideogram3.png` - Ideogram 3
- `/Imagen4.png` - Imagen 4

#### Общие изображения:
- `/umaicon.png` - иконка Uma AI (для руководств по платформе)
- `/images/uma-ai-banner.jpg` - баннер Uma AI
- `/lovable-uploads/ab89e09b-adb8-459a-a40e-415c25cc846b.png` - общее изображение платформы

### 5. Публикация статьи

1. **Заполните все поля**
2. **Нажмите "Сохранить"** - статья сохранится как черновик
3. **Включите публикацию** - нажмите на иконку глаза рядом со статьей
4. **Статья появится в блоге** и будет доступна всем пользователям

### 6. Редактирование существующих статей

1. **Найдите статью** в списке админки
2. **Нажмите иконку редактирования** (карандаш)
3. **Внесите изменения**
4. **Сохраните изменения**

### 7. Управление видимостью

- **Опубликованные статьи** - видны всем пользователям в блоге
- **Черновики** - видны только в админке, не отображаются в публичном блоге
- **Переключение** - используйте иконку глаза для изменения статуса

### 8. Удаление статей

1. **Найдите статью** в админке
2. **Нажмите иконку корзины**
3. **Подтвердите удаление**

⚠️ **Внимание:** Удаление необратимо!

### 9. Советы по написанию статей

#### Структура хорошей статьи:
1. **Введение** - объясните, о чем статья
2. **Основные разделы** - разбейте контент на логические части
3. **Практические примеры** - добавляйте конкретные примеры
4. **Заключение** - подведите итоги

#### Рекомендации:
- **Используйте подзаголовки** для структурирования
- **Добавляйте списки** для лучшей читаемости
- **Выделяйте важные моменты** жирным текстом
- **Указывайте конкретные примеры** промптов и настроек
- **Добавляйте практические советы**

### 10. Технические детали

#### Хранение данных:
- Статьи сохраняются в `localStorage` браузера
- Для продакшена нужно будет подключить базу данных

#### Доступ:
- Только пользователи с email `<EMAIL>` имеют доступ к админке
- Все остальные пользователи могут только читать статьи

#### Резервное копирование:
- Периодически экспортируйте данные из `localStorage`
- Сохраняйте важные статьи в отдельных файлах

### 11. Примеры тем для статей

#### Обучающие статьи:
- "Как создать идеальный промпт для [модель]"
- "Сравнение моделей: когда использовать что"
- "Секреты экономии кредитов"
- "Продвинутые техники генерации"

#### Новости и обновления:
- "Новая модель [название] в Uma AI"
- "Обновления платформы"
- "Изменения в ценах и планах"

#### Кейсы и примеры:
- "Как мы создали [проект] с помощью ИИ"
- "Лучшие работы пользователей"
- "Разбор успешных промптов"

---

## Быстрый чеклист для публикации

- [ ] Заголовки на русском и английском
- [ ] Описания на двух языках
- [ ] Контент на русском и английском
- [ ] Подходящее изображение из `/public/`
- [ ] Релевантные теги
- [ ] Время чтения указано
- [ ] Статья сохранена
- [ ] Публикация включена
- [ ] Проверка в блоге

Готово! Ваша статья опубликована и доступна всем пользователям Uma AI.
