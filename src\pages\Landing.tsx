import React from 'react';

const Landing: React.FC = () => {
  return (
    <div className="landing-page">
      <div className="pricing-section">
        <h2 className="text-3xl font-bold text-center mb-12">Тарифные планы</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="pricing-card">
            <h3 className="text-2xl font-bold">Базовый</h3>
            <p className="text-4xl font-bold mt-4">300 ₽</p>
            <p className="text-gray-500 mb-6">600 кредитов</p>
            <ul className="mb-6">
              <li>✓ text2img (5 кр.)</li>
              <li>✓ img2img (10 кр.)</li>
              <li>✓ video (5с) (100 кр.)</li>
              <li>✓ video (9с) (150 кр.)</li>
              <li>✓ Сохранение истории генераций</li>
            </ul>
            <button className="w-full bg-primary text-white py-3 rounded-md hover:bg-primary/80 transition">Купить</button>
          </div>
          
          <div className="pricing-card relative overflow-hidden">
            <div className="absolute top-0 right-0 bg-primary text-white px-4 py-1 transform rotate-45 translate-x-2 -translate-y-2">
              Популярный
            </div>
            <h3 className="text-2xl font-bold">Стандартный</h3>
            <p className="text-4xl font-bold mt-4">750 ₽</p>
            <p className="text-gray-500 mb-6">1500 кредитов</p>
            <ul className="mb-6">
              <li>✓ text2img (5 кр.)</li>
              <li>✓ img2img (10 кр.)</li>
              <li>✓ video (5с) (100 кр.)</li>
              <li>✓ video (9с) (150 кр.)</li>
              <li>✓ Сохранение истории генераций</li>
            </ul>
            <button className="w-full bg-primary text-white py-3 rounded-md hover:bg-primary/80 transition">Купить</button>
          </div>
          
          <div className="pricing-card">
            <h3 className="text-2xl font-bold">Премиум</h3>
            <p className="text-4xl font-bold mt-4">2000 ₽</p>
            <p className="text-gray-500 mb-6">4000 кредитов</p>
            <ul className="mb-6">
              <li>✓ text2img (5 кр.)</li>
              <li>✓ img2img (10 кр.)</li>
              <li>✓ video (5с) (100 кр.)</li>
              <li>✓ video (9с) (150 кр.)</li>
              <li>✓ Приоритетная поддержка</li>
            </ul>
            <button className="w-full bg-primary text-white py-3 rounded-md hover:bg-primary/80 transition">Купить</button>
          </div>
        </div>
      </div>

      <div className="footer-section">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">UMA AI</h3>
              <p className="text-gray-600">
                Мощный инструмент генерации изображений и видео на базе искусственного интеллекта.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Контакты</h3>
              <p className="text-gray-600">Телефон: +79509540097</p>
              <p className="text-gray-600">Email: <EMAIL></p>
              <p className="text-gray-600">Самозанятый Богдан Евгеньевич Друковский</p>
              <p className="text-gray-600">ИНН: 550147543516</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Документы</h3>
              <ul>
                <li><a href="/terms" className="text-primary hover:underline">Условия использования</a></li>
                <li><a href="/privacy" className="text-primary hover:underline">Политика конфиденциальности</a></li>
                <li><a href="/refund" className="text-primary hover:underline">Политика возврата</a></li>
                <li><a href="/service-agreement" className="text-primary hover:underline">Договор оказания услуг</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-200 mt-8 pt-8 text-center">
            <p className="text-gray-600">© 2024 UMA AI. Все права защищены.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing; 