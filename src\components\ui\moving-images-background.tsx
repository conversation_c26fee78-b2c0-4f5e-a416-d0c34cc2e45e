
import React, { useEffect, useRef } from 'react';

// Updated image array with new images
const imageUrls = [
  "/lovable-uploads/6d004f09-51cb-4557-b299-bedd4599ee1f.png",
  "/lovable-uploads/b46b12b7-fdb7-4f21-89a8-76780fc21b7f.png",
  "/lovable-uploads/a8dd2576-b8f5-46d4-b4f9-5403e89921d7.png",
  "/lovable-uploads/b0276d6a-c757-42e3-b533-192d1fc69873.png",
  "/lovable-uploads/ff7ecd5c-6394-4ff0-96c5-6562ff549eb5.png",
  "/lovable-uploads/5a5c8708-ae5b-4d0e-9c6a-b6974d78d02f.png",
  "/lovable-uploads/27449ea4-7ed9-4a45-8756-3871c7b58d3f.png",
  "/lovable-uploads/2cf9b19b-276b-4937-a2b4-454a214c05fb.png",
  "/lovable-uploads/bed6c76e-e72c-4e5c-a3d5-80d701748513.png",
  "/lovable-uploads/f9a214bc-8cb6-424f-8543-c986392e1a04.png",
  "/lovable-uploads/d6ab8b4f-c4d1-45e5-8686-5c8ae4cc63f3.png"
];

// Function to shuffle array
const shuffleArray = (array: any[]) => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

const MovingImagesBackground = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Clear any existing content
    container.innerHTML = '';

    // Number of rows to create - increasing to fill the screen better
    const numRows = 12;
    
    // Add animation particles
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'absolute inset-0 z-0';
    
    // Create 50 particles
    for (let i = 0; i < 50; i++) {
      const particle = document.createElement('div');
      const size = Math.random() * 3 + 1;
      particle.className = 'absolute rounded-full bg-white/20';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      particle.style.opacity = `${Math.random() * 0.5}`;
      
      // Add animation
      particle.style.animation = `floating ${Math.random() * 15 + 10}s linear infinite`;
      particlesContainer.appendChild(particle);
    }
    
    container.appendChild(particlesContainer);
    
    // Create rows of moving images
    for (let i = 0; i < numRows; i++) {
      const row = document.createElement('div');
      row.className = 'flex gap-4 my-8 relative z-10';
      // Using rotateX perspective to create the tilted effect
      row.style.transform = 'perspective(800px) rotateX(30deg)';
      // Alternate direction and speed between rows
      row.style.animation = `slide${i % 2 === 0 ? 'Right' : 'Left'} ${50 + i * 10}s linear infinite`;
      // Lower opacity for background effect
      row.style.opacity = '0.15';
      
      // Shuffle images for each row to ensure different sequences
      const rowImages = shuffleArray(imageUrls);
      
      // Create more images per row to fill the screen better
      const totalImages = 20; 
      
      for (let j = 0; j < totalImages; j++) {
        const imgContainer = document.createElement('div');
        // Smaller images with rounded corners
        imgContainer.className = 'rounded-xl overflow-hidden min-w-[160px] h-[100px] shadow-lg';
        // Alternate the Y-axis rotation of each image for more dynamic effect
        imgContainer.style.transform = `rotateY(${20 * (j % 2 === 0 ? 1 : -1)}deg)`;
        
        const img = document.createElement('img');
        img.src = rowImages[j % rowImages.length];
        img.alt = 'Background image';
        img.className = 'w-full h-full object-cover';
        
        imgContainer.appendChild(img);
        row.appendChild(imgContainer);
      }
      
      container.appendChild(row);
    }

    // Add CSS animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideRight {
        0% { transform: perspective(800px) rotateX(30deg) translateX(-100%); }
        100% { transform: perspective(800px) rotateX(30deg) translateX(100%); }
      }
      
      @keyframes slideLeft {
        0% { transform: perspective(800px) rotateX(30deg) translateX(100%); }
        100% { transform: perspective(800px) rotateX(30deg) translateX(-100%); }
      }
      
      @keyframes floating {
        0% { transform: translate(0, 0); }
        50% { transform: translate(20px, 30px); }
        100% { transform: translate(0, 0); }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 0.2; }
        50% { opacity: 0.5; }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className="fixed inset-0 -z-10 overflow-hidden"
      style={{ 
        // Darker background overlay with subtle gradient
        background: 'linear-gradient(to bottom, rgba(8,8,12,0.94) 0%, rgba(10,10,14,0.97) 100%)'
      }}
    />
  );
};

export default MovingImagesBackground;
