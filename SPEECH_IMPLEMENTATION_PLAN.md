# Детальный план создания страницы Speech

## 📋 Обзор проекта

Создание новой страницы Speech с интеграцией Replicate API для генерации речи и клонирования голосов, аналогичной по стилю и функциональности страницам Image и Video.

## 🎯 Основные функции

### 1. **Переключатель режимов (Tabs)**
- **Text to Speech** - генерация речи из текста
- **Voice Cloning** - клонирование голосов

### 2. **Text to Speech функциональность**
- Поддержка моделей: `minimax/speech-02-turbo` и `minimax/speech-02-hd`
- Все параметры из API (кроме технических битрейт/канал)
- Выбор голоса из библиотеки
- Предварительный просмотр стоимости

### 3. **Voice Cloning функциональность**
- Запись голоса прямо на сайте (10 сек - 5 мин)
- Загрузка аудиофайлов (MP3, M4A, WAV)
- Предварительное прослушивание
- Автоматическое добавление в библиотеку голосов

### 4. **Библиотека голосов**
- **Дефолтные**: системные голоса API (Wise_Woman, Friendly_Person, Inspirational_girl, Deep_Voice_Man, Calm_Woman, Casual_Guy, Lively_Girl, Patient_Man, Young_Knight, Determined_Man, Lovely_Girl, Decent_Boy, Imposing_Manner, Elegant_Man, Abbess, Sweet_Girl_2, Exuberant_Girl)
- **Ваши**: клонированные пользователем голоса
- **Сообщество**: все клонированные голоса (публичные)

### 5. **История генераций**
- Список аудиозаписей пользователя
- Кнопки воспроизведения и скачивания
- Отображение даты/времени создания

## 🗂️ Структура файлов

```
src/pages/SpeechGeneration.tsx          # Основная страница
src/components/speech/
├── VoiceSelector.tsx                   # Селектор голосов
├── VoiceRecorder.tsx                   # Компонент записи
├── AudioPlayer.tsx                     # Аудиоплеер
└── SpeechHistory.tsx                   # История генераций
src/utils/
├── speechApi.ts                        # API интеграция
└── audioUtils.ts                       # Аудио утилиты
Database Tables:
├── speech_generations                  # Генерации речи
├── voice_clones                        # Клонированные голоса
└── voice_library                       # Библиотека голосов
```

## 🛠️ Техническая реализация

### 1. **Новые компоненты**

#### `src/pages/SpeechGeneration.tsx`
- Основная страница с переключателем режимов
- Интеграция всех подкомпонентов
- Управление состоянием

#### `src/components/speech/VoiceSelector.tsx`
- Модальное окно выбора голоса
- Три категории: Дефолтные, Ваши, Сообщество
- Градиентные аватары для голосов

#### `src/components/speech/VoiceRecorder.tsx`
- Запись аудио через MediaRecorder API
- Визуализация процесса записи
- Предварительное прослушивание
- Ограничения: 10 секунд - 5 минут
- Автоматическая остановка при превышении лимита

#### `src/components/speech/AudioPlayer.tsx`
- Кастомный аудиоплеер
- Кнопки воспроизведения/паузы/скачивания
- Прогресс-бар

#### `src/components/speech/SpeechHistory.tsx`
- История генераций пользователя
- Аналогично RecentGenerations

### 2. **API интеграция**

#### `src/utils/speechApi.ts`
```typescript
// Функции для работы с Replicate Speech API
export async function generateSpeech(params: SpeechParams): Promise<string>
export async function cloneVoice(params: VoiceCloneParams): Promise<string>

interface SpeechParams {
  text: string;
  voice_id: string;
  model: 'speech-02-turbo' | 'speech-02-hd';
  speed?: number;
  pitch?: number;
  volume?: number;
  emotion?: string;
  language_boost?: string;
  english_normalization?: boolean;
}

interface VoiceCloneParams {
  voice_file: string;
  model: 'speech-02-turbo' | 'speech-02-hd';
  accuracy?: number;
  need_noise_reduction?: boolean;
  need_volume_normalization?: boolean;
}
```

#### `src/utils/audioUtils.ts`
```typescript
// Утилиты для работы с аудио
export function recordAudio(): Promise<Blob>
export function validateAudioFile(file: File): boolean
export function convertToWav(blob: Blob): Promise<Blob>
export function generateGradientAvatar(name: string): string
```

### 3. **База данных**

#### Новые таблицы Supabase:

**speech_generations**
```sql
CREATE TABLE speech_generations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  text TEXT NOT NULL,
  audio_url TEXT NOT NULL,
  voice_id TEXT NOT NULL,
  voice_name TEXT,
  model TEXT NOT NULL,
  parameters JSONB,
  cost INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  public BOOLEAN DEFAULT false
);
```

**voice_clones**
```sql
CREATE TABLE voice_clones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  name TEXT NOT NULL,
  voice_id TEXT UNIQUE NOT NULL,
  audio_url TEXT NOT NULL,
  model TEXT NOT NULL,
  accuracy FLOAT,
  created_at TIMESTAMP DEFAULT NOW(),
  public BOOLEAN DEFAULT true
);
```

**voice_library**
```sql
CREATE TABLE voice_library (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voice_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'system', 'user', 'community'
  creator_id UUID REFERENCES profiles(id),
  avatar_gradient TEXT, -- CSS градиент
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. **Ценообразование**

#### Логика расчета стоимости:
```typescript
function calculateSpeechCost(text: string, model: string): number {
  const charCount = text.length;
  const baseRate = model === 'speech-02-turbo' ? 10 : 17; // токенов на 1M символов
  const calculatedCost = Math.ceil((charCount * baseRate) / 1000000);
  return Math.max(calculatedCost, 5); // минимум 5 токенов
}

const VOICE_CLONE_COST = 900; // токенов за клонирование
```

### 5. **Локализация**

#### Добавления в `src/locales/ru/translation.json`:
```json
{
  "speechGen": {
    "title": "Генерация речи",
    "textToSpeech": "Текст в речь",
    "voiceCloning": "Клонирование голоса",
    "selectVoice": "Выбрать голос",
    "recordVoice": "Записать голос",
    "uploadAudio": "Загрузить аудио",
    "generateSpeech": "Сгенерировать речь",
    "cloneVoice": "Клонировать голос",
    "textPlaceholder": "Введите текст для генерации речи...",
    "voiceLibrary": {
      "title": "Библиотека голосов",
      "default": "Дефолтные",
      "yours": "Ваши",
      "community": "Сообщество"
    },
    "parameters": {
      "model": "Модель",
      "speed": "Скорость",
      "pitch": "Высота тона",
      "volume": "Громкость",
      "emotion": "Эмоция",
      "languageBoost": "Усиление языка",
      "englishNormalization": "Нормализация английского"
    },
    "emotions": {
      "auto": "Автоматически",
      "neutral": "Нейтральная",
      "happy": "Радостная",
      "sad": "Грустная",
      "angry": "Злая",
      "fearful": "Испуганная",
      "disgusted": "Отвращение",
      "surprised": "Удивленная"
    },
    "recording": {
      "start": "Начать запись",
      "stop": "Остановить запись",
      "recording": "Запись...",
      "duration": "Длительность: {{duration}}",
      "maxDuration": "Максимум 5 минут",
      "minDuration": "Минимум 10 секунд",
      "preview": "Предварительное прослушивание",
      "rerecord": "Перезаписать"
    },
    "cost": "Стоимость: {{cost}} токенов",
    "minCost": "Минимальная стоимость: 5 токенов",
    "cloneCost": "Стоимость клонирования: 900 токенов",
    "history": {
      "title": "История речи",
      "empty": "Нет сгенерированных записей",
      "play": "Воспроизвести",
      "download": "Скачать"
    },
    "errors": {
      "noText": "Введите текст для генерации",
      "noVoice": "Выберите голос",
      "noRecording": "Сделайте запись голоса",
      "recordingTooShort": "Запись слишком короткая (минимум 10 секунд)",
      "recordingTooLong": "Запись слишком длинная (максимум 5 минут)",
      "invalidFile": "Неподдерживаемый формат файла",
      "fileTooLarge": "Файл слишком большой (максимум 20MB)",
      "microphoneAccess": "Нет доступа к микрофону",
      "generation": "Ошибка генерации речи",
      "cloning": "Ошибка клонирования голоса"
    },
    "success": {
      "generated": "Речь успешно сгенерирована",
      "cloned": "Голос успешно клонирован"
    }
  },
  "sidebar": {
    "speech": "Речь"
  }
}
```

#### Добавления в `src/locales/en/translation.json`:
```json
{
  "speechGen": {
    "title": "Speech Generation",
    "textToSpeech": "Text to Speech",
    "voiceCloning": "Voice Cloning",
    "selectVoice": "Select Voice",
    "recordVoice": "Record Voice",
    "uploadAudio": "Upload Audio",
    "generateSpeech": "Generate Speech",
    "cloneVoice": "Clone Voice",
    "textPlaceholder": "Enter text to generate speech...",
    "voiceLibrary": {
      "title": "Voice Library",
      "default": "Default",
      "yours": "Yours",
      "community": "Community"
    },
    "parameters": {
      "model": "Model",
      "speed": "Speed",
      "pitch": "Pitch",
      "volume": "Volume",
      "emotion": "Emotion",
      "languageBoost": "Language Boost",
      "englishNormalization": "English Normalization"
    },
    "emotions": {
      "auto": "Auto",
      "neutral": "Neutral",
      "happy": "Happy",
      "sad": "Sad",
      "angry": "Angry",
      "fearful": "Fearful",
      "disgusted": "Disgusted",
      "surprised": "Surprised"
    },
    "recording": {
      "start": "Start Recording",
      "stop": "Stop Recording",
      "recording": "Recording...",
      "duration": "Duration: {{duration}}",
      "maxDuration": "Maximum 5 minutes",
      "minDuration": "Minimum 10 seconds",
      "preview": "Preview",
      "rerecord": "Re-record"
    },
    "cost": "Cost: {{cost}} tokens",
    "minCost": "Minimum cost: 5 tokens",
    "cloneCost": "Cloning cost: 900 tokens",
    "history": {
      "title": "Speech History",
      "empty": "No generated recordings",
      "play": "Play",
      "download": "Download"
    },
    "errors": {
      "noText": "Enter text to generate",
      "noVoice": "Select a voice",
      "noRecording": "Make a voice recording",
      "recordingTooShort": "Recording too short (minimum 10 seconds)",
      "recordingTooLong": "Recording too long (maximum 5 minutes)",
      "invalidFile": "Unsupported file format",
      "fileTooLarge": "File too large (maximum 20MB)",
      "microphoneAccess": "No microphone access",
      "generation": "Speech generation error",
      "cloning": "Voice cloning error"
    },
    "success": {
      "generated": "Speech generated successfully",
      "cloned": "Voice cloned successfully"
    }
  },
  "sidebar": {
    "speech": "Speech"
  }
}
```

### 6. **Навигация**

#### Обновление `src/components/layout/Sidebar.tsx`:
```typescript
import { Mic } from 'lucide-react'; // Добавить импорт

const getSidebarLinks = (t: (key: string) => string) => [
  { icon: Home, label: t('sidebar.home'), path: '/dashboard' },
  { icon: History, label: t('sidebar.history'), path: '/history' },
  { icon: MessageSquare, label: t('sidebar.text'), path: '/text' },
  { icon: Image, label: t('sidebar.image'), path: '/image' },
  { icon: Video, label: t('sidebar.video'), path: '/video' },
  { icon: Mic, label: t('sidebar.speech'), path: '/speech' }, // НОВОЕ
];
```

#### Обновление `src/App.tsx`:
```typescript
import SpeechGeneration from "./pages/SpeechGeneration"; // Добавить импорт

// В Routes добавить:
<Route path="/speech" element={<ProtectedRoute><SpeechGeneration /></ProtectedRoute>} />
```

## 🎨 UI/UX дизайн

### 1. **Стилистическая идентичность**
- Использование тех же компонентов UI что и в Image/Video
- Аналогичная структура layout с боковой историей
- Те же цвета, шрифты, анимации
- Компонент DottedBackground для фона

### 2. **Уникальные элементы**
- **Аудиоплеер**: кастомный дизайн с волновой визуализацией
- **Селектор голосов**: модальное окно с градиентными аватарами
- **Рекордер**: анимированная кнопка записи с таймером
- **Градиентные аватары**: уникальные градиенты для каждого голоса

### 3. **Адаптивность**
- Мобильная версия с упрощенным интерфейсом
- Responsive дизайн для всех компонентов
- Скрытие боковой истории на мобильных устройствах

## 📱 Пользовательские сценарии

### Сценарий 1: Генерация речи
1. Пользователь выбирает "Text to Speech"
2. Вводит текст (показывается стоимость в реальном времени)
3. Выбирает голос из библиотеки
4. Настраивает параметры (скорость, тон, эмоция)
5. Нажимает "Сгенерировать речь"
6. Получает аудиофайл с возможностью прослушивания/скачивания

### Сценарий 2: Клонирование голоса
1. Пользователь выбирает "Voice Cloning"
2. Записывает голос или загружает файл
3. Прослушивает запись, при необходимости перезаписывает
4. Вводит название голоса
5. Нажимает "Клонировать голос" (900 токенов)
6. Голос появляется в библиотеке и доступен для использования

## 🔄 Интеграция с существующей системой

### 1. **Кредитная система**
- Интеграция с `getUserCredits()` и `updateUserCredits()`
- Проверка баланса перед генерацией
- Автоматическое списание токенов
- Отображение баланса в реальном времени

### 2. **Система уведомлений**
- Использование существующих toast уведомлений
- Обработка ошибок API
- Уведомления о успешных операциях

### 3. **Аутентификация**
- Использование существующего AuthContext
- Локальное сохранение для неавторизованных пользователей
- Синхронизация с сервером при входе

### 4. **Компоненты UI**
- Использование существующих компонентов из `src/components/ui/`
- Tabs, Button, Select, Dialog, etc.
- Соблюдение единого стиля

## 🚀 План реализации

### Этап 1: Базовая структура (1-2 дня)
1. ✅ Создание основной страницы SpeechGeneration
2. ✅ Настройка роутинга и навигации
3. ✅ Создание базовых компонентов
4. ✅ Добавление переводов

### Этап 2: API интеграция (2-3 дня)
1. ✅ Реализация speechApi.ts
2. ✅ Интеграция с Replicate API
3. ✅ Обработка ошибок и валидация
4. ✅ Система ценообразования

### Этап 3: Аудио функциональность (2-3 дня)
1. ✅ Компонент записи голоса
2. ✅ Аудиоплеер с кастомным дизайном
3. ✅ Обработка аудиофайлов
4. ✅ Валидация и конвертация

### Этап 4: База данных (1-2 дня)
1. ✅ Создание таблиц Supabase
2. ✅ Функции для работы с БД
3. ✅ Миграции и тестирование
4. ✅ Интеграция с существующей системой

### Этап 5: UI/UX полировка (2-3 дня)
1. ✅ Библиотека голосов с модальным окном
2. ✅ История генераций
3. ✅ Адаптивный дизайн
4. ✅ Градиентные аватары

### Этап 6: Тестирование и оптимизация (1-2 дня)
1. ✅ Тестирование всех функций
2. ✅ Оптимизация производительности
3. ✅ Исправление багов
4. ✅ Финальная полировка

**Общее время реализации: 9-15 дней**

## 📊 Метрики успеха

- ✅ Полная функциональность генерации речи
- ✅ Работающее клонирование голосов
- ✅ Интуитивный пользовательский интерфейс
- ✅ Стабильная работа на всех устройствах
- ✅ Корректная интеграция с существующей системой
- ✅ Соответствие дизайн-системе проекта

## 🔧 Технические детали

### Системные голоса API
```typescript
const SYSTEM_VOICES = [
  'Wise_Woman', 'Friendly_Person', 'Inspirational_girl', 'Deep_Voice_Man',
  'Calm_Woman', 'Casual_Guy', 'Lively_Girl', 'Patient_Man', 'Young_Knight',
  'Determined_Man', 'Lovely_Girl', 'Decent_Boy', 'Imposing_Manner',
  'Elegant_Man', 'Abbess', 'Sweet_Girl_2', 'Exuberant_Girl'
];
```

### Поддерживаемые форматы аудио
- **Входные**: MP3, M4A, WAV
- **Выходные**: MP3 (по умолчанию)
- **Ограничения**: 10 секунд - 5 минут, максимум 20MB

### Параметры генерации речи
- **text**: строка (обязательно, максимум 5000 символов)
- **voice_id**: строка (обязательно)
- **speed**: число (0.5-2.0, по умолчанию 1.0)
- **pitch**: число (-12 до 12, по умолчанию 0)
- **volume**: число (0-10, по умолчанию 1.0)
- **emotion**: строка (auto, neutral, happy, sad, angry, fearful, disgusted, surprised)
- **language_boost**: строка (None, Automatic, Russian, English, etc.)
- **english_normalization**: булево (по умолчанию false)

Этот план обеспечивает создание полнофункциональной страницы Speech, которая органично впишется в существующую экосистему Uma AI и предоставит пользователям мощные инструменты для работы с аудио-контентом.