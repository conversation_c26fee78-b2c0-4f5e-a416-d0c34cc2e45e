// Интерфейс для параметров создания платежа
interface CreatePaymentParams {
  amount: number;
  currency?: string;
  description?: string;
  userId: string;
  creditsAmount: number;
  returnUrl: string;
  email: string;
}

// Интерфейс для ответа при создании платежа
interface PaymentResponse {
  id: string;
  status: string;
  amount: {
    value: string;
    currency: string;
  };
  confirmation: {
    type: string;
    confirmation_url?: string;
  };
  created_at: string;
  description?: string;
  metadata?: {
    userId?: string;
    creditsAmount?: number;
  };
}

/**
 * Создание платежа в ЮKassa через бессерверную функцию
 */
export const createPayment = async ({
  amount,
  currency = 'RUB',
  description = 'Покупка кредитов UMA.AI',
  userId,
  creditsAmount,
  returnUrl,
  email
}: CreatePaymentParams): Promise<PaymentResponse> => {
  try {
    // Проверка параметров
    if (!amount || amount <= 0) {
      throw new Error('Некорректная сумма платежа');
    }
    if (!userId) {
      throw new Error('Не указан ID пользователя');
    }
    if (!creditsAmount || creditsAmount <= 0) {
      throw new Error('Некорректное количество кредитов');
    }
    if (!returnUrl) {
      throw new Error('Не указан URL для возврата после оплаты');
    }

    // Формируем данные для запроса
    const paymentData = {
      amount,
      currency,
      description,
      userId,
      creditsAmount,
      returnUrl,
      email
    };
    
    // Отправляем запрос через бессерверную функцию
    console.log(`Отправка запроса на создание платежа через бессерверную функцию`, paymentData);
    const response = await fetch('/api/yukassa/payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paymentData)
    });

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const responseText = await response.text();
      console.error('Ожидался JSON, получен:', responseText.substring(0, 200) + '...');
      throw new Error(`Ожидался JSON, получен текст: ${responseText.substring(0, 200)}`);
    }
    
    // Получаем ответ в JSON
    const data = await response.json();
    
    if (!response.ok) {
      console.error('Ошибка сервера при создании платежа:', data);
      throw new Error(`Ошибка при создании платежа: ${JSON.stringify(data.error || data)}`);
    }
    
    console.log('Успешно создан платеж:', data);
    return data;
  } catch (error) {
    console.error('Ошибка при создании платежа:', error);
    throw error;
  }
};

/**
 * Получение информации о платеже через бессерверную функцию
 */
export const getPaymentInfo = async (paymentId: string): Promise<PaymentResponse> => {
  try {
    // Отправляем запрос через бессерверную функцию
    const response = await fetch(`/api/yukassa/payment/${paymentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Ошибка при получении информации о платеже: ${JSON.stringify(errorData)}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Ошибка при получении информации о платеже:', error);
    throw error;
  }
};

// Интерфейс для кредитного плана
export interface CreditPlan {
  id: string;
  name: string;
  description: string;
  amount: number; // сумма в рублях
  credits: number; // количество кредитов
  isTestPlan?: boolean; // отметка тестового плана
  isCustomPlan?: boolean; // отметка плана с произвольной суммой
}

// Доступные планы покупки кредитов
export const creditPlans: CreditPlan[] = [
  {
    id: 'basic',
    name: 'Базовый',
    description: 'Пакет кредитов для начинающих',
    amount: 300,
    credits: 600
  },
  {
    id: 'standard',
    name: 'Стандартный',
    description: 'Самый популярный выбор',
    amount: 750,
    credits: 1500
  },
  {
    id: 'premium',
    name: 'Премиум',
    description: 'Для активных пользователей',
    amount: 2000,
    credits: 4000
  },
  {
    id: 'custom',
    name: 'Ультра (Свободный)',
    description: 'Выберите любую сумму от 2000₽ до 100000₽',
    amount: 2000, // минимальная сумма
    credits: 4000, // будет рассчитываться динамически
    isCustomPlan: true
  }
];

/**
 * Имитация успешного платежа для тестового плана
 */
export const simulateSuccessfulPayment = async (userId: string, creditsAmount: number): Promise<boolean> => {
  try {
    // Импортируем функцию для обновления кредитов пользователя
    const { updateUserCredits } = await import('./database');
    
    // Начисляем кредиты пользователю напрямую для тестового плана
    await updateUserCredits(
      userId, 
      creditsAmount, 
      'test_payment', 
      `Тестовая покупка ${creditsAmount} кредитов`
    );
    
    return true;
  } catch (error) {
    console.error('Ошибка при имитации успешного платежа:', error);
    return false;
  }
};

/**
 * Обработка успешного платежа - начисление кредитов пользователю
 */
export const processSuccessfulPayment = async (
  paymentId: string
): Promise<{ success: boolean; userId?: string; creditsAmount?: number }> => {
  try {
    // Используем новый API endpoint для проверки и обработки платежа
    const response = await fetch(`/api/yukassa/payment/check?paymentId=${paymentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // Добавляем проверку типа контента ответа
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Ошибка: Ответ сервера не в формате JSON', { 
        status: response.status, 
        contentType 
      });
      
      // Пытаемся прочитать текст ответа для диагностики
      const responseText = await response.text();
      console.error('Текст ответа сервера:', responseText.substring(0, 200) + '...');
      
      return { success: false };
    }
    
    // Теперь парсим JSON, когда уверены в типе контента
    const result = await response.json();
    
    if (!response.ok) {
      console.error('Ошибка сервера при обработке платежа:', result);
      return { success: false };
    }
    
    // Проверяем результат обработки
    if (!result.success) {
      console.error('Ошибка при обработке платежа:', result.message);
      return { success: false };
    }
    
    // Возвращаем успешный результат
    return {
      success: true,
      userId: result.userId,
      creditsAmount: result.creditsAmount
    };
  } catch (error) {
    console.error('Ошибка при обработке успешного платежа:', error);
    return { success: false };
  }
};
