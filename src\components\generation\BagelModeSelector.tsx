import React from 'react';
import { Button } from '@/components/ui/button';
import { Wand2, Edit3 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface BagelModeSelectorProps {
  mode: 'text2img' | 'edit';
  onChange: (mode: 'text2img' | 'edit') => void;
  className?: string;
}

const BagelModeSelector: React.FC<BagelModeSelectorProps> = ({
  mode,
  onChange,
  className = ""
}) => {
  const { t } = useTranslation();

  return (
    <div className={`space-y-3 ${className}`}>
      <label className="text-sm font-medium text-foreground">
        {t('imageGen.bagel.modeTitle')}
      </label>
      
      <div className="grid grid-cols-2 gap-3">
        <Button
          variant={mode === 'text2img' ? 'default' : 'outline'}
          className="h-auto p-3 sm:p-4 flex flex-col items-center gap-2"
          onClick={() => onChange('text2img')}
        >
          <Wand2 size={18} className="sm:w-5 sm:h-5" />
          <div className="text-center">
            <div className="font-medium text-sm sm:text-base">{t('imageGen.bagel.modes.text2img')}</div>
            <div className="text-xs text-muted-foreground mt-1 hidden sm:block">
              {t('imageGen.bagel.modes.text2imgDesc')}
            </div>
          </div>
        </Button>

        <Button
          variant={mode === 'edit' ? 'default' : 'outline'}
          className="h-auto p-3 sm:p-4 flex flex-col items-center gap-2"
          onClick={() => onChange('edit')}
        >
          <Edit3 size={18} className="sm:w-5 sm:h-5" />
          <div className="text-center">
            <div className="font-medium text-sm sm:text-base">{t('imageGen.bagel.modes.edit')}</div>
            <div className="text-xs text-muted-foreground mt-1 hidden sm:block">
              {t('imageGen.bagel.modes.editDesc')}
            </div>
          </div>
        </Button>
      </div>
    </div>
  );
};

export default BagelModeSelector;