/**
 * Скрипт для выполнения в консоли Supabase SQL для проверки и добавления
 * колонки updated_at в таблицу profiles, если она отсутствует
 */

// Сначала проверим существует ли колонка
const checkColumnQuery = `
SELECT EXISTS (
  SELECT 1 
  FROM information_schema.columns 
  WHERE table_name = 'profiles' 
    AND column_name = 'updated_at'
) as column_exists;
`;

// Запрос для добавления колонки
const addColumnQuery = `
ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
`;

// Запрос для обновления существующих записей
const updateExistingQuery = `
UPDATE profiles
SET updated_at = created_at
WHERE updated_at IS NULL;
`;

// Запрос для добавления триггера автообновления
const createTriggerQuery = `
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
`;

// Для выполнения в консоли Supabase:
// 1. Проверьте существование колонки с помощью: 
//    SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'updated_at') as column_exists;
// 
// 2. Если колонка не существует (результат: column_exists = false), выполните:
//    ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
//
// 3. Обновите существующие записи:
//    UPDATE profiles SET updated_at = created_at WHERE updated_at IS NULL;
//
// 4. Создайте триггер для автоматического обновления:
//    CREATE OR REPLACE FUNCTION update_updated_at_column()
//    RETURNS TRIGGER AS $$
//    BEGIN
//        NEW.updated_at = NOW();
//        RETURN NEW;
//    END;
//    $$ LANGUAGE plpgsql;
//
//    CREATE TRIGGER set_updated_at
//    BEFORE UPDATE ON profiles
//    FOR EACH ROW
//    EXECUTE FUNCTION update_updated_at_column(); 