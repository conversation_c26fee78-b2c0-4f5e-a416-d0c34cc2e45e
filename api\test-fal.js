// Простой тестовый endpoint для проверки API
export default async function handler(req, res) {
  console.log(`[TEST FAL] ${req.method} request received`);
  console.log('[TEST FAL] Request body:', JSON.stringify(req.body, null, 2));
  console.log('[TEST FAL] Request headers:', JSON.stringify(req.headers, null, 2));

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    return res.status(200).json({
      success: true,
      message: 'Test endpoint working',
      receivedData: req.body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[TEST FAL] Error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
