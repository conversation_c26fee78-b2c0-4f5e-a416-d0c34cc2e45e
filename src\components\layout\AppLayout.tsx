
import React from 'react';
import Sidebar from './Sidebar';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
  className?: string;
}

const AppLayout = ({ children, showSidebar = true, className }: AppLayoutProps) => {
  return (
    <div className="min-h-screen bg-background text-foreground">
      {showSidebar && <Sidebar />}
      <main className={cn(
        "min-h-screen transition-all duration-300 ease-in-out",
        showSidebar ? "sm:ml-[240px] ml-0" : "ml-0",
        className
      )}>
        {children}
      </main>
    </div>
  );
};

export default AppLayout;
