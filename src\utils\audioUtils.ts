// Утилиты для работы с аудио

/**
 * Интерфейс для записи аудио
 */
export interface AudioRecording {
  blob: Blob;
  duration: number;
  url: string;
}

/**
 * Класс для записи аудио с микрофона
 */
export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private startTime: number = 0;
  private maxDuration: number = 5 * 60 * 1000; // 5 минут в миллисекундах
  private minDuration: number = 10 * 1000; // 10 секунд в миллисекундах

  /**
   * Запрос доступа к микрофону
   */
  async requestMicrophoneAccess(): Promise<MediaStream> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });
      this.stream = stream;
      return stream;
    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw new Error('Не удалось получить доступ к микрофону');
    }
  }

  /**
   * Начать запись
   */
  async startRecording(): Promise<void> {
    if (!this.stream) {
      await this.requestMicrophoneAccess();
    }

    if (!this.stream) {
      throw new Error('Нет доступа к микрофону');
    }

    this.audioChunks = [];
    this.mediaRecorder = new MediaRecorder(this.stream, {
      mimeType: this.getSupportedMimeType()
    });

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };

    this.startTime = Date.now();
    this.mediaRecorder.start(100); // Собирать данные каждые 100мс

    // Автоматическая остановка при превышении максимального времени
    setTimeout(() => {
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
        this.stopRecording();
      }
    }, this.maxDuration);
  }

  /**
   * Остановить запись
   */
  async stopRecording(): Promise<AudioRecording> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
        reject(new Error('Запись не активна'));
        return;
      }

      const duration = Date.now() - this.startTime;

      if (duration < this.minDuration) {
        reject(new Error(`Запись слишком короткая. Минимум ${this.minDuration / 1000} секунд`));
        return;
      }

      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.audioChunks, { 
          type: this.getSupportedMimeType() 
        });
        const url = URL.createObjectURL(blob);
        
        resolve({
          blob,
          duration,
          url
        });
      };

      this.mediaRecorder.stop();
    });
  }

  /**
   * Получить текущую длительность записи
   */
  getCurrentDuration(): number {
    if (!this.startTime) return 0;
    return Date.now() - this.startTime;
  }

  /**
   * Проверить, идет ли запись
   */
  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording';
  }

  /**
   * Остановить поток микрофона
   */
  cleanup(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
  }

  /**
   * Получить поддерживаемый MIME тип
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // fallback
  }
}

/**
 * Валидация аудиофайла
 */
export function validateAudioFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 20 * 1024 * 1024; // 20MB
  const supportedTypes = ['audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/m4a', 'audio/mp4'];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Файл слишком большой. Максимальный размер: 20MB'
    };
  }

  if (!supportedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Неподдерживаемый формат файла. Поддерживаются: MP3, WAV, M4A'
    };
  }

  return { valid: true };
}

/**
 * Получение длительности аудиофайла
 */
export function getAudioDuration(file: File | Blob): Promise<number> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(file);

    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration * 1000); // возвращаем в миллисекундах
    });

    audio.addEventListener('error', () => {
      URL.revokeObjectURL(url);
      reject(new Error('Не удалось загрузить аудиофайл'));
    });

    audio.src = url;
  });
}

/**
 * Конвертация аудио в WAV формат (если нужно)
 */
export async function convertToWav(blob: Blob): Promise<Blob> {
  // Для простоты пока возвращаем исходный blob
  // В будущем можно добавить конвертацию через Web Audio API
  return blob;
}

/**
 * Форматирование времени в читаемый вид
 */
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${remainingSeconds}s`;
}

/**
 * Генерация градиентного аватара для голоса
 */
export function generateGradientAvatar(name: string): string {
  // Создаем хеш из имени для консистентности
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Конвертируем в 32-битное число
  }

  // Массив красивых градиентов
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%)',
    'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)',
    'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
    'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
    'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)',
    'linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%)',
    'linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%)'
  ];

  // Выбираем градиент на основе хеша
  const index = Math.abs(hash) % gradients.length;
  return gradients[index];
}

/**
 * Создание аудио элемента с обработкой ошибок
 */
export function createAudioElement(src: string): Promise<HTMLAudioElement> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    
    audio.addEventListener('canplaythrough', () => {
      resolve(audio);
    });

    audio.addEventListener('error', () => {
      reject(new Error('Не удалось загрузить аудиофайл'));
    });

    audio.src = src;
    audio.preload = 'metadata';
  });
}

/**
 * Проверка поддержки Web Audio API
 */
export function isWebAudioSupported(): boolean {
  return !!(window.AudioContext || (window as any).webkitAudioContext);
}

/**
 * Проверка поддержки MediaRecorder API
 */
export function isMediaRecorderSupported(): boolean {
  return !!window.MediaRecorder;
}

/**
 * Получение информации о поддерживаемых аудио форматах
 */
export function getSupportedAudioFormats(): string[] {
  const audio = document.createElement('audio');
  const formats: string[] = [];

  const testFormats = [
    { ext: 'mp3', type: 'audio/mpeg' },
    { ext: 'wav', type: 'audio/wav' },
    { ext: 'm4a', type: 'audio/mp4' },
    { ext: 'ogg', type: 'audio/ogg' },
    { ext: 'webm', type: 'audio/webm' }
  ];

  testFormats.forEach(format => {
    if (audio.canPlayType(format.type)) {
      formats.push(format.ext);
    }
  });

  return formats;
}