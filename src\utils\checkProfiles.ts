import { supabase } from './supabase';
import { getUserCredits } from './database';

async function checkUserProfiles() {
  // Get current user session
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    console.log('No authenticated user found');
    return;
  }

  // Check if profile exists in database
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('id, email, credits')
    .eq('id', user.id)
    .single();

  if (error) {
    console.error('Error fetching profile:', error);
    return;
  }

  console.log('User profile:', profile);

  // Check credits via getUserCredits
  const credits = await getUserCredits(user.id);
  console.log('Credits from getUserCredits:', credits);
}

checkUserProfiles()
  .then(() => console.log('Profile check complete'))
  .catch(err => console.error('Error checking profile:', err));
