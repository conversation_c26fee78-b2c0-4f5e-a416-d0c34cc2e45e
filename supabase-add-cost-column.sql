-- Добавляет столбец cost в таблицу image_generations
-- Этот столбец необходим для сохранения стоимости генерации изображений

DO $$ 
BEGIN
  -- Проверяем существует ли столбец cost в таблице image_generations
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'image_generations' 
      AND column_name = 'cost'
  ) THEN
    -- Если столбец не существует, добавляем его
    EXECUTE 'ALTER TABLE image_generations ADD COLUMN cost INTEGER DEFAULT 0';
    RAISE NOTICE 'Столбец cost добавлен в таблицу image_generations';
  ELSE
    RAISE NOTICE 'Столбец cost уже существует в таблице image_generations';
  END IF;
END $$;

-- Обновляем все существующие записи, у которых cost равен NULL
UPDATE image_generations
SET cost = 0
WHERE cost IS NULL;

-- Добавляем комментарий к столбцу
COMMENT ON COLUMN image_generations.cost IS 'Стоимость генерации изображения в кредитах';