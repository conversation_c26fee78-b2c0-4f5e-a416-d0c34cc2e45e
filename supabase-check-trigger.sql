-- Скрипт для проверки и настройки триггера автоматического создания профилей

-- 1. Проверяем существование колонки updated_at
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
      AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Колонка updated_at добавлена в таблицу profiles';
  ELSE
    RAISE NOTICE 'Колонка updated_at существует в таблице profiles';
  END IF;
END $$;

-- 2. Удаляем существующий триггер и функцию
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- 3. Создаем функцию для автоматического создания профиля
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Проверяем существует ли уже профиль
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    -- Если профиль не существует, создаем его
    INSERT INTO public.profiles (id, email, name, credits, created_at, updated_at)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name', ''),
      10,
      NOW(),
      NOW()
    );
    RAISE NOTICE 'Профиль для пользователя % создан автоматически', NEW.id;
  ELSE
    RAISE NOTICE 'Профиль для пользователя % уже существует', NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Создаем триггер для вызова функции
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- 5. Проверяем существование триггера
SELECT
  tgname AS trigger_name,
  proname AS function_name,
  nspname AS schema_name,
  relname AS table_name
FROM pg_trigger
JOIN pg_proc ON pg_trigger.tgfoid = pg_proc.oid
JOIN pg_namespace ON pg_proc.pronamespace = pg_namespace.oid
JOIN pg_class ON pg_trigger.tgrelid = pg_class.oid
WHERE tgname = 'on_auth_user_created';

-- 6. Создаем профили для конкретных ID
DO $$
DECLARE
  user_ids UUID[] := ARRAY['512bf940-fd5a-4e90-a10c-c50f4c4aa8d3'::UUID, '085f7aba-5f49-4231-a28d-c6c520c07b5c'::UUID];
  user_id UUID;
  email_addr TEXT := '<EMAIL>';
BEGIN
  FOREACH user_id IN ARRAY user_ids
  LOOP
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = user_id) THEN
      INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
      VALUES (user_id, email_addr, 'User', 10, NOW(), NOW());
      RAISE NOTICE 'Создан профиль для пользователя: %', user_id;
    ELSE
      RAISE NOTICE 'Профиль для пользователя % уже существует', user_id;
    END IF;
  END LOOP;
END $$; 