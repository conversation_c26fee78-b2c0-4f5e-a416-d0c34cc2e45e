import React from 'react';

export default function GlowEffect() {
  return (
    <div className="w-full h-[50vh] overflow-hidden">
      {/* Static colored divs with soft visibility */}
      <div
        className="absolute bottom-0 left-[10%] w-[30%] h-[40vh]"
        style={{
          background:
            "radial-gradient(ellipse at center bottom, rgba(255, 0, 128, 0.2) 0%, rgba(255, 0, 128, 0.07) 50%, rgba(255, 0, 128, 0) 80%)",
          filter: "blur(45px)",
          opacity: 0.8,
        }}
      />

      <div
        className="absolute bottom-0 left-[25%] w-[30%] h-[45vh]"
        style={{
          background:
            "radial-gradient(ellipse at center bottom, rgba(0, 128, 255, 0.2) 0%, rgba(0, 128, 255, 0.07) 50%, rgba(0, 128, 255, 0) 80%)",
          filter: "blur(45px)",
          opacity: 0.8,
        }}
      />

      <div
        className="absolute bottom-0 left-[40%] w-[30%] h-[35vh]"
        style={{
          background:
            "radial-gradient(ellipse at center bottom, rgba(0, 255, 128, 0.2) 0%, rgba(0, 255, 128, 0.07) 50%, rgba(0, 255, 128, 0) 80%)",
          filter: "blur(45px)",
          opacity: 0.8,
        }}
      />

      <div
        className="absolute bottom-0 left-[55%] w-[30%] h-[50vh]"
        style={{
          background:
            "radial-gradient(ellipse at center bottom, rgba(128, 0, 255, 0.2) 0%, rgba(128, 0, 255, 0.07) 50%, rgba(128, 0, 255, 0) 80%)",
          filter: "blur(45px)",
          opacity: 0.8,
        }}
      />

      <div
        className="absolute bottom-0 left-[70%] w-[30%] h-[38vh]"
        style={{
          background:
            "radial-gradient(ellipse at center bottom, rgba(255, 200, 0, 0.2) 0%, rgba(255, 200, 0, 0.07) 50%, rgba(255, 200, 0, 0) 80%)",
          filter: "blur(45px)",
          opacity: 0.8,
        }}
      />
    </div>
  );
} 