import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface AspectRatio {
  value: string; // e.g., "1:1", "3:2"
  label: string;
}

interface AspectRatioSelectorProps {
  value: string; // This will be the string ratio like "1:1"
  onChange: (aspectRatioValue: string) => void;
  model?: string;
}

const aspectRatiosAll: AspectRatio[] = [
  { value: "1:1", label: "Square (1:1)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "2:3", label: "Vertical (2:3)" },
  { value: "9:16", label: "Story (9:16)" },
  { value: "3:2", label: "Classic (3:2)" },
  { value: "1:3", label: "Tall (1:3)" },
  { value: "3:1", label: "Wide (3:1)" },
  { value: "10:16", label: "10:16" },
  { value: "16:10", label: "16:10" },
  { value: "5:4", label: "5:4" }
];

const aspectRatiosFlux: AspectRatio[] = [
  { value: "1:1", label: "Square (1:1)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "2:3", label: "Vertical (2:3)" },
  { value: "9:16", label: "Story (9:16)" },
  { value: "3:2", label: "Classic (3:2)" }
];

// MiniMax Image-01 supported aspect ratios
const aspectRatiosMinimax: AspectRatio[] = [
  { value: "1:1", label: "Square (1:1)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "9:16", label: "Story (9:16)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "3:2", label: "Classic (3:2)" },
  { value: "2:3", label: "Vertical (2:3)" }
];

// Google Imagen-4 supported aspect ratios
const aspectRatiosImagen4: AspectRatio[] = [
  { value: "1:1", label: "Square (1:1)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "9:16", label: "Story (9:16)" }
];

// ByteDance BAGEL - uses standard ratios
const aspectRatiosBagel: AspectRatio[] = [
  { value: "1:1", label: "Square (1:1)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "9:16", label: "Story (9:16)" },
  { value: "3:2", label: "Classic (3:2)" },
  { value: "2:3", label: "Vertical (2:3)" }
];

// Flux Kontext supported aspect ratios
const aspectRatiosFluxKontext: AspectRatio[] = [
  { value: "match_input_image", label: "Match Input (auto)" },
  { value: "1:1", label: "Square (1:1)" },
  { value: "16:9", label: "Widescreen (16:9)" },
  { value: "9:16", label: "Story (9:16)" },
  { value: "4:3", label: "Standard (4:3)" },
  { value: "3:4", label: "Portrait (3:4)" },
  { value: "3:2", label: "Classic (3:2)" },
  { value: "2:3", label: "Vertical (2:3)" },
  { value: "4:5", label: "4:5" },
  { value: "5:4", label: "5:4" },
  { value: "21:9", label: "Ultra Wide (21:9)" },
  { value: "9:21", label: "Ultra Tall (9:21)" },
  { value: "2:1", label: "Wide (2:1)" },
  { value: "1:2", label: "Tall (1:2)" }
];

const AspectRatioSelector = ({ value, onChange, model }: AspectRatioSelectorProps) => {
  let aspectRatios = aspectRatiosAll;

  // Select appropriate aspect ratios based on model
  switch (model) {
    case "together":
      aspectRatios = aspectRatiosFlux;
      break;
    case "minimax-image01":
      aspectRatios = aspectRatiosMinimax;
      break;
    case "imagen4":
      aspectRatios = aspectRatiosImagen4;
      break;
    case "bagel":
      aspectRatios = aspectRatiosBagel;
      break;
    case "flux-kontext-pro":
    case "flux-kontext-max":
      aspectRatios = aspectRatiosFluxKontext;
      break;
    case "ideogram":
    case "hidream":
    case "fluxpro":
    default:
      aspectRatios = aspectRatiosAll;
      break;
  }

  const currentLabel = aspectRatios.find(ar => ar.value === value)?.label || "Select Ratio";
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm text-foreground/70">Format:</span>
      <Select
        value={value}
        onValueChange={(val) => onChange(val)}
      >
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Select Ratio">
            {currentLabel}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {aspectRatios.map((ratio) => (
            <SelectItem key={ratio.value} value={ratio.value}>
              {ratio.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default AspectRatioSelector;