# Заголовки для всех страниц
/*
  Content-Security-Policy: default-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://ehamdaltpbuicmggxhbn.supabase.co https://umaai.site; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https://ehamdaltpbuicmggxhbn.supabase.co https://replicate.delivery https://*.replicate.delivery; connect-src 'self' https://ehamdaltpbuicmggxhbn.supabase.co wss://ehamdaltpbuicmggxhbn.supabase.co https://umaai.site https://replicate.delivery https://*.replicate.delivery; media-src 'self' blob: https://replicate.delivery https://*.replicate.delivery https://umaai.site https://umaai.site/api https://ehamdaltpbuicmggxhbn.supabase.co;
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-Robots-Tag: index, follow
  Referrer-Policy: strict-origin-when-cross-origin

# Заголовки для API
/api/*
  X-Robots-Tag: noindex, nofollow
