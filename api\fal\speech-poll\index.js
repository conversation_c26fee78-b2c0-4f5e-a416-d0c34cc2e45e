// Serverless function for background polling of Fal AI Speech API status
// This runs independently of client connection and saves results to database

import fetch from "node-fetch";
import { saveSpeechGeneration, updateUserCredits, downloadFile, uploadAudioToSupabase } from "../../utils/database.js";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const falKey = process.env.FAL_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

if (!falKey) {
  throw new Error('Missing FAL_KEY environment variable');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { requestId, model, userId, text, voice, visibility = 'public', cost } = req.body;

    if (!requestId || !model || !userId) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    console.log(`[FAL SPEECH POLL] Starting background polling for request ${requestId}`);

    // Запускаем фоновый процесс polling (не ждем его завершения)
    pollFalSpeechRequest(requestId, model, userId, text, voice, visibility, cost)
      .catch(error => {
        console.error(`[FAL SPEECH POLL] Background polling failed for ${requestId}:`, error);
      });

    // Сразу возвращаем ответ клиенту
    return res.status(200).json({
      success: true,
      message: 'Background polling started',
      requestId
    });

  } catch (error) {
    console.error('[FAL SPEECH POLL] Handler error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}

/**
 * Фоновый polling статуса Fal Speech API с сохранением в БД
 */
export async function pollFalSpeechRequest(requestId, model, userId, text, voice, visibility, cost) {
  const maxAttempts = 60; // 10 минут максимум (60 * 10 секунд)
  let attempt = 0;
  const retryDelay = 10000; // 10 секунд между проверками

  console.log(`[FAL SPEECH POLL] Background polling started for ${requestId}, max attempts: ${maxAttempts}`);

  while (attempt < maxAttempts) {
    try {
      console.log(`[FAL SPEECH POLL] Checking status (attempt ${attempt + 1}/${maxAttempts}) for ${requestId}`);

      // Проверяем статус через Fal API
      const statusResponse = await fetch(`https://queue.fal.run/${model}/requests/${requestId}/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Key ${falKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!statusResponse.ok) {
        throw new Error(`Status check failed: ${statusResponse.status}`);
      }

      const statusData = await statusResponse.json();
      console.log(`[FAL SPEECH POLL] Status for ${requestId}:`, statusData.status);

      if (statusData.status === 'COMPLETED') {
        console.log(`[FAL SPEECH POLL] Generation completed for ${requestId}, getting result...`);

        // Получаем результат
        const resultResponse = await fetch(`https://queue.fal.run/${model}/requests/${requestId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Key ${falKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!resultResponse.ok) {
          throw new Error(`Result fetch failed: ${resultResponse.status}`);
        }

        const resultData = await resultResponse.json();
        console.log(`[FAL SPEECH POLL] Got result for ${requestId}:`, !!resultData.audio);

        if (resultData.audio && resultData.audio.url) {
          // Сохраняем аудио в базу данных
          await saveSpeechToDatabase(resultData.audio.url, model, userId, text, voice, visibility, requestId);
          console.log(`[FAL SPEECH POLL] Successfully saved speech generation for ${requestId}`);
        } else {
          console.error(`[FAL SPEECH POLL] No audio URL in result for ${requestId}`);
        }

        return; // Завершаем polling
      }

      if (statusData.status === 'FAILED') {
        console.error(`[FAL SPEECH POLL] Generation failed for ${requestId}`);

        // Возвращаем кредиты пользователю
        if (cost && cost > 0) {
          try {
            await updateUserCredits(userId, cost, 'refund', `Fal Speech API Generation Failed (${model})`);
            console.log(`[FAL SPEECH POLL] Refunded ${cost} credits to user ${userId}`);
          } catch (refundError) {
            console.error(`[FAL SPEECH POLL] Error refunding credits:`, refundError);
          }
        }

        throw new Error('Speech generation failed');
      }

      // Статус все еще "IN_QUEUE" или "IN_PROGRESS", ждем и опрашиваем снова
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;

    } catch (error) {
      console.error(`[FAL SPEECH POLL] Error during polling (attempt ${attempt + 1}):`, error);

      // Если это последняя попытка, возвращаем кредиты
      if (attempt >= maxAttempts - 1) {
        if (cost && cost > 0) {
          try {
            await updateUserCredits(userId, cost, 'refund', `Fal Speech API Timeout (${model})`);
            console.log(`[FAL SPEECH POLL] Refunded ${cost} credits to user ${userId} due to timeout`);
          } catch (refundError) {
            console.error(`[FAL SPEECH POLL] Error refunding credits on timeout:`, refundError);
          }
        }
        throw error;
      }

      // Ждем перед следующей попыткой
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      attempt++;
    }
  }

  console.error(`[FAL SPEECH POLL] Timeout after ${maxAttempts} attempts for ${requestId}`);
  
  // Возвращаем кредиты при таймауте
  if (cost && cost > 0) {
    try {
      await updateUserCredits(userId, cost, 'refund', `Fal Speech API Timeout (${model})`);
      console.log(`[FAL SPEECH POLL] Refunded ${cost} credits to user ${userId} due to timeout`);
    } catch (refundError) {
      console.error(`[FAL SPEECH POLL] Error refunding credits on timeout:`, refundError);
    }
  }

  throw new Error('Timeout waiting for speech generation');
}

/**
 * Сохраняет аудио в базу данных
 */
async function saveSpeechToDatabase(audioUrl, model, userId, text, voice, visibility, falRequestId) {
  try {
    console.log(`[FAL SPEECH POLL] Saving audio to database: ${audioUrl}`);

    // Скачиваем аудио
    const audioBuffer = await downloadFile(audioUrl);
    console.log(`[FAL SPEECH POLL] Downloaded audio, size: ${audioBuffer.length} bytes`);

    // Генерируем уникальное имя файла
    const { v4: uuidv4 } = await import('uuid');
    const audioId = uuidv4();
    const timestamp = Date.now();
    const fileName = `${audioId}/${timestamp}-audio.mp3`;

    // Загружаем аудио в Supabase Storage
    const supabaseAudioUrl = await uploadAudioToSupabase(audioBuffer, fileName);
    console.log(`[FAL SPEECH POLL] Audio uploaded to Storage: ${supabaseAudioUrl}`);

    // Рассчитываем стоимость
    let cost = 5; // Минимум
    if (model.includes('turbo-v2.5')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 20));
    } else if (model.includes('multilingual-v2')) {
      cost = Math.max(5, Math.ceil((text.length / 1000) * 40));
    }

    // Сохраняем в БД
    const savedGeneration = await saveSpeechGeneration({
      user_id: userId,
      text: text || '',
      audio_url: supabaseAudioUrl,
      voice_id: voice || 'Rachel',
      voice_name: voice || 'Rachel',
      model: model,
      parameters: {
        voice: voice || 'Rachel',
        fal_request_id: falRequestId
      },
      cost: cost,
      public: visibility === 'public'
    });

    console.log(`[FAL SPEECH POLL] Speech generation saved to database with ID: ${audioId}`);

  } catch (error) {
    console.error(`[FAL SPEECH POLL] Error saving audio to database:`, error);
    throw error;
  }
}
