{"name": "enigma-genesis-server", "version": "1.0.0", "description": "Backend server for Enigma Genesis", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.2", "replicate": "^1.0.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}