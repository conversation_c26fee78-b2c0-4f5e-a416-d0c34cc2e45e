import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase, createUserProfile } from '../utils/supabase';
import { checkAndAddUpdatedAtColumn } from '../utils/database';
import { AuthError, Session, User } from '@supabase/supabase-js';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

interface AuthContextProps {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name?: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextProps | null>(null);

// Проверка email на валидность
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return Boolean(email && emailRegex.test(email.trim()));
};

// Проверка пароля на минимальную длину
const isValidPassword = (password: string): boolean => {
  return Boolean(password && password.length >= 6);
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    // Проверяем и добавляем колонку updated_at в таблицу profiles, если она отсутствует
    checkAndAddUpdatedAtColumn()
      .then(result => {
        if (result) {
          console.log('Колонка updated_at проверена и готова к использованию');
        } else {
          console.warn('Не удалось проверить/создать колонку updated_at');
        }
      })
      .catch(error => {
        console.error('Ошибка при проверке колонки updated_at:', error);
      });

    // Устанавливаем слушатель изменений авторизации
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false);

        if (event === 'SIGNED_IN' && session?.user) {
          console.log('Пользователь вошел:', session.user.email);
          
          // Создаем профиль пользователя без ожидания
          console.log('Создаем профиль пользователя:', session.user.id);
          
          // Только если это явный вход пользователя через форму логина или регистрации, 
          // перенаправляем на dashboard
          if (window.location.pathname === '/login' || 
              window.location.pathname === '/register' || 
              window.location.pathname.includes('/auth/callback')) {
            console.log('Переходим на /dashboard после явного входа');
            navigate('/dashboard');
          }
          
          // Создаем/обновляем профиль только при успешном входе
          createUserProfile(session.user)
            .then(() => {
              console.log('Профиль пользователя создан/обновлен успешно');
            })
            .catch(error => {
              console.error('Ошибка при создании профиля:', error);
              // При сетевых ошибках не блокируем вход пользователя
            });
        } else if (event === 'SIGNED_OUT') {
          console.log('Пользователь вышел, перенаправление на главную');
          navigate('/');
        }
      }
    );

    // Получаем текущую сессию при загрузке
    const getCurrentSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        setSession(data.session);
        setUser(data.session?.user ?? null);
        setIsLoading(false);

        if (data.session?.user) {
          console.log('Существующая сессия найдена:', data.session.user.email);
          
          try {
            // Обновляем профиль пользователя
            console.log('Проверяем профиль пользователя:', data.session.user.id);
            await createUserProfile(data.session.user);
          } catch (error) {
            console.error('Ошибка при проверке профиля:', error);
            // Продолжаем работу даже при ошибке профиля
          }
        }
      } catch (error) {
        console.error('Ошибка при получении сессии:', error);
        setIsLoading(false);
      }
    };
    
    getCurrentSession();

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [navigate]);

  // Обработка ошибок авторизации
  const handleAuthError = (error: AuthError | Error | unknown) => {
    console.error('Auth error:', error);
    let errorMessage = t('auth.generalError');
    let description = '';

    if (error instanceof Error) {
      // Проверяем специфические ошибки
      if (error.message.includes('Email not confirmed')) {
        errorMessage = t('auth.emailNotConfirmed');
        description = t('auth.checkEmailForConfirmation');
        
        toast.error(errorMessage, {
          description: description,
          duration: 8000
        });
        
        return;
      } else if (error.message.includes('Invalid login credentials')) {
        errorMessage = t('auth.invalidCredentials');
      } else if (error.message.includes('Email address is invalid')) {
        errorMessage = t('auth.invalidEmail');
      } else if (error.message.includes('User already registered')) {
        errorMessage = t('auth.userAlreadyExists');
        description = t('auth.tryLoginInstead');
      } else if (error.message.includes('Password should be at least')) {
        errorMessage = t('auth.passwordTooShort');
      } else {
        errorMessage = error.message;
      }
    }

    if (description) {
      toast.error(errorMessage, { description });
    } else {
      toast.error(errorMessage);
    }
  };

  // Авторизация с email и паролем
  const handleSignIn = async (email: string, password: string) => {
    setIsLoading(true);

    // Валидация входных данных
    if (!isValidEmail(email)) {
      setIsLoading(false);
      throw new Error(t('auth.invalidEmail'));
    }

    if (!isValidPassword(password)) {
      setIsLoading(false);
      throw new Error(t('auth.passwordTooShort'));
    }
    
    // Очищаем email от лишних пробелов
    const cleanEmail = email.trim();

    try {
      // Пытаемся авторизоваться
      console.log('Попытка авторизации:', cleanEmail);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: cleanEmail,
        password,
      });

      // Обрабатываем ошибки авторизации
      if (error) {
        console.log('Ошибка при входе:', error.message);
        
        // Обработка ошибки "Неверные учетные данные"
        if (error.message.includes('Invalid login credentials')) {
          console.log('Неверные учетные данные. Проверяем, возможен ли новый пользователь');
          
          // Пробуем зарегистрировать пользователя
          console.log('Пробуем автоматическую регистрацию для:', cleanEmail);
          
          try {
            const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
              email: cleanEmail,
              password,
              options: {
                data: { email: cleanEmail, email_confirmed: true },
                emailRedirectTo: `${window.location.origin}/auth/callback`
              }
            });
            
            if (signUpError) {
              console.error('Ошибка при авторегистрации:', signUpError);
              
              // Если пользователь уже существует
              if (signUpError.message.includes('already registered')) {
                console.log('Email уже зарегистрирован, возможно не подтвержден или неверный пароль');
                // Показываем сообщение о сбросе пароля или подтверждении email
                throw new Error(t('auth.userExistsOrInvalidPassword'));
              } else {
                throw signUpError;
              }
            }
            
            if (signUpData.user) {
              console.log('Пользователь успешно зарегистрирован:', signUpData.user.email);
              
              // Создаем профиль пользователя
              try {
                await createUserProfile(signUpData.user);
              } catch (profileError) {
                console.error('Ошибка при создании профиля:', profileError);
                // Продолжаем даже при ошибке создания профиля
              }
              
              // Если сессия создана сразу, входим автоматически
              if (signUpData.session) {
                setUser(signUpData.user);
                setSession(signUpData.session);
                toast.success(t('auth.registrationSuccessful'));
                navigate('/dashboard');
                setIsLoading(false);
                return;
              } else {
                // Пробуем войти сразу после регистрации
                console.log('Пробуем автоматический вход после регистрации');
                
                // Небольшая задержка перед входом
                await new Promise(resolve => setTimeout(resolve, 500));
                
                try {
                  const { data: autoLoginData, error: autoLoginError } = await supabase.auth.signInWithPassword({
                    email: cleanEmail,
                    password
                  });
                  
                  if (autoLoginError) {
                    console.error('Ошибка при автовходе после регистрации:', autoLoginError);
                    // Сообщаем о необходимости подтверждения email
                    toast.success(t('auth.registrationSuccessful'), {
                      description: t('auth.checkEmailForConfirmation'),
                      duration: 8000
                    });
                  } else if (autoLoginData.user) {
                    console.log('Автоматический вход после регистрации успешен');
                    setUser(autoLoginData.user);
                    setSession(autoLoginData.session);
                    toast.success(t('auth.registrationSuccessful'));
                    navigate('/dashboard');
                    setIsLoading(false);
                    return;
                  }
                } catch (autoLoginError) {
                  console.error('Ошибка при автоматическом входе:', autoLoginError);
                  // Сообщаем о необходимости подтверждения email
                  toast.success(t('auth.registrationSuccessful'), {
                    description: t('auth.checkEmailForConfirmation'),
                    duration: 8000
                  });
                }
              }
              setIsLoading(false);
              return;
            }
          } catch (regError) {
            if (regError instanceof Error && 
               (regError.message.includes('Email not confirmed') || 
                regError.message.includes('already registered') ||
                regError.message.includes('userExistsOrInvalidPassword'))) {
              throw regError;
            }
            // Для других ошибок возвращаемся к исходной ошибке авторизации
            console.error('Не удалось проверить/создать пользователя:', regError);
            throw error;
          }
        } else if (error.message.includes('Email not confirmed')) {
          // Прямая ошибка о неподтвержденном email
          console.log('Email не подтвержден:', cleanEmail);
          throw new Error('Email not confirmed');
        } else {
          // Любые другие ошибки авторизации
          throw error;
        }
      }

      // Успешная авторизация
      if (data.user) {
        console.log('Пользователь успешно авторизован:', data.user.email);
        setUser(data.user);
        setSession(data.session);
      toast.success(t('auth.signInSuccess'));
      navigate('/dashboard');
      }
    } catch (error) {
      console.error('Ошибка при авторизации:', error);
      handleAuthError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Регистрация нового пользователя
  const handleSignUp = async (email: string, password: string, name?: string) => {
    setIsLoading(true);

    // Валидация входных данных
    if (!isValidEmail(email)) {
      setIsLoading(false);
      throw new Error(t('auth.invalidEmail'));
    }

    if (!isValidPassword(password)) {
      setIsLoading(false);
      throw new Error(t('auth.passwordTooShort'));
    }
    
    // Очищаем email от лишних пробелов
    const cleanEmail = email.trim();

    try {
      console.log('Регистрация нового пользователя:', cleanEmail);
      const { data, error } = await supabase.auth.signUp({
        email: cleanEmail,
        password,
        options: {
          data: {
            name: name || '',
          },
        },
      });

      if (error) {
        throw error;
      }
      
      if (data.user) {
        console.log('Пользователь зарегистрирован:', data.user.email);
        
        // Пытаемся создать профиль пользователя
        try {
          console.log('Создаем профиль для нового пользователя:', data.user.id);
          await createUserProfile(data.user);
        } catch (profileError) {
          console.error('Ошибка при создании профиля:', profileError);
          // Продолжаем работу даже при ошибке создания профиля
        }
        
        toast.success(t('auth.registrationSuccessful'), {
          description: t('auth.checkEmailForConfirmation'),
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Ошибка при регистрации:', error);
      handleAuthError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Выход из системы
  const handleSignOut = async () => {
    setIsLoading(true);
    try {
      console.log('Выход из системы');
      
      // Очищаем состояние пользователя и сессии перед вызовом signOut
      setUser(null);
      setSession(null);
      
      // Очистка localStorage от данных пользователя и токенов
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('supabase.auth.expires_at');
      localStorage.removeItem('pendingPaymentId');
      
      // Вызов API для выхода
      await supabase.auth.signOut();
      
      console.log('Выход выполнен успешно');
      
      // Принудительный переход на главную страницу
      window.location.href = '/';
    } catch (error) {
      console.error('Ошибка при выходе:', error);
      handleAuthError(error);
      
      // Даже при ошибке пытаемся перенаправить пользователя
      setTimeout(() => {
        window.location.href = '/';
      }, 500);
    } finally {
      setIsLoading(false);
    }
  };

  // Сброс пароля
  const handleResetPassword = async (email: string) => {
    setIsLoading(true);

    // Валидация email
    if (!isValidEmail(email)) {
      setIsLoading(false);
      throw new Error(t('auth.invalidEmail'));
    }
    
    // Очищаем email от лишних пробелов
    const cleanEmail = email.trim();

    try {
      console.log('Запрос на сброс пароля:', cleanEmail);
      const { error } = await supabase.auth.resetPasswordForEmail(cleanEmail);

      if (error) {
        throw error;
      }

      toast.success(t('auth.resetEmailSent'));
    } catch (error) {
      console.error('Ошибка при сбросе пароля:', error);
      handleAuthError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Вход через Google
  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      console.log('Вход через Google');
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Ошибка при входе через Google:', error);
      handleAuthError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    session,
    isLoading,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    signInWithGoogle: handleGoogleSignIn,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
