-- Скрипт для создания тестового пользователя с известным паролем
-- ВНИМАНИЕ: Только для тестирования и разработки!

-- 1. Получим информацию о существующем пользователе
SELECT 
  id, 
  email, 
  email_confirmed_at,
  raw_user_meta_data
FROM auth.users
WHERE email = '<EMAIL>';

-- 2. Создадим нового тестового пользователя (если существующий не работает)
-- Пароль должен быть в формате hash

-- Если хотим создать пользователя вручную, нужно использовать pg_crypto для хеша пароля
DO $$
DECLARE
  test_email TEXT := '<EMAIL>';
  test_password TEXT := 'password123';
  user_id UUID;
BEGIN
  -- Проверяем, что такого пользователя нет
  SELECT id INTO user_id FROM auth.users WHERE email = test_email;
  
  IF user_id IS NULL THEN
    -- Создаем пользователя напрямую (только в крайнем случае, нормальный подход - использовать API Supabase)
    INSERT INTO auth.users (
      instance_id,
      id,
      aud,
      role,
      email,
      encrypted_password,
      email_confirmed_at,
      raw_app_meta_data,
      raw_user_meta_data,
      created_at,
      updated_at,
      confirmation_token
    ) VALUES (
      '00000000-0000-0000-0000-000000000000',
      gen_random_uuid(),
      'authenticated',
      'authenticated',
      test_email,
      crypt(test_password, gen_salt('bf')),
      NOW(),
      '{"provider":"email","providers":["email"]}',
      '{"name":"Test User"}',
      NOW(),
      NOW(),
      ''
    ) RETURNING id INTO user_id;
    
    -- Создаем профиль для пользователя
    INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
    VALUES (user_id, test_email, 'Test User', 20, NOW(), NOW());
    
    RAISE NOTICE 'Тестовый пользователь % создан, ID: %', test_email, user_id;
  ELSE
    RAISE NOTICE 'Пользователь % уже существует, ID: %', test_email, user_id;
  END IF;
END $$;

-- 3. Альтернативный способ - сбросить пароль для существующего пользователя
DO $$
DECLARE
  reset_email TEXT := '<EMAIL>';
  new_password TEXT := 'Password123!';
  found_id UUID;
BEGIN
  -- Получаем ID пользователя
  SELECT id INTO found_id FROM auth.users WHERE email = reset_email;
  
  IF found_id IS NOT NULL THEN
    -- Устанавливаем новый пароль и подтверждаем email
    UPDATE auth.users
    SET 
      encrypted_password = crypt(new_password, gen_salt('bf')),
      email_confirmed_at = NOW(),
      updated_at = NOW()
    WHERE id = found_id;
    
    RAISE NOTICE 'Пароль для пользователя % (ID: %) сброшен на: %', reset_email, found_id, new_password;
  ELSE
    RAISE NOTICE 'Пользователь с email % не найден', reset_email;
  END IF;
END $$;

-- 4. Настраиваем запись в auth.identities, если нужно
DO $$
DECLARE
  email_to_fix TEXT := '<EMAIL>';
  user_id UUID;
BEGIN
  -- Получаем ID пользователя
  SELECT id INTO user_id FROM auth.users WHERE email = email_to_fix;
  
  IF user_id IS NOT NULL THEN
    -- Проверяем наличие записи в auth.identities
    IF NOT EXISTS (SELECT 1 FROM auth.identities WHERE user_id = user_id) THEN
      -- Создаем запись в auth.identities
      INSERT INTO auth.identities (
        id,
        user_id,
        identity_data,
        provider,
        last_sign_in_at,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        user_id,
        jsonb_build_object('sub', user_id, 'email', email_to_fix),
        'email',
        NOW(),
        NOW(),
        NOW()
      );
      
      RAISE NOTICE 'Создана запись в auth.identities для пользователя % (ID: %)', email_to_fix, user_id;
    ELSE
      RAISE NOTICE 'Запись в auth.identities для пользователя % уже существует', email_to_fix;
    END IF;
  ELSE
    RAISE NOTICE 'Пользователь с email % не найден', email_to_fix;
  END IF;
END $$;

-- 5. Выводим информацию о пользователях после всех изменений
SELECT 
  u.id, 
  u.email, 
  u.email_confirmed_at,
  u.raw_user_meta_data,
  i.provider,
  i.identity_data
FROM auth.users u
LEFT JOIN auth.identities i ON u.id = i.user_id
ORDER BY u.created_at DESC
LIMIT 10; 