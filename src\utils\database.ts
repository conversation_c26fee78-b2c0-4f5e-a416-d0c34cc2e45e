import { supabase, downloadImage, uploadImageToSupabaseStorage, dataUriToBlob } from './supabase';
import { v4 as uuidv4 } from 'uuid';

// Получение всех публичных видео-генераций для Dashboard
export const getAllVideoGenerations = async (limit: number = 100, offset: number = 0): Promise<GenerationGroup[]> => {
  try {
    console.log(`Запрос видео генераций из базы данных (limit: ${limit}, offset: ${offset})...`);
    const { data, error } = await supabase
      .from('video_generations')
      .select('id, prompt, video_url, thumbnail_url, created_at, style, aspect_ratio, model, user_id, likes')
      .eq('public', true)
      .order('likes', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    if (error) {
      console.error('Ошибка при получении всех видео-генераций:', error);
      return [];
    }
    if (!data || data.length === 0) return [];
    return data
      .filter(item => item.video_url)
      .map(item => ({
        id: item.id,
        prompt: item.prompt,
        title: item.prompt?.split(' ').slice(0, 3).join(' ') + (item.prompt?.split(' ').length > 3 ? '...' : ''),
        images: [item.video_url],
        timestamp: new Date(item.created_at),
        style: item.style,
        aspect_ratio: item.aspect_ratio,
        model: 'video',
        user_id: item.user_id,
        thumbnail_url: item.thumbnail_url,
        likes: typeof item.likes === 'number' ? item.likes : 0,
        local: false
      }));
  } catch (error) {
    console.error('Ошибка при получении всех видео-генераций:', error);
    return [];
  }
};

interface GenerationSaveData {
  user_id: string;
  prompt: string;
  image_url: string[]; // Changed to array of strings
  style?: string;
  aspect_ratio?: string;
  model?: string;
  cost_per_image?: number; // Added for accurate credit deduction
  timestamp?: Date;
  visibility?: 'public' | 'private';
  replicate_prediction_id?: string; // Added for preventing duplicates from webhook/polling
}

interface TextGenerationSaveData {
  user_id: string;
  prompt: string;
  response: string;
  model?: string;
  images?: string[];
  timestamp?: Date;
  cost?: number; // Добавлено для стоимости генерации видео
}

export interface GenerationGroup {
  id: string;
  prompt: string;
  title: string;
  images: string[];
  timestamp: Date;
  style?: string;
  aspect_ratio?: string;
  model?: string;
  thumbnail_url?: string;
  local?: boolean;
  user_id?: string;
  likes?: number; // Добавлено для поддержки лайков
}

interface TextGeneration {
  id: string;
  prompt: string;
  response: string;
  model?: string;
  images?: string[];
  timestamp: Date;
}

// Определяем интерфейс VideoGenerationSaveData здесь, так как файл types/database.ts поврежден
export interface VideoGenerationSaveData {
  user_id: string;
  prompt: string;
  video_url: string;
  thumbnail_url?: string;
  style?: string;
  duration?: number;
  aspect_ratio?: string;
  model?: string;
  timestamp?: Date;
  cost?: number;
  generation_id?: string; // для защиты от дублей
  replicate_prediction_id?: string; // ID предсказания от Replicate для защиты от дублей
  visibility?: 'public' | 'private'; // Добавляем поле для приватности
}

interface VideoGeneration {
  id: string;
  prompt: string;
  video_url: string;
  thumbnail_url?: string;
  style?: string;
  duration?: number;
  aspect_ratio?: string;
  model?: string;
  timestamp: Date;
  local?: boolean;
}

export interface UserCredits {
  total: number;
  history: {
    id: string;
    amount: number;
    transaction_type: string;
    description: string;
    created_at: Date;
  }[];
}

// Интерфейс для генераций изображений
export interface ImageGeneration {
  id: string;
  user_id: string;
  prompt: string;
  image_urls: string[];
  model: string;
  created_at: string;
  [key: string]: any; // Для дополнительных полей
}

// Helper function to safely get time from created_at or Date object
const getSafeTime = (dateInput: any): number => {
  if (dateInput) {
    // Handles string, number, or Date object inputs
    const date = new Date(dateInput);
    const time = date.getTime();
    if (!isNaN(time)) {
      return time;
    }
  }
  // console.warn('[getSafeTime] Invalid or missing dateInput, defaulting to 0 for sort:', dateInput);
  return 0; // Default for invalid or missing dates, treats them as very old.
};

/**
 * Сохранение генерации изображения (экспортируется для ESM)
 */
export async function saveImageGeneration(data: GenerationSaveData): Promise<string> {
  const { user_id, prompt, image_url, style, aspect_ratio, model, cost_per_image = 2, visibility = 'private' } = data; // image_url is now string[], added cost_per_image
  const generationId = uuidv4();

  // console.log('Пытаемся сохранить изображение(я):', {
  //   generationId,
  //   user_id,
  //   imageUrlsCount: image_url?.length || 0, // Updated to reflect array
  //   style,
  //   aspect_ratio,
  //   model,
  //   cost_per_image
  // });

  try {
    // Если пользователь не авторизован, сохраняем локально
    if (!user_id || user_id === 'anonymous') {
      // console.log('Пользователь не авторизован, сохраняем изображение(я) локально');
      const localGenerationId = saveImageLocally(user_id || 'anonymous', prompt, image_url, style, aspect_ratio, model, visibility);
      return localGenerationId;
    }

    // Проверяем, что supabase клиент инициализирован
    if (!supabase.auth) {
      console.error('Supabase client is not properly initialized. Falling back to local save.');
      const localGenerationId = saveImageLocally(user_id, prompt, image_url, style, aspect_ratio, model, visibility);
      return localGenerationId;
    }

    // console.log('Сохраняем изображение(я) в базу данных для пользователя:', user_id);

    // Process images: download from temp URL and upload to Supabase Storage
    const processedImageUrls: string[] = [];
    if (image_url && image_url.length > 0) {
      // console.log(`[saveImageGeneration] Processing ${image_url.length} image(s) for Supabase Storage upload.`);
      for (const currentUrl of image_url) {
        try {
          // Если URL уже является Supabase Storage URL, используем его как есть
          if (typeof currentUrl === 'string' && currentUrl.includes('supabase.co/storage/v1/object/public/')) {
            processedImageUrls.push(currentUrl);
            continue;
          }

          let imageBlob: Blob;
          let originalFileNameForExt: string | undefined = undefined;

          if (typeof currentUrl === 'string' && currentUrl.startsWith('data:image')) {
            // console.log(`[saveImageGeneration] Converting data URI to Blob.`);
            imageBlob = dataUriToBlob(currentUrl);
            originalFileNameForExt = currentUrl;
            // console.log(`[saveImageGeneration] Data URI converted to Blob. Type: ${imageBlob.type}, Size: ${imageBlob.size}`);
          } else if (typeof currentUrl === 'string' && (currentUrl.startsWith('http://') || currentUrl.startsWith('https://'))) {
            // console.log(`[saveImageGeneration] Downloading image from: ${currentUrl}`);
            imageBlob = await downloadImage(currentUrl);
            originalFileNameForExt = currentUrl;
            // console.log(`[saveImageGeneration] Image downloaded. Type: ${imageBlob.type}, Size: ${imageBlob.size}`);
          } else {
            console.warn(`[saveImageGeneration] Skipping invalid URL or non-image data URI: "${String(currentUrl).substring(0,100)}..."`);
            continue;
          }

          // console.log(`[saveImageGeneration] Uploading image to Supabase Storage for user ${user_id}. Original source: ${originalFileNameForExt ? String(originalFileNameForExt).substring(0,50)+'...' : 'Blob'}`);
          const supabaseUrl = await uploadImageToSupabaseStorage(user_id, imageBlob, originalFileNameForExt);
          processedImageUrls.push(supabaseUrl);
          // console.log(`[saveImageGeneration] Successfully processed image. Supabase URL: ${supabaseUrl}`);
        } catch (processError) {
          console.error(`[saveImageGeneration] Failed to process image from ${String(currentUrl).substring(0,100)}...:`, processError);
        }
      }
      // console.log(`[saveImageGeneration] Finished processing images. ${processedImageUrls.length} image(s) ready for database.`);
    }

    if (user_id && user_id !== 'anonymous' && processedImageUrls.length === 0 && image_url && image_url.length > 0) {
        console.warn('[saveImageGeneration] No images were successfully processed for Supabase Storage. Falling back to local storage, filtering for HTTP(S) URLs.');
        const httpUrlsForLocal = (image_url || []).filter(url => typeof url === 'string' && url.startsWith('http'));
        const localGenerationId = saveImageLocally(user_id, prompt, httpUrlsForLocal, style, aspect_ratio, model, visibility);
        return localGenerationId;
    }

    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user_id)
        .single();

      if (profileError) {
        if (profileError.code === '42P01') {
          console.error('Таблица profiles не существует. Сохраняем локально.');
          const localGenerationId = saveImageLocally(user_id, prompt, image_url, style, aspect_ratio, model, visibility);
          return localGenerationId;
        }
        if (profileError.code !== 'PGRST116') { // PGRST116: "Fetched row returned no data" - expected if profile doesn't exist
          console.error('Ошибка при проверке профиля (не PGRST116):', profileError);
          const localGenerationId = saveImageLocally(user_id, prompt, image_url, style, aspect_ratio, model, visibility);
          return localGenerationId;
        }
      }

      if (!profile) {
        const { data: userData } = await supabase.auth.getUser();
        const email = userData?.user?.email || `user_${user_id.substring(0, 8)}@example.com`;
        const { error: createProfileError } = await import('./supabase').then(module =>
          module.createUserProfileLegacy(user_id, email)
        );
        if (createProfileError) {
          console.error('Ошибка при создании профиля:', createProfileError);
          const localGenerationId = saveImageLocally(user_id, prompt, image_url, style, aspect_ratio, model, visibility);
          return localGenerationId;
        }
      }

      try {
        const { error: tableCheckError } = await supabase
          .from('image_generations')
          .select('id')
          .limit(1);

        if (tableCheckError && tableCheckError.code === '42P01') {
          console.error('Таблица image_generations не существует. Сохраняем локально.');
          const localGenerationId = saveImageLocally(user_id, prompt, image_url, style, aspect_ratio, model, visibility);
          return localGenerationId;
        }

        // console.log('Отправляем запрос на сохранение изображения в базу данных:', {
        //   id: generationId,
        //   user_id,
        //   prompt: prompt.substring(0, 30) + '...',
        //   imageUrls: processedImageUrls.map(url => url.substring(0, 30) + '...'),
        //   style,
        //   aspect_ratio,
        //   model: model || 'together'
        // });

        const { error } = await supabase
          .from('image_generations')
          .insert({
            id: generationId,
            user_id,
            prompt,
            image_urls: processedImageUrls,
            style,
            aspect_ratio,
            model: model || 'together',
            created_at: new Date().toISOString(),
            public: visibility === 'public' // Используем параметр visibility для определения публичности
          });

        if (error) {
          console.error('Ошибка при сохранении генерации в базу данных:', error);
          const urlsToSaveLocally = processedImageUrls.length > 0
            ? processedImageUrls
            : (image_url || []).filter(url => typeof url === 'string' && url.startsWith('http'));
          const localGenerationId = saveImageLocally(user_id, prompt, urlsToSaveLocally, style, aspect_ratio, model, visibility);
          return localGenerationId;
        } else {
          // console.log('Изображение(я) успешно сохранено(ы) в базе данных с ID:', generationId);

          setTimeout(() => {
            const event = new CustomEvent('generationsUpdated', { detail: { userId: user_id } });
            window.dispatchEvent(event);
            // console.log('Отправлено событие generationsUpdated с задержкой');
          }, 500);

          if (processedImageUrls.length > 0) {
            const totalDeduction = cost_per_image * processedImageUrls.length;
            try {
              await updateUserCredits(user_id, -totalDeduction, 'image_generation', `Генерация ${processedImageUrls.length} изображения(й) (${model}): ${prompt.substring(0, 30)}...`);
            } catch (creditError) {
              console.error('Ошибка при списании кредитов:', creditError);
            }
          }
          return generationId;
        }
      } catch (saveError) {
        console.error('Ошибка при сохранении генерации (внутренний try-catch):', saveError);
        const httpUrlsForLocalOnError = (image_url || []).filter(url => typeof url === 'string' && url.startsWith('http'));
        const localGenerationId = saveImageLocally(user_id, prompt, httpUrlsForLocalOnError, style, aspect_ratio, model, visibility);
        return localGenerationId;
      }
    } catch (profileCheckError) {
      console.error('Ошибка при проверке/создании профиля (внешний try-catch):', profileCheckError);
      const httpUrlsForLocalOnProfileError = (image_url || []).filter(url => typeof url === 'string' && url.startsWith('http'));
      const localGenerationId = saveImageLocally(user_id, prompt, httpUrlsForLocalOnProfileError, style, aspect_ratio, model, visibility);
      return localGenerationId;
    }
  } catch (error) {
    console.error('Ошибка при сохранении генерации (основной try-catch):', error);
    const httpUrlsForLocalOnGeneralError = (image_url || []).filter(url => typeof url === 'string' && (url.startsWith('http') || url.startsWith('data:image')) );
    const finalUrlsForLocal = (user_id && user_id !== 'anonymous')
        ? httpUrlsForLocalOnGeneralError.filter(url => url.startsWith('http'))
        : httpUrlsForLocalOnGeneralError;
    const localGenerationId = saveImageLocally(user_id || 'anonymous', prompt, finalUrlsForLocal, style, aspect_ratio, model, visibility);
    return localGenerationId;
  }
};

/**
 * Функция для локального сохранения изображений, когда Supabase недоступен
 */
function saveImageLocally(user_id: string, prompt: string, image_urls_arr: string[], style?: string, aspect_ratio?: string, model?: string, visibility: 'public' | 'private' = 'private') { // Renamed image_url to image_urls_arr and type to string[]
  const generationId = uuidv4();

  try {
    const existingData = localStorage.getItem('localGenerations');
    const generations = existingData ? JSON.parse(existingData) : [];

    let validLocalImageUrls: string[] = [];
    if (image_urls_arr && Array.isArray(image_urls_arr)) {
      if (user_id && user_id !== 'anonymous') {
        validLocalImageUrls = image_urls_arr.filter(url => typeof url === 'string' && url.startsWith('http'));
        // if (validLocalImageUrls.length < image_urls_arr.length) {
        //   // console.log(`[saveImageLocally] Filtered out data URIs for authenticated user ${user_id}. Storing only HTTP(S) links.`);
        // }
      } else {
        validLocalImageUrls = image_urls_arr.filter(url => typeof url === 'string' && (url.startsWith('http') || url.startsWith('data:image')));
      }
    }

    const newGeneration = {
      id: generationId,
      user_id,
      prompt,
      image_urls: validLocalImageUrls,
      style,
      aspect_ratio,
      model: model || 'together',
      created_at: new Date().toISOString(),
      local: true,
      visibility
    };

    generations.push(newGeneration); // Add new generation first
    let limitedGenerations = generations
      .filter((g: any) => g.image_urls && g.image_urls.length > 0 && g.image_urls.every((url: string) => !!url))
      .sort((a: any, b: any) => getSafeTime(b.created_at) - getSafeTime(a.created_at)) // Sort by date descending
      .slice(0, 50); // Keep only the newest 50

    try {
      localStorage.setItem('localGenerations', JSON.stringify(limitedGenerations));
      // console.log('Изображение сохранено локально (только метаданные).');
    } catch (storageError) {
      if (storageError instanceof DOMException &&
         (storageError.name === 'QuotaExceededError' ||
          storageError.code === 22 ||
          storageError.code === 1014)) {

        console.warn('Превышена квота localStorage при сохранении в "localGenerations". Пытаемся освободить место...');

        // generations array already includes the newGeneration and is sorted
        const countToRemove = Math.max(1, Math.floor(generations.length * 0.2)); // Calculate 20% of current total
        generations.splice(generations.length - countToRemove, countToRemove); // Remove oldest 20%

        limitedGenerations = generations // Re-assign limitedGenerations after cleanup
            .filter((g: any) => g.image_urls && g.image_urls.length > 0 && g.image_urls.every((url: string) => !!url))
            .slice(0, 50); // Ensure it's still capped at 50

        // console.log(`Удалено ${countToRemove} старых генераций из "localGenerations". Пытаемся сохранить снова...`);

        try {
          localStorage.setItem('localGenerations', JSON.stringify(limitedGenerations));
          // console.log('Изображение сохранено локально в "localGenerations" после очистки старых данных.');
        } catch (retryError) {
          console.error('Не удалось сохранить в "localGenerations" даже после очистки:', retryError);
        }
      } else {
        console.error('Не удалось сохранить изображение в localStorage ("localGenerations"):', storageError);
      }
    }

    // try {
    //   const updatedData = localStorage.getItem('localGenerations');
    //   const parsedData = updatedData ? JSON.parse(updatedData) : [];
    //   // console.log(`В локальном хранилище "localGenerations" сейчас ${parsedData.length} генераций`);
    // } catch (parseError) {
    //   // console.error('Ошибка при чтении "localGenerations" после сохранения:', parseError);
    // }

    try {
      setTimeout(() => {
        const event = new CustomEvent('generationsUpdated', {
          detail: { userId: user_id }
        });
        window.dispatchEvent(event);
        // console.log('Отправлено событие generationsUpdated для локального сохранения с задержкой');
      }, 500);
    } catch (eventError) {
      console.error('Ошибка при отправке события generationsUpdated (локальное сохранение):', eventError);
    }

    return generationId;
  } catch (e) {
    console.error('Не удалось сохранить изображение локально (основной try-catch в saveImageLocally):', e);
    return generationId; // Возвращаем ID даже при ошибке, т.к. он уже сгенерирован
  }
}

// Получение из localStorage данных генераций
export const getLocalGenerations = (userId?: string): ImageGeneration[] => {
  try {
    if (!userId) return [];

    const localDataKey = 'localGenerations'; // Используем общий ключ
    const localData = localStorage.getItem(localDataKey);

    if (!localData) return [];

    const allLocalGenerations = JSON.parse(localData) as ImageGeneration[];
    // Фильтруем по userId, так как 'localGenerations' общий
    return allLocalGenerations.filter(gen => gen.user_id === userId || gen.user_id === 'anonymous');
  } catch (error) {
    console.error('Ошибка при получении локальных генераций:', error);
    return [];
  }
};

// Сохранение в localStorage генераций
export const saveLocalGenerations = (userId: string, generations: ImageGeneration[]): void => {
  try {
    if (!userId) return;

    const localDataKey = `generations_user_cache_${userId}`; // Отдельный ключ для кэша пользователя
    let existingUserCache: ImageGeneration[] = [];
    try {
      const rawCache = localStorage.getItem(localDataKey);
      if (rawCache) {
        existingUserCache = JSON.parse(rawCache);
      }
    } catch (e) {
      console.error(`Ошибка чтения кэша пользователя ${localDataKey}:`, e);
      existingUserCache = [];
    }

    // Объединяем новые и существующие, удаляем дубликаты по ID, сортируем, ограничиваем
    const combinedGenerations = [
      ...generations,
      ...existingUserCache
    ];

    const uniqueGenerationsMap = new Map<string, ImageGeneration>();
    combinedGenerations.forEach(gen => {
      if (!uniqueGenerationsMap.has(gen.id)) {
        uniqueGenerationsMap.set(gen.id, gen);
      }
    });

    const sortedUniqueGenerations = Array.from(uniqueGenerationsMap.values())
      .sort((a, b) => getSafeTime(b.created_at) - getSafeTime(a.created_at));

    const limitedGenerations = sortedUniqueGenerations.slice(0, 50);

    localStorage.setItem(localDataKey, JSON.stringify(limitedGenerations));
    // console.log(`Сохранено ${limitedGenerations.length} генераций в localStorage для пользователя ${userId} (ключ: ${localDataKey})`);

  } catch (error) {
    if (error instanceof DOMException &&
       (error.name === 'QuotaExceededError' || error.code === 22 || error.code === 1014)) {
      console.warn(`Превышена квота localStorage при сохранении в "generations_user_cache_${userId}". Попытка очистки...`);
      // При ошибке квоты здесь, можно попробовать очистить только этот конкретный кэш пользователя
      // или реализовать более глобальную стратегию очистки (например, LRU для всех кэшей)
      // Пока просто логируем и не пытаемся сохранить, чтобы избежать бесконечного цикла, если очистка не помогает.
      // Можно удалить самые старые 20% из ЭТОГО кэша и попробовать снова.
      const localDataKey = `generations_user_cache_${userId}`;
      let generationsForCleanup: ImageGeneration[] = [];
      try {
        const rawCache = localStorage.getItem(localDataKey);
        if (rawCache) generationsForCleanup = JSON.parse(rawCache);
      } catch (e) { /*ignore*/ }

      if (generationsForCleanup.length > 0) {
        generationsForCleanup.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        const countToRemove = Math.max(1, Math.floor(generationsForCleanup.length * 0.2));
        generationsForCleanup.splice(generationsForCleanup.length - countToRemove, countToRemove);

        // Попытка сохранить очищенные + новые данные (уже ограниченные)
        const mergedAndLimited = [...generations.slice(0,50), ...generationsForCleanup]
            .sort((a: any, b: any) => getSafeTime(b.created_at) - getSafeTime(a.created_at))
            .slice(0, 50);
        try {
          localStorage.setItem(localDataKey, JSON.stringify(mergedAndLimited));
          // console.log(`Данные для "generations_user_cache_${userId}" сохранены после очистки.`);
        } catch (retryError) {
          console.error(`Не удалось сохранить "generations_user_cache_${userId}" даже после очистки:`, retryError);
        }
      }
    } else {
      console.error(`Ошибка при сохранении локальных генераций для ${userId}:`, error);
    }
  }
};


// Функция для получения генераций изображений пользователя
export const PAGE_SIZE_HISTORY = 20;

// Новая функция специально для пагинированной истории пользователя
export const getUserHistoryImageGenerations = async (
  userId: string,
  page: number,
  limit: number = PAGE_SIZE_HISTORY
): Promise<ImageGeneration[]> => {
  if (!userId) {
    console.error('[getUserHistoryImageGenerations] userId is required.');
    return [];
  }

  try {
    // 1. Получаем пагинированные данные из БД
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    const { data: dbData, error: dbError } = await supabase
      .from('image_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (dbError) {
      console.error(`Error fetching paginated DB generations for ${userId} (page ${page}):`, dbError);
      // В случае ошибки БД, для первой страницы можно попробовать вернуть локальные
      if (page === 1) {
        const localFallback = getLocalGenerations(userId).map(g => ({ ...g, local: true } as ImageGeneration));
        // getLocalGenerations не гарантирует сортировку, поэтому отсортируем здесь
        return localFallback.sort((a,b) => getSafeTime(b.created_at) - getSafeTime(a.created_at));
      }
      return []; // Для последующих страниц или если нет локальных - пустой массив
    }

    // Данные из БД помечаем как нелокальные
    const paginatedDbGenerations = ((dbData || []) as ImageGeneration[]).map(g => ({ ...g, local: false } as ImageGeneration));

    if (page === 1) {
      // Для первой страницы также загружаем локальные и объединяем
      const localUserGenerations = getLocalGenerations(userId).map(g => ({ ...g, local: true } as ImageGeneration)); // Убедимся, что local:true

      // mergeGenerations объединит и отсортирует.
      // paginatedDbGenerations уже отсортированы по created_at из БД.
      // localUserGenerations из getLocalGenerations не обязательно отсортированы так, как нужно для merge.
      // Но mergeGenerations сама выполняет сортировку в конце.
      return mergeGenerations(paginatedDbGenerations, localUserGenerations);
    } else {
      // Для последующих страниц возвращаем только данные из БД
      // Они уже отсортированы запросом .order() и помечены как local:false
      return paginatedDbGenerations;
    }
  } catch (error) {
    console.error(`Exception in getUserHistoryImageGenerations for ${userId} (page ${page}):`, error);
    return []; // Общий обработчик ошибок
  }
};
export const getUserImageGenerations = async (userId: string): Promise<ImageGeneration[]> => {
  if (!userId) {
    console.error('Не указан ID пользователя для получения генераций');
    return [];
  }

  console.log('Получение генераций изображений для пользователя:', userId);

  try {
    // Сначала проверяем наличие сохраненных генераций в localStorage
    const localGenerations = getLocalGenerations(userId);
    console.log('Найдены локальные генерации:', localGenerations.length);

    // Запрашиваем генерации из базы данных
    console.log('Запрашиваем генерации изображений из базы данных для пользователя:', userId);
    const { data, error } = await supabase
      .from('image_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Ошибка при получении генераций из базы данных:', error);
      // Если произошла ошибка, возвращаем локальные генерации
      return localGenerations;
    }

    // Обрабатываем полученные данные
    const dbGenerations = data || [];
    console.log(`Получено ${dbGenerations.length} генераций из базы данных`);

    // Объединяем генерации из базы данных и локальные, удаляя дубликаты
    const mergedGenerations = mergeGenerations(dbGenerations, localGenerations);

    // Сохраняем объединенные генерации в localStorage для дальнейшего использования
    saveLocalGenerations(userId, mergedGenerations);

    return mergedGenerations;
  } catch (error) {
    console.error('Ошибка при получении генераций изображений:', error);
    // В случае ошибки возвращаем локальные данные
    return getLocalGenerations(userId);
  }
};

// Функция для объединения генераций из разных источников, удаляя дубликаты и сохраняя флаг 'local'
const mergeGenerations = (
  dbGenerations: ImageGeneration[],
  localGenerations: ImageGeneration[]
): ImageGeneration[] => {
  const generationsMap = new Map<string, ImageGeneration>();

  // Добавляем генерации из базы данных (они приоритетнее при совпадении ID)
  dbGenerations.forEach(gen => {
    // Фильтруем генерации без URL изображений
    if ((Array.isArray(gen.image_urls) && gen.image_urls.length > 0) ||
        (typeof gen.image_url === 'string' && gen.image_url) ||
        (Array.isArray(gen.images) && gen.images.length > 0)) {
      generationsMap.set(gen.id, { ...gen, local: gen.local !== undefined ? gen.local : false } as ImageGeneration);
    }
  });

  // Добавляем локальные генерации, только если их ID еще нет в карте (БД приоритетнее)
  localGenerations.forEach(gen => {
    if (!generationsMap.has(gen.id)) {
      // Фильтруем локальные генерации без URL изображений
      if ((Array.isArray(gen.image_urls) && gen.image_urls.length > 0) ||
          (typeof gen.image_url === 'string' && gen.image_url) ||
          (Array.isArray(gen.images) && gen.images.length > 0)) {
        generationsMap.set(gen.id, { ...gen, local: true } as ImageGeneration); // Убедимся, что локальные всегда имеют local:true
      }
    }
  });

  // Преобразуем Map обратно в массив и сортируем по дате создания
  return Array.from(generationsMap.values())
    .filter(gen => gen && typeof gen === 'object' && gen.id) // Добавлена проверка на существование gen и gen.id
    .sort((a, b) => {
      const timeA = getSafeTime(a.created_at);
      const timeB = getSafeTime(b.created_at);
      return timeB - timeA; // Сортировка по убыванию (новые в начале)
    });
};

// Получение всех генераций изображений пользователя
// Получение недавних генераций изображений ТЕКУЩЕГО пользователя (для ImageGeneration.tsx -> RecentGenerations)
// Возвращает ограниченное количество последних генераций.
export const getImageGenerations = async (userId?: string, limit: number = 5): Promise<ImageGeneration[]> => {
  if (!userId || userId === 'anonymous') {
    console.log('[getImageGenerations] Пользователь не авторизован, возвращаем только локальные генерации (если есть).');
    const localUnsorted = getLocalGenerations(userId);
    return localUnsorted
      .sort((a, b) => getSafeTime(b.created_at) - getSafeTime(a.created_at))
      .slice(0, limit);
  }

  console.log(`[getImageGenerations] Получение последних ${limit} генераций изображений для пользователя: ${userId}`);

  // Кэширование на короткий срок (10 секунд)
  const cacheKey = `recent_image_generations_cache_${userId}_limit_${limit}`;
  const cacheTimeKey = `recent_image_generations_cache_time_${userId}_limit_${limit}`;

  const cachedData = localStorage.getItem(cacheKey);
  const cacheTime = localStorage.getItem(cacheTimeKey);

  if (cachedData && cacheTime) {
    const elapsedTime = Date.now() - parseInt(cacheTime);
    if (elapsedTime < 10000) { // 10 секунд
      console.log(`[getImageGenerations] Использую кэшированные данные, возраст кэша: ${Math.round(elapsedTime / 1000)} сек`);
      try {
        return JSON.parse(cachedData) as ImageGeneration[];
      } catch (parseError) {
        console.error('[getImageGenerations] Ошибка при разборе кэшированных данных:', parseError);
      }
    }
  }

  try {
    // 1. Получаем данные из БД
    const { data: dbData, error: dbError } = await supabase
      .from('image_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit); // Ограничиваем выборку из БД

    if (dbError) {
      console.error(`[getImageGenerations] Ошибка при получении генераций из БД для ${userId}:`, dbError);
      // При ошибке БД, пробуем вернуть только локальные, отсортированные и ограниченные
      const localFallback = getLocalGenerations(userId)
        .sort((a,b) => getSafeTime(b.created_at) - getSafeTime(a.created_at))
        .slice(0, limit);
      // Кэшируем локальный fallback
      try {
        localStorage.setItem(cacheKey, JSON.stringify(localFallback));
        localStorage.setItem(cacheTimeKey, Date.now().toString());
      } catch (e) { /* ignore */ }
      return localFallback;
    }

    const dbGenerations = (dbData || []) as ImageGeneration[];

    // 2. Получаем локальные данные
    const localGenerations = getLocalGenerations(userId);

    // 3. Объединяем и сортируем. mergeGenerations сама сортирует и удаляет дубликаты (БД приоритетнее).
    const merged = mergeGenerations(dbGenerations, localGenerations);

    // 4. Ограничиваем результат снова, так как mergeGenerations могла увеличить количество, если локальных было много уникальных
    const finalResult = merged.slice(0, limit);

    // Кэшируем результат
    try {
      localStorage.setItem(cacheKey, JSON.stringify(finalResult));
      localStorage.setItem(cacheTimeKey, Date.now().toString());
    } catch (cacheError) {
      console.error('[getImageGenerations] Ошибка при кэшировании данных:', cacheError);
    }

    return finalResult;

  } catch (error) {
    console.error(`[getImageGenerations] Исключение при получении генераций изображений для ${userId}:`, error);
    // В случае общей ошибки, возвращаем локальные, отсортированные и ограниченные
    const localFallback = getLocalGenerations(userId)
        .sort((a,b) => getSafeTime(b.created_at) - getSafeTime(a.created_at))
        .slice(0, limit);
    // Кэшируем локальный fallback
    try {
        localStorage.setItem(cacheKey, JSON.stringify(localFallback));
        localStorage.setItem(cacheTimeKey, Date.now().toString());
    } catch (e) { /* ignore */ }
    return localFallback;
  }
};

// Получение всех генераций изображений (для dashbaord администратора)
export const getAllImageGenerations = async (limit: number = 200, offset: number = 0): Promise<GenerationGroup[]> => {
  try {
    console.log(`Запрос генераций изображений из базы данных (limit: ${limit}, offset: ${offset})...`);
    // Получаем все публичные генерации с лайками, сортируем по лайкам и времени
    const { data, error } = await supabase
      .from('image_generations')
      .select('id, prompt, image_urls, created_at, style, aspect_ratio, model, user_id, likes')
      .eq('public', true)
      .order('likes', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Ошибка при получении всех генераций:', error);

      // Если ошибка связана с отсутствием таблицы или доступа, не перезапрашиваем данные
      if (error.code === '42P01' || error.code === '400' || error.code === '404') {
        console.log('Таблица image_generations не существует или недоступна, возвращаем пустой массив');
        return [];
      }

      // Не выбрасываем исключение, а просто логгируем ошибку и продолжаем
      return [];
    }

    if (!data || data.length === 0) {
      console.log('Генерации отсутствуют или данные пусты');
      return [];
    }

    console.log(`Успешно получено ${data.length} генераций`);

    const mappedData = data.map(item => {
      const imageUrls = (item.image_urls || []).filter((url: string) => url && typeof url === 'string' && url.trim() !== '');
      return {
        id: item.id,
        prompt: item.prompt,
        title: item.prompt.split(' ').slice(0, 3).join(' ') + (item.prompt.split(' ').length > 3 ? '...' : ''),
        images: imageUrls,
        timestamp: new Date(item.created_at),
        style: item.style,
        aspect_ratio: item.aspect_ratio,
        model: item.model,
        user_id: item.user_id,
        likes: typeof item.likes === 'number' ? item.likes : 0,
        local: false
      };
    });
    return mappedData;
  } catch (error) {
    console.error('Ошибка при получении всех генераций:', error);
    // Возвращаем пустой массив вместо выбрасывания исключения
    return [];
// --- ЛАЙКИ ДЛЯ ГЕНЕРАЦИЙ ---


/**
 * Сбросить лайки для всех генераций (image/video)
 * @param {'image'|'video'} type
 * @returns {Promise<void>}
 */
  }
};

// Обновление баланса кредитов пользователя
export const updateUserCredits = async (
  userId: string,
  amount: number,
  transactionType: string,
  description: string
): Promise<number> => {
  try {
    console.log(`Обновление баланса пользователя ${userId}: ${amount > 0 ? '+' : ''}${amount} кредитов`);

    // Проверка на валидный UUID
    if (!userId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)) {
      console.error('Ошибка: userId невалидный UUID:', userId);
      throw new Error('UserId должен быть валидным UUID для обновления кредитов');
    }

    // Начинаем транзакцию
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      // Если профиля не существует, создаем его с начальным балансом
      if (profileError.code === 'PGRST116') {
        console.log('Профиль пользователя не найден, создаем новый');
        const { data: userData } = await supabase.auth.getUser();
        const email = userData?.user?.email || `user_${userId.substring(0, 8)}@example.com`;

        // Создаем профиль пользователя
        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            email,
            credits: Math.max(0, amount),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error('Ошибка при создании профиля пользователя:', createError);
          throw createError;
        }

        // Записываем транзакцию в историю, если amount > 0
        if (amount > 0) {
          try {
            await supabase
              .from('user_credits')
              .insert({
                user_id: userId,
                amount,
                transaction_type: transactionType,
                description,
                created_at: new Date().toISOString()
              });
          } catch (transactionError) {
            console.error('Ошибка при записи первоначальной транзакции:', transactionError);
          }
        }

        return Math.max(0, amount);
      }

      console.error('Ошибка при получении кредитов пользователя:', profileError);
      throw profileError;
    }

    const currentCredits = profileData?.credits || 0;
    const newCredits = currentCredits + amount;

    // Проверка на отрицательный баланс при списании
    if (amount < 0 && newCredits < 0) {
      console.error(`Недостаточно кредитов для списания: ${currentCredits} < ${Math.abs(amount)}`);
      throw new Error('Недостаточно кредитов для выполнения операции');
    }

    console.log(`Обновление баланса: ${currentCredits} -> ${newCredits}`);

    // Обновляем баланс кредитов пользователя
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        credits: newCredits,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Ошибка при обновлении кредитов пользователя:', updateError);
      throw updateError;
    }

    // Проверяем существование таблицы user_credits
    try {
      const { error: tableCheckError } = await supabase
        .from('user_credits')
        .select('id')
        .limit(1);

      if (tableCheckError && tableCheckError.code === '42P01') {
        // Таблица не существует, пропускаем запись транзакции
        console.error('Таблица user_credits не существует, пропускаем запись транзакции');
        return newCredits;
      }

      // Записываем транзакцию в историю
      const { error: transactionError } = await supabase
        .from('user_credits')
        .insert({
          user_id: userId,
          amount,
          transaction_type: transactionType,
          description,
          created_at: new Date().toISOString()
        });

      if (transactionError) {
        console.error('Ошибка при записи транзакции кредитов:', transactionError);
        // Не прерываем выполнение, так как кредиты уже обновлены
      }
    } catch (tableError) {
      console.error('Ошибка при проверке таблицы user_credits:', tableError);
    }

    // Отправляем событие обновления баланса
    try {
      const event = new CustomEvent('creditsUpdated', {
        detail: { userId, newBalance: newCredits }
      });
      window.dispatchEvent(event);
      console.log(`Отправлено событие creditsUpdated: ${newCredits} кредитов`);
    } catch (eventError) {
      console.error('Ошибка при отправке события обновления баланса:', eventError);
    }

    return newCredits;
  } catch (error) {
    console.error('Ошибка при обновлении кредитов:', error);
    throw error;
  }
};

// Получение баланса кредитов пользователя
export const getUserCredits = async (userId: string): Promise<number> => {
  try {
    if (!userId) {
      console.error('Ошибка: userId не указан');
      return 0;
    }

    // Получаем текущий баланс
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      // Если профиль не найден, возвращаем 0
      if (profileError.code === 'PGRST116') {
        console.log('Профиль пользователя не найден, возвращаем 0 кредитов');
        return 0;
      }

      console.error('Ошибка при получении баланса кредитов:', profileError);

      // При сетевых ошибках выбрасываем исключение
      if (profileError.message && (
          profileError.message.includes('Failed to fetch') ||
          profileError.message.includes('network') ||
          profileError.message.includes('connection')
        )) {
        throw new Error('Network error: Unable to fetch credits');
      }

      return 0;
    }

    // Возвращаем только числовое значение баланса
    return profileData?.credits || 0;
  } catch (error) {
    console.error('Ошибка при получении кредитов пользователя:', error);

    // При сетевых ошибках выбрасываем исключение
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('Network error: Unable to fetch credits');
    }

    if (error instanceof Error && error.message.includes('Network error')) {
      throw error;
    }

    return 0;
  }
};

// Получение полной информации о кредитах пользователя (баланс + история)
export const getUserCreditsWithHistory = async (userId: string): Promise<UserCredits> => {
  try {
    if (!userId) {
      console.error('Ошибка: userId не указан');
      return { total: 0, history: [] };
    }

    // Получаем текущий баланс
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      // Если профиль не найден, возвращаем пустой объект
      if (profileError.code === 'PGRST116') {
        console.log('Профиль пользователя не найден, возвращаем 0 кредитов и пустую историю');
        return { total: 0, history: [] };
      }

      console.error('Ошибка при получении баланса кредитов:', profileError);
      return { total: 0, history: [] };
    }

    // Проверяем существование таблицы user_credits
    try {
      const { error: tableCheckError } = await supabase
        .from('user_credits')
        .select('id')
        .limit(1);

      if (tableCheckError && tableCheckError.code === '42P01') {
        // Таблица не существует, возвращаем только баланс
        console.error('Таблица user_credits не существует, возвращаем только баланс');
        return { total: profileData?.credits || 0, history: [] };
      }

      // Получаем историю транзакций
      const { data: historyData, error: historyError } = await supabase
        .from('user_credits')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (historyError) {
        console.error('Ошибка при получении истории кредитов:', historyError);
        return { total: profileData?.credits || 0, history: [] };
      }

      return {
        total: profileData?.credits || 0,
        history: historyData.map(item => ({
          id: item.id,
          amount: item.amount,
          transaction_type: item.transaction_type,
          description: item.description,
          created_at: new Date(item.created_at)
        }))
      };
    } catch (tableError) {
      console.error('Ошибка при проверке таблицы user_credits:', tableError);
      return { total: profileData?.credits || 0, history: [] };
    }
  } catch (error) {
    console.error('Ошибка при получении кредитов пользователя:', error);
    return { total: 0, history: [] };
  }
};

// Сохранение сессии чата и первого сообщения
export const saveChatSession = async (
  userId: string,
  title: string,
  initialMessage: string
): Promise<string> => {
  try {
    const sessionId = uuidv4();

    // Создаем сессию чата
    const { error: sessionError } = await supabase
      .from('chat_sessions')
      .insert({
        id: sessionId,
        user_id: userId,
        title,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (sessionError) {
      console.error('Ошибка при создании сессии чата:', sessionError);
      throw sessionError;
    }

    // Добавляем первое сообщение
    const { error: messageError } = await supabase
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        role: 'user',
        content: initialMessage,
        created_at: new Date().toISOString()
      });

    if (messageError) {
      console.error('Ошибка при добавлении сообщения в чат:', messageError);
      throw messageError;
    }

    return sessionId;
  } catch (error) {
    console.error('Ошибка при сохранении сессии чата:', error);
    throw error;
  }
};

// Получение всех сессий чата пользователя
export const getChatSessions = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Ошибка при получении сессий чата:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Ошибка при получении сессий чата:', error);
    return [];
  }
};

// Получение сообщений для конкретной сессии чата
export const getChatMessages = async (sessionId: string) => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Ошибка при получении сообщений чата:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Ошибка при получении сообщений чата:', error);
    return [];
  }
};

// Добавление нового сообщения в сессию чата
export const addChatMessage = async (sessionId: string, role: string, content: string) => {
  try {
    // Добавляем сообщение
    const { error: messageError } = await supabase
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        role,
        content,
        created_at: new Date().toISOString()
      });

    if (messageError) {
      console.error('Ошибка при добавлении сообщения в чат:', messageError);
      throw messageError;
    }

    // Обновляем время последнего обновления сессии
    const { error: sessionError } = await supabase
      .from('chat_sessions')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', sessionId);

    if (sessionError) {
      console.error('Ошибка при обновлении времени сессии:', sessionError);
      throw sessionError;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при добавлении сообщения в чат:', error);
    throw error;
  }
};

// Сохранение генерации текста
export const saveTextGeneration = async (data: TextGenerationSaveData): Promise<string> => {
  const { user_id, prompt, response, model, images, timestamp } = data;
  const generationId = uuidv4();

  try {
    // Проверяем что user_id является валидным UUID
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user_id)) {
      throw new Error(`Invalid user_id format: ${user_id}`);
    }

    // Вначале проверим, существует ли профиль пользователя
    const { error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user_id)
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        // Если профиль не существует, создаем его
        const { error: insertProfileError } = await supabase
          .from('profiles')
          .insert({
            id: user_id,
            email: 'пользователь_' + user_id.substring(0, 8) + '@example.com',
            created_at: new Date().toISOString(),
            credits: 50,
            updated_at: new Date().toISOString()
          });

        if (insertProfileError) {
          console.error('Ошибка при создании профиля:', insertProfileError);
          throw insertProfileError;
        }
      } else {
        // Если другая ошибка
        console.error('Ошибка при проверке профиля:', profileError);
        throw profileError;
      }
    }

    // Сохраняем генерацию текста в таблицу
    const { error } = await supabase
      .from('text_generations')
      .insert({
        id: generationId,
        user_id,
        prompt,
        response,
        model: model || null,
        images: images || null,
        created_at: (timestamp || new Date()).toISOString(),
        public: true
      });

    if (error) {
      console.error('Ошибка при сохранении генерации текста:', error);
      throw error;
    }

    // Списываем кредиты за генерацию (1 кредит за текстовую генерацию)
    await updateUserCredits(user_id, -1, 'text_generation', `Текстовая генерация: ${prompt.substring(0, 30)}...`);

    return generationId;
  } catch (error) {
    console.error('Ошибка при сохранении генерации текста:', error);
    throw error;
  }
};

// Получение генераций текста для пользователя
export const getTextGenerations = async (userId: string): Promise<TextGeneration[]> => {
  try {
    const { data, error } = await supabase
      .from('text_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Ошибка при получении текстовых генераций:', error);
      throw error;
    }

    return data.map(item => ({
      id: item.id,
      prompt: item.prompt,
      response: item.response,
      model: item.model || undefined,
      images: item.images || undefined,
      timestamp: new Date(item.created_at)
    }));
  } catch (error) {
    console.error('Ошибка при получении текстовых генераций:', error);
    return [];
  }
};

// Сохранение генерации видео
export const saveVideoGeneration = async (data: VideoGenerationSaveData): Promise<string> => {
  const { user_id, prompt, video_url, thumbnail_url, style, duration, aspect_ratio, model, generation_id, replicate_prediction_id, visibility } = data;
  const generationId = generation_id || uuidv4();

  try {
    // Проверяем, что supabase клиент имеет правильный ключ
    if (!supabase.auth.getSession) {
      console.error('Supabase client is not properly initialized. API key may be missing.');
      return generationId; // Возвращаем ID, но сохранения не происходит
    }

    console.log('Attempting to save video generation with user_id:', user_id);

    // Вначале проверим, существует ли профиль пользователя
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user_id)
      .single();

    if (profileError) {
      console.error('Ошибка при проверке профиля:', profileError);
      if (profileError.code === '401' || profileError.message?.includes('JWT')) {
        console.error('Ошибка авторизации Supabase. Проверьте ключ API.');
        // В случае ошибки авторизации, сохраняем локально
        saveVideoLocally(user_id, prompt, video_url, thumbnail_url, style, duration, aspect_ratio, model);
        return generationId;
      }

      if (profileError.code !== 'PGRST116') {
        // Если ошибка не связана с отсутствием записи
        throw profileError;
      }
    }

    if (!profileData) {
      // Если профиль не существует, создаем его
      const email = 'пользователь_' + user_id.substring(0, 8) + '@example.com'; // Временный email

      // Используем функцию createUserProfileLegacy вместо ручного создания
      const profile = await import('./supabase').then(module =>
        module.createUserProfileLegacy(user_id, email)
      );

      if (!profile) {
        console.error('Ошибка при создании профиля');
        throw new Error('Не удалось создать профиль пользователя');
      }
    }

    // Сохраняем генерацию видео с URL, полученным от replicateVideoGeneration.ts
    // Логика скачивания и загрузки в Supabase Storage теперь находится в replicateVideoGeneration.ts
    const insertData: any = {
      id: generationId,
      user_id,
      prompt,
      video_url, // Используем URL, переданный в функцию
      thumbnail_url, // Используем URL, переданный в функцию
      style,
      duration,
      aspect_ratio,
      model: model || 'replicate',
      created_at: new Date().toISOString(),
      public: visibility === 'public' // Используем параметр visibility для определения публичности
    };

    // Добавляем replicate_prediction_id если он есть
    if (replicate_prediction_id) {
      insertData.replicate_prediction_id = replicate_prediction_id;
    }

    const { error } = await supabase
      .from('video_generations')
      .insert(insertData);

    if (error) {
      console.error('Ошибка при сохранении видеогенерации в базу данных:', error);
      // В случае ошибки сохранения в БД, сохраняем локально с исходными URL
      saveVideoLocally(user_id, prompt, video_url, thumbnail_url, style, duration, aspect_ratio, model);
      throw error;
    }

    // Списываем кредиты за генерацию (5 кредитов за видео)
    // Списание кредитов после успешного сохранения в БД

    // Вызываем событие обновления генераций
    try {
      const event = new CustomEvent('generationsUpdated', {
        detail: { userId: user_id, type: 'video' }
      });
      window.dispatchEvent(event);
      console.log('Отправлено событие generationsUpdated после сохранения видео');
    } catch (eventError) {
      console.error('Ошибка при отправке события обновления:', eventError);
    }

    return generationId;
  } catch (error) {
    console.error('Ошибка при сохранении видеогенерации (основной try-catch):', error);
    // В случае ошибки сети или другой ошибки, сохраняем локально с исходным URL
    saveVideoLocally(user_id, prompt, video_url, thumbnail_url, style, duration, aspect_ratio, model);
    return generationId;
  }
};
function saveVideoLocally(
  user_id: string,
  prompt: string,
  video_url: string,
  thumbnail_url?: string,
  style?: string,
  duration?: number,
  aspect_ratio?: string,
  model?: string
) {
  try {
    const existingData = localStorage.getItem('localVideoGenerations');
    const generations = existingData ? JSON.parse(existingData) : [];

    generations.push({
      id: uuidv4(),
      user_id,
      prompt,
      video_url,
      thumbnail_url,
      style,
      duration,
      aspect_ratio,
      model: model || 'replicate',
      created_at: new Date().toISOString(),
      local: true // Маркер, что это локальное сохранение
    });

    localStorage.setItem('localVideoGenerations', JSON.stringify(generations));
    console.log('Видео сохранено локально из-за ошибки Supabase');
  } catch (e) {
    console.error('Не удалось сохранить видео локально:', e);
  }
}

// Получение генераций видео для пользователя
export const getVideoGenerations = async (userId: string): Promise<VideoGeneration[]> => {
  const results: VideoGeneration[] = [];

  try {
    // Попытка получить данные из Supabase
    const { data, error } = await supabase
      .from('video_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Ошибка при получении видеогенераций из базы данных:', error);
      // Если ошибка, не выбрасываем исключение, а просто продолжаем
    } else if (data) {
      // Преобразуем данные из Supabase и фильтруем те, у которых нет video_url
      const supabaseResults = data
        .filter(item => item.video_url) // Добавляем фильтрацию
        .map(item => ({
          id: item.id,
          prompt: item.prompt,
          video_url: item.video_url,
          thumbnail_url: item.thumbnail_url,
          style: item.style,
          duration: item.duration,
          aspect_ratio: item.aspect_ratio,
          model: item.model,
          timestamp: new Date(item.created_at)
        }));

      results.push(...supabaseResults);
    }
  } catch (dbError) {
    console.error('Ошибка при доступе к базе данных:', dbError);
  }

  // Получаем локально сохраненные генерации
  try {
    const localData = localStorage.getItem('localVideoGenerations');
    if (localData) {
      const localGenerations = JSON.parse(localData);
      const userLocalGenerations = localGenerations
        .filter((gen: any) => gen.user_id === userId && gen.video_url)
        .map((item: any) => ({
          id: item.id,
          prompt: item.prompt,
          video_url: item.video_url,
          thumbnail_url: item.thumbnail_url,
          style: item.style,
          duration: item.duration,
          aspect_ratio: item.aspect_ratio,
          model: item.model,
          timestamp: new Date(item.created_at),
          local: true
        }));

      results.push(...userLocalGenerations);
    }
  } catch (localError) {
    console.error('Ошибка при получении локальных видеогенераций:', localError);
  }

  // Сортируем по времени, самые новые первыми
  return results.sort((a, b) => {
    const timeA = a.timestamp ? getSafeTime(a.timestamp) : 0;
    const timeB = b.timestamp ? getSafeTime(b.timestamp) : 0;
    return timeB - timeA;
  });
};

/**
 * Проверяет и создает колонку updated_at в таблице profiles, если она отсутствует
 * @returns {Promise<boolean>} Успешность операции
 */
export async function checkAndAddUpdatedAtColumn(): Promise<boolean> {
  try {
    // Проверяем существование колонки через запрос к суперпользователю
    console.log('Проверка существования колонки updated_at в таблице profiles');

    // Пробуем сначала просто добавить колонку, если её нет - запрос выполнится успешно
    // Используем валидный UUID для тестового запроса
    const { error: alterError } = await supabase
      .from('profiles')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', '00000000-0000-0000-0000-000000000000')
      .select();

    // Если запрос вернул ошибку 42703 (column does not exist), значит колонки нет
    if (alterError && alterError.code === '42703') {
      console.log('Колонка updated_at не найдена, пробуем создать через обновление типов');

      // В этом случае нужно обновить клиент и выполнить миграцию в Supabase
      console.warn('Необходимо выполнить SQL миграцию в Supabase:');
      console.warn('ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()');

      // Возвращаем false, так как мы не можем создать колонку без прав суперпользователя
      return false;
    }

    // Если ошибка не связана с отсутствием колонки, просто логируем её
    if (alterError && alterError.code !== '42703') {
      console.log('При проверке колонки произошла ошибка, но, возможно, колонка существует:', alterError);
    } else {
      console.log('Колонка updated_at существует в таблице profiles');
    }

    return true;
  } catch (error) {
    console.error('Непредвиденная ошибка при проверке/добавлении колонки updated_at:', error);
    return false;
  }
}

/**
 * Инкремент/декремент лайка для генерации (image/video)
 * @param {'image'|'video'} type
 * @param {string} generationId
 * @param {1|-1} delta
 * @returns {Promise<number>} новое значение likes
 */
async function incrementGenerationLike(type: 'image' | 'video', generationId: string, delta: 1 | -1): Promise<number> {
  // Для image_generations: сначала получаем текущее значение likes, затем делаем update
  if (type === 'image') {
    // Получаем текущее значение likes
    const { data: currentData, error: selectError } = await supabase
      .from('image_generations')
      .select('likes')
      .eq('id', generationId)
      .single();

    if (selectError || !currentData) {
      console.error('Ошибка при получении текущего значения лайков:', selectError);
      throw selectError || new Error('Generation not found');
    }

    const currentLikes = typeof currentData.likes === 'number' ? currentData.likes : 0;
    const newLikes = Math.max(0, currentLikes + delta);

    const { data: updateData, error: updateError } = await supabase
      .from('image_generations')
      .update({ likes: newLikes })
      .eq('id', generationId)
      .select('likes')
      .single();

    if (updateError || !updateData) {
      console.error('Ошибка при обновлении лайков:', updateError);
      throw updateError || new Error('Update failed');
    }

    return updateData.likes ?? 0;
  } else {
    // Для video_generations оставляем прежнюю логику (если она работает)
    const { data, error } = await supabase
      .from('video_generations')
      .select('likes')
      .eq('id', generationId)
      .single();

    if (error || !data) {
      console.error('Ошибка при получении текущего значения лайков (видео):', error);
      throw error || new Error('Generation not found');
    }

    const currentLikes = typeof data.likes === 'number' ? data.likes : 0;
    const newLikes = Math.max(0, currentLikes + delta);

    const { data: updateData, error: updateError } = await supabase
      .from('video_generations')
      .update({ likes: newLikes })
      .eq('id', generationId)
      .select('likes')
      .single();

    if (updateError || !updateData) {
      console.error('Ошибка при обновлении лайков (видео):', updateError);
      throw updateError || new Error('Update failed');
    }

    return updateData.likes ?? 0;
  }
}

export { incrementGenerationLike };

// Интерфейсы для чатов
interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  images?: string[];
  isUser: boolean;
  isLoading?: boolean;
}

interface ChatData {
  id: string;
  user_id: string;
  title: string;
  messages: ChatMessage[];
  created_at: string;
  updated_at: string;
}

// Сохранение чата в базу данных
export const saveChatToDatabase = async (chatId: string, userId: string, messages: ChatMessage[]): Promise<void> => {
  try {
    if (!userId || userId === 'anonymous') {
      // Сохраняем локально для анонимных пользователей
      saveChatLocally(chatId, userId, messages);
      return;
    }

    // Получаем первое сообщение пользователя для заголовка
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    const title = firstUserMessage
      ? firstUserMessage.content.slice(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '')
      : 'Новый чат';

    const chatData = {
      user_id: userId,
      title,
      messages: JSON.stringify(messages),
      updated_at: new Date().toISOString()
    };

    // Используем upsert для избежания конфликтов
    const { error } = await supabase
      .from('text_chats')
      .upsert({
        id: chatId,
        ...chatData,
        created_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (error) {
      console.error('Ошибка при сохранении чата:', error);
      saveChatLocally(chatId, userId, messages);
    }
  } catch (error) {
    console.error('Ошибка при сохранении чата:', error);
    saveChatLocally(chatId, userId, messages);
  }
};

// Локальное сохранение чата
const saveChatLocally = (chatId: string, userId: string, messages: ChatMessage[]): void => {
  try {
    const localChats = JSON.parse(localStorage.getItem('localChats') || '[]');
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    const title = firstUserMessage
      ? firstUserMessage.content.slice(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '')
      : 'Новый чат';

    const chatData = {
      id: chatId,
      user_id: userId,
      title,
      messages,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const existingIndex = localChats.findIndex((chat: any) => chat.id === chatId);
    if (existingIndex >= 0) {
      localChats[existingIndex] = chatData;
    } else {
      localChats.unshift(chatData);
    }

    // Ограничиваем количество локальных чатов
    const limitedChats = localChats.slice(0, 50);
    localStorage.setItem('localChats', JSON.stringify(limitedChats));
  } catch (error) {
    console.error('Ошибка при локальном сохранении чата:', error);
  }
};

// Загрузка чатов из базы данных
export const loadChatsFromDatabase = async (userId: string): Promise<ChatData[]> => {
  try {
    if (!userId || userId === 'anonymous') {
      return loadChatsLocally(userId);
    }

    const { data, error } = await supabase
      .from('text_chats')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Ошибка при загрузке чатов:', error);
      return loadChatsLocally(userId);
    }

    const chats = data.map(chat => ({
      ...chat,
      messages: JSON.parse(chat.messages || '[]')
    }));

    // Объединяем с локальными чатами
    const localChats = loadChatsLocally(userId);
    const allChats = [...chats, ...localChats];

    // Удаляем дубликаты по ID
    const uniqueChats = allChats.filter((chat, index, self) =>
      index === self.findIndex(c => c.id === chat.id)
    );

    return uniqueChats.sort((a, b) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    );
  } catch (error) {
    console.error('Ошибка при загрузке чатов:', error);
    return loadChatsLocally(userId);
  }
};

// Локальная загрузка чатов
const loadChatsLocally = (userId: string): ChatData[] => {
  try {
    const localChats = JSON.parse(localStorage.getItem('localChats') || '[]');
    return localChats.filter((chat: any) => chat.user_id === userId || chat.user_id === 'anonymous');
  } catch (error) {
    console.error('Ошибка при локальной загрузке чатов:', error);
    return [];
  }
};

// Загрузка сообщений конкретного чата
export const loadChatMessagesFromDatabase = async (chatId: string): Promise<ChatMessage[]> => {
  try {
    // Сначала пытаемся загрузить из базы данных
    const { data, error } = await supabase
      .from('text_chats')
      .select('messages')
      .eq('id', chatId)
      .single();

    if (!error && data) {
      return JSON.parse(data.messages || '[]');
    }

    // Если не найдено в базе, ищем локально
    const localChats = JSON.parse(localStorage.getItem('localChats') || '[]');
    const localChat = localChats.find((chat: any) => chat.id === chatId);

    return localChat ? localChat.messages : [];
  } catch (error) {
    console.error('Ошибка при загрузке сообщений чата:', error);
    return [];
  }
};

/**
 * Проверяет существование видео генерации по replicate_prediction_id
 */
export const checkVideoGenerationExists = async (replicatePredictionId: string): Promise<boolean> => {
  try {
    if (!replicatePredictionId) return false;

    const { data, error } = await supabase
      .from('video_generations')
      .select('id')
      .eq('replicate_prediction_id', replicatePredictionId)
      .limit(1);

    if (error) {
      console.error('Ошибка при проверке существования видео генерации:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Ошибка при проверке существования видео генерации:', error);
    return false;
  }
};
