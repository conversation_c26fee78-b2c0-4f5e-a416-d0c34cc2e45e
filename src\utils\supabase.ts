import { createClient } from '@supabase/supabase-js';
import { User } from '@supabase/supabase-js';
import type { ImageGenerationRow } from "../types/database";

/**
 * Получить генерации всех пользователей (общая лента)
 * @param {number} limit - сколько записей вернуть (по умолчанию 20)
 * @param {number} offset - смещение (по умолчанию 0)
 * @returns {Promise<ImageGenerationRow[]>}
 */
export const getAllGenerations = async (
  limit: number = 20,
  offset: number = 0
): Promise<ImageGenerationRow[]> => {
  const { data, error } = await supabase
    .from("image_generations")
    .select("*")
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Ошибка при получении генераций:", error);
    return [];
  }
  return data as ImageGenerationRow[];
};

// Настройки подключения к Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://ehamdaltpbuicmggxhbn.supabase.co';
const supabaseAnonKey = 
  import.meta.env.VITE_SUPABASE_ANON_KEY || 
  import.meta.env.VITE_SUPABASE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4ODI4NzIsImV4cCI6MjA1OTQ1ODg3Mn0.E-Q4-HfKege8ipj2JFGsl8sCFlUbbDhJqTue7A6Uhgo';

// Проверяем наличие обязательных переменных окружения
if (!supabaseUrl) {
  console.error('ОШИБКА: Не найден URL для Supabase');
}
if (!supabaseAnonKey) {
  console.error('ОШИБКА: Не найден ключ для Supabase');
}
/* console.log('Supabase URL:', supabaseUrl);
console.log('Supabase key установлен:', !!supabaseAnonKey); */

const appUrl = import.meta.env.VITE_APP_URL || window.location.origin;

// Создаем клиент Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'implicit'
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'x-application-name': 'enigma-genesis'
    }
  }
});

// Также доступны для дополнительных операций:
// Service role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzg4Mjg3MiwiZXhwIjoyMDU5NDU4ODcyfQ.nhTTkxS8gam_1PejxDX93nsVgKmy61BJdc_ss8xuAYc
// JWT Secret: bl3JKyZ9JEcvaFy3T7gGshVzFtKcBMO1Cssf6ABNwwDCjWKEDYJm1rhKB7pmknD2X++OBcFRW6jFIPZlSVtpXQ==

// Функция для выполнения SQL запроса через Supabase
export const executeQuery = async (query: string, params: any[] = []) => {
  const { data, error } = await supabase.rpc('execute_query', {
    query_text: query,
    query_params: params
  });
  
  if (error) throw error;
  return data;
};

// Функция для аутентификации через Google
export const signInWithGoogle = async () => {
  return supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${appUrl}/auth/callback`
    }
  });
};

// Функция для проверки и обработки аутентификации после OAuth редиректа
export const handleAuthCallback = async () => {
  const { data, error } = await supabase.auth.getSession();
  if (error) {
    console.error('Ошибка при получении сессии:', error);
    throw error;
  }
  return { session: data.session };
};

// Функция для входа по email/password
export const signInWithEmail = async (email: string, password: string) => {
  const response = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  return { data: response.data, error: response.error };
};

// Функция для регистрации по email/password
export const signUpWithEmail = async (email: string, password: string) => {
  // Сначала пробуем зарегистрировать пользователя
  const response = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${appUrl}/auth/callback`,
      // Устанавливаем данные для автовхода - отключаем необходимость подтверждения email
      data: {
        email_confirmed: true // пометка что email подтвержден
      }
    }
  });
  
  // После успешной регистрации создаем профиль пользователя
  if (response.data.user) {
    try {
      await createUserProfile(response.data.user);
      
      // Если сессия не была создана автоматически, делаем вход вручную
      if (!response.data.session) {
        /* console.log('Сессия не создана автоматически, выполняем вход вручную'); */
        
        // Небольшая задержка перед входом, чтобы данные пользователя точно сохранились
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Пробуем войти с теми же данными
        const signInResponse = await supabase.auth.signInWithPassword({
          email,
          password
        });
        
        if (signInResponse.data.session) {
          // Если удалось войти, обновляем данные в ответе
          response.data.session = signInResponse.data.session;
          /* console.log('Вход выполнен успешно после регистрации'); */
        } else if (signInResponse.error) {
          console.error('Ошибка при автоматическом входе после регистрации:', signInResponse.error);
        }
      }
    } catch (profileError) {
      console.error('Ошибка при создании профиля:', profileError);
      // Продолжаем даже при ошибке создания профиля
    }
  }
  
  return { data: response.data, error: response.error };
};

// Функция для выхода из системы
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

// Функция получения текущего пользователя
export const getCurrentUser = async () => {
  const { data, error } = await supabase.auth.getUser();
  return { user: data.user, error };
};

// Функция получения текущей сессии
export const getCurrentSession = async () => {
  const response = await supabase.auth.getSession();
  return { session: response.data.session, error: response.error };
};

/**
 * Сброс пароля
 */
export const resetPassword = async (email: string) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${appUrl}/reset-password`,
  });
  
  if (error) throw error;
};

// Обмен кода авторизации на сессию (для PKCE flow)
export async function exchangeCodeForSession(code: string) {
  try {
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Ошибка при обмене кода на сессию:', error);
    throw error;
  }
}

// Функция для проверки, существует ли пользователь с данным email
export const checkUserExists = async (email: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('email')
    .eq('email', email)
    .single();
  
  return { exists: !!data, error };
};

// Функция для обновления профиля пользователя
export const updateUserProfile = async (userId: string, profile: any) => {
  return await supabase
    .from('profiles')
    .upsert({ 
      id: userId,
      ...profile,
      updated_at: new Date().toISOString()
    }, { onConflict: 'id' });
};

/**
 * Создает профиль пользователя, если он еще не существует
 * Проверяет существование профиля и создает новый, если профиль не найден
 * @param user Информация о пользователе
 * @returns Профиль пользователя
 */
export const createUserProfile = async (user: User): Promise<any> => {
  if (!user?.id) {
    console.error('Ошибка: Не указан ID пользователя');
    return null;
  }

  /* console.log('Проверка существования профиля для пользователя:', user.id); */

  try {
    // Проверяем, существует ли профиль
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: При сетевых ошибках НЕ создаем новый профиль
    if (checkError) {
      console.error('Ошибка при проверке профиля:', checkError);
      
      // Если это сетевая ошибка - НЕ создаем новый профиль
      if (checkError.message.includes('Failed to fetch') ||
          checkError.message.includes('network') ||
          checkError.message.includes('connection')) {
        console.warn('Сетевая ошибка при проверке профиля. Возвращаем базовый профиль без создания нового.');
        throw new Error('Network error: Cannot verify profile existence');
      }
    }

    // Если профиль существует - возвращаем его
    if (existingProfile) {
      /* console.log('Существующий профиль найден:', existingProfile.id); */
      return existingProfile;
    }

    /* console.log('Профиль не найден для пользователя:', user.id); */

    // Создаем профиль ТОЛЬКО если он действительно не существует
    const newProfile = {
      id: user.id,
      email: user.email || `user_${user.id.substring(0, 8)}@example.com`,
      name: user.user_metadata?.name || user.user_metadata?.full_name || '',
      credits: 10,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    /* console.log('Создаем новый профиль пользователя:', newProfile.id); */
    
    // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Используем INSERT вместо UPSERT
    const { data: profile, error: createError } = await supabase
      .from('profiles')
      .insert(newProfile)
      .select()
      .single();
      
    if (createError) {
      console.error('Ошибка при создании профиля:', createError);
      
      // Если профиль уже существует - получаем его
      if (createError.code === '23505' || createError.message.includes('duplicate')) {
        /* console.log('Профиль уже существует, получаем его'); */
        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        return existingProfile;
      }
      
      throw createError;
    }
    
    return profile;
  } catch (error) {
    console.error('Ошибка при работе с профилем:', error);
    
    // При сетевых ошибках НЕ создаем фейковый профиль
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('Network error: Cannot create or verify profile');
    }
    
    if (error instanceof Error && error.message.includes('Network error')) {
      throw error;
    }
    
    // При других ошибках возвращаем базовый профиль
    return {
      id: user.id,
      email: user.email || `user_${user.id.substring(0, 8)}@example.com`,
      credits: 10
    };
  }
};

// Адаптер для backwards compatibility с предыдущими вызовами
export const createUserProfileLegacy = async (userId: string, email: string, name?: string) => {
  const mockUser: User = {
    id: userId,
    email: email || undefined, // Changed null to undefined
    user_metadata: { name: name || '' },
    app_metadata: {},
    aud: '',
    created_at: '',
    role: undefined, // Changed null to undefined
    updated_at: ''
  };
  
  return createUserProfile(mockUser);
};

// Helper function to get file extension from URL
export const getImageExtension = (url: string): string => {
  if (typeof url !== 'string' || (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:'))) {
    console.warn(`[getImageExtension] Invalid URL provided: "${String(url).substring(0,50)}...". Defaulting to 'png'.`);
    return 'png';
  }
  try {
    // For data URLs, try to extract from the type
    if (url.startsWith('data:image/')) {
      const match = url.match(/^data:image\/([a-zA-Z+]+);/);
      if (match && match[1]) {
        const ext = match[1].toLowerCase();
        if (['png', 'jpg', 'jpeg', 'gif', 'webp'].includes(ext)) {
          return ext === 'jpeg' ? 'jpg' : ext;
        }
      }
    }

    const pathname = new URL(url).pathname; // This might still fail for some malformed http/https URLs
    const parts = pathname.split('.');
    if (parts.length > 1) {
      let extension = parts.pop()?.toLowerCase() || 'png';
      extension = extension.split('?')[0]; // Remove query params
      extension = extension.split('#')[0]; // Remove fragment
      const validExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'];
      if (validExtensions.includes(extension)) {
        return extension;
      }
    }
  } catch (e) {
    console.error(`[getImageExtension] Error extracting extension from URL: "${String(url).substring(0,100)}...". Error: ${e instanceof Error ? e.message : String(e)}`);
  }
  // Default to 'png' if extraction fails or not a common image type
  return 'png';
};

// Helper function to download an image from a URL
export const downloadImage = async (imageUrl: string): Promise<Blob> => {
  if (typeof imageUrl !== 'string' || (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://'))) {
    console.error(`[downloadImage] Invalid URL provided for download: ${imageUrl}`);
    throw new Error(`Invalid URL for download: ${imageUrl}`);
  }
  try {
    const response = await fetch(imageUrl, { mode: 'cors' }); // Use cors mode
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText} from ${imageUrl}`);
    }
    const blob = await response.blob();
    return blob;
  } catch (error) {
  console.error('Error downloading image:', imageUrl, error);
  throw error;
  }
};

// Helper function to convert Data URI to Blob
export const dataUriToBlob = (dataURI: string): Blob => {
  if (!dataURI.startsWith('data:')) {
    throw new Error('Invalid data URI');
  }
  // Split metadata from data
  const parts = dataURI.split(',');
  if (parts.length < 2) {
    throw new Error('Malformed data URI');
  }
  const meta = parts[0];
  const data = parts[1];

  // Extract MIME type
  const mimeMatch = meta.match(/:(.*?);/);
  if (!mimeMatch || mimeMatch.length < 2) {
    throw new Error('Could not extract MIME type from data URI');
  }
  const mimeType = mimeMatch[1];

  // Convert base64 to raw binary data held in a string
  // Use atob for environments where it's available (browsers)
  const byteString = atob(data);

  // Write the bytes of the string to an ArrayBuffer
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  // Create the Blob
  return new Blob([ab], { type: mimeType });
};

// Helper function to upload an image to Supabase Storage
export const uploadImageToSupabaseStorage = async (
  userId: string,
  imageBlob: Blob,
  originalFileName?: string
): Promise<string> => {
  const bucketName = 'generations';
  const extension =
    (originalFileName && originalFileName.split('.').pop()) ||
    imageBlob.type.split('/').pop() ||
    'png';
  const fileName = `images/${userId}/${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 8)}.${extension}`;

  /* console.log(
    `Uploading image to Supabase Storage: bucket='${bucketName}', path='${fileName}', type='${imageBlob.type}'`
  ); */

  const { error } = await supabase.storage
    .from(bucketName)
    .upload(fileName, imageBlob, {
      contentType: imageBlob.type || `image/${extension}`,
      upsert: false,
    });

  if (error) {
    console.error('Error uploading image to Supabase Storage:', error);
    throw error;
  }

  // Получаем публичную ссылку
  const publicUrlData = supabase.storage
    .from(bucketName)
    .getPublicUrl(fileName);
  
    if (!publicUrlData || !publicUrlData.data || !publicUrlData.data.publicUrl) {
      console.error('Error getting public URL for uploaded image:', fileName);
      throw new Error('Failed to get public URL for uploaded image.');
    }
  
    /* console.log('Image uploaded successfully to Supabase Storage:', publicUrlData.data.publicUrl); */
    return publicUrlData.data.publicUrl;
  }
  
  // Функция для загрузки видео в Supabase Storage
  export const uploadVideoToSupabaseStorage = async (
  userId: string,
  videoBlob: Blob,
  originalFileName?: string
): Promise<string> => {
  const bucketName = 'generations';
  const extension =
    (originalFileName && originalFileName.split('.').pop()) ||
    videoBlob.type.split('/').pop() ||
    'mp4';
  const fileName = `videos/${userId}/${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 8)}.${extension}`;

  /* console.log(
    `Uploading video to Supabase Storage: bucket='${bucketName}', path='${fileName}', type='${videoBlob.type}'`
  ); */

  const { error } = await supabase.storage
    .from(bucketName)
    .upload(fileName, videoBlob, {
      contentType: videoBlob.type || `video/${extension}`,
      upsert: false,
    });

  if (error) {
    console.error('Error uploading video to Supabase Storage:', error);
    throw error;
  }

  // Получаем публичную ссылку
  // Получаем публичную ссылку
  const publicUrlData = supabase.storage
    .from(bucketName)
    .getPublicUrl(fileName);

  if (!publicUrlData || !publicUrlData.data || !publicUrlData.data.publicUrl) {
    console.error('Error getting public URL for uploaded video:', fileName);
    throw new Error('Failed to get public URL for uploaded video.');
  }

  /* console.log('Video uploaded successfully to Supabase Storage:', publicUrlData.data.publicUrl); */
  return publicUrlData.data.publicUrl;
};
