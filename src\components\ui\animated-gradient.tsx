
import React from 'react';
import { cn } from '@/lib/utils';

interface AnimatedGradientProps {
  className?: string;
}

const AnimatedGradient = ({ className }: AnimatedGradientProps) => {
  return (
    <div className={cn("absolute inset-0 overflow-hidden -z-10", className)}>
      <div className="absolute top-[-10%] -left-[10%] w-[40%] h-[40%] rounded-full bg-uma-purple/20 filter blur-[100px] animate-pulse-slow" />
      <div className="absolute top-[20%] right-[5%] w-[30%] h-[30%] rounded-full bg-uma-purple/15 filter blur-[120px] animate-pulse-slow animation-delay-400" />
      <div className="absolute bottom-[-5%] left-[20%] w-[25%] h-[25%] rounded-full bg-uma-purple-dark/10 filter blur-[80px] animate-pulse-slow animation-delay-800" />
    </div>
  );
};

export default AnimatedGradient;
