import fetch from 'node-fetch';

export default async function handler(req, res) {
  const { url } = req.query;
  if (!url || typeof url !== "string" || !url.startsWith("https://replicate.delivery/")) {
    res.status(400).json({ error: "Invalid or missing URL" });
    return;
  }

  try {
    const response = await fetch(url);

    if (!response.ok) {
      res.status(response.status).json({ error: "Failed to fetch video from Replicate" });
      return;
    }

    res.setHeader('Content-Type', response.headers.get('content-type') || 'video/mp4');
    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');

    response.body.pipe(res);
  } catch (e) {
    res.status(500).json({ error: "Proxy error", details: e.message });
  }
}