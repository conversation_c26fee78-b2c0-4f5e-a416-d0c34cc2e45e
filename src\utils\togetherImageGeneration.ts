import { saveImageGeneration } from './database';

export interface TogetherImageParams {
  n?: number;
  width?: number;
  height?: number;
  steps?: number;
  seed?: number;
  style?: string;
  aspectRatio?: string;
  userId?: string;
  initImage?: string | null;
  mode?: 'image' | 'img2img'; // Режим генерации: обычная генерация текст->изображение или изображение->изображение
  model?: 'together' | 'ideogram' | 'hidream' | 'minimax-image01' | 'imagen4' | 'bagel' | 'flux-kontext-pro' | 'flux-kontext-max'; // Модель для генерации: together (Flux Schnell), ideogram (img2img), hidream, minimax-image01, imagen4, bagel, flux-kontext-pro, flux-kontext-max
  prompt: string;
  negative_prompt?: string;
  visibility?: 'public' | 'private'; // Приватность генерации
}

// Используем прямой API ключ для Together AI (Flux Schnell)
const TOGETHER_API_KEY = "97742fce477abee08db8ca4510a4a955d5379e8e8a7c92fabb46d87d2a76cdcf";

export async function generateImageWithTogether(
  prompt: string,
  params: TogetherImageParams
): Promise<Array<{ url: string }>> {
  // Получаем или устанавливаем значения по умолчанию
  const {
    n = 1,
    width = 1024,
    height = 1024,
    steps = 20,
    userId,
    style,
    aspectRatio,
    initImage,
    mode = 'image',
    model = 'together',
    negative_prompt
  } = params;

  /* console.log('Генерация изображения с параметрами:', {
    prompt: prompt.substring(0, 30) + '...',
    model,
    style,
    aspectRatio,
    hasUserId: !!userId,
    mode
  }); */

  // НОВОЕ: Все модели кроме 'together' (Flux Schnell) должны использовать Replicate API
  if (model !== 'together') {
    /* console.log(`Переключение на Replicate API для модели: ${model}`); */
    try {
      // Импортируем динамически функцию для Replicate
      const replicateModule = await import('./replicateImageGeneration');
      const replicateResult = await replicateModule.generateImageWithReplicate({
        prompt,
        negativePrompt: negative_prompt,
        model: model, // 'ideogram', 'hidream', 'ideogram3', etc.
        width: width,
        height: height,
        userId: userId, // Передаем userId
        aspectRatio: aspectRatio, // Передаем aspectRatio
        // Другие параметры, специфичные для Replicate, могут быть добавлены здесь, если они есть в params
        // Например, style_type, если он передается в params для моделей типа Ideogram
        ...(params.style && { style: params.style }), // Передаем общий 'style', если он есть
        visibility: (params.visibility || 'public') as 'public' | 'private', // Передаем приватность
      });

      // Преобразуем результат Replicate (ReplicateGenerationResult)
      // в массив объектов { url: string }, ожидаемый далее в этой функции
      if (replicateResult && replicateResult.images && replicateResult.images.length > 0) {
        // replicateResult.images это GeneratedImage[]
        // Нам нужно вернуть массив объектов { url: string } для совместимости
        return replicateResult.images.map(item => ({ url: item.url }));
      }
      return [];
    } catch (error) {
      console.error('Ошибка при импорте модуля Replicate:', error);
      throw new Error('Не удалось загрузить модуль для генерации с Replicate API');
    }
  }

  // Массив для хранения результатов
  let imageUrlsToSave: string[] = [];

  try {


    if (mode === 'img2img' && initImage) {
      /* console.log('Используем Ideogram для img2img с подсказкой'); */
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';
      const response = await fetch(`${apiUrl}/api/replicate-img2img`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: prompt,
          image: initImage,
          aspectRatio: aspectRatio || '1:1',
          userId: userId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Ошибка при генерации img2img');
      }

      const data = await response.json();
      /* console.log('Ideogram generated image via server, response:', data); */

      if (data.success && data.imageUrl) {
        imageUrlsToSave.push(data.imageUrl);
        /* console.log('Ideogram generated image URL:', data.imageUrl); */
      } else {
        throw new Error('Не удалось получить URL изображения');
      }
    } else {
      const baseUrl = '/api/together/generate';

      if (!TOGETHER_API_KEY) {
        throw new Error('API ключ для Together AI не найден.');
      }

      const adjustedWidth = Math.floor(width / 16) * 16;
      const adjustedHeight = Math.floor(height / 16) * 16;

      if (width !== adjustedWidth || height !== adjustedHeight) {
        /* console.log(`Размеры изображения скорректированы: ${width}x${height} -> ${adjustedWidth}x${adjustedHeight}`); */
      }

      const targetCount = Math.max(1, Math.min(n, 4));

      const requestBody = {
        model: "black-forest-labs/FLUX.1-schnell-Free",
        prompt: prompt,
        width: adjustedWidth,
        height: adjustedHeight,
        steps: Math.max(1, Math.min(steps, 4)),
        num_outputs: targetCount,
        response_format: "b64_json",
      };

      /* console.log('Together AI params:', requestBody); */

      let retryCount = 0;
      const maxRetries = 5;
      const initialDelay = 5000;
      let delay = initialDelay;
      const maxDelay = 60000;

      let b64Results: Array<{ b64_json: string }> = [];

      while (retryCount <= maxRetries) {
        try {
          const response = await fetch(baseUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${TOGETHER_API_KEY}`
            },
            body: JSON.stringify(requestBody)
          });

          if (response.status === 429) {
            console.warn(`Получен код 429. Ожидаем ${delay/1000} секунд перед повторной попыткой (${retryCount+1}/${maxRetries})...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            delay = Math.min(delay * 2, maxDelay);
            retryCount++;
            continue;
          }

          if (!response.ok) {
            if (response.status === 504) {
              throw new Error('Ошибка API: 504 (Gateway Timeout). Попробуйте увеличить steps или повторить позже.');
            }
            throw new Error(`Ошибка API: ${response.status} ${await response.text()}`);
          }

          const data = await response.json();
          /* console.log('API response:', data); */

          if (data.data && data.data.length > 0) {
            data.data.forEach((item: any) => {
              b64Results.push({ b64_json: item.b64_json });
            });
            /* console.log(`Generated ${data.data.length} images, total: ${b64Results.length}`); */
          }
          break;
        } catch (error) {
          console.error(`Attempt ${retryCount + 1} failed:`, error);
          if (retryCount >= maxRetries) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, delay));
          delay = Math.min(delay * 2, maxDelay);
          retryCount++;
        }
      }

      if (b64Results.length > 0 && userId) {
        // Загружаем base64 изображения в Supabase Storage и сохраняем в БД
        const { dataUriToBlob, uploadImageToSupabaseStorage } = await import('./supabase');
        for (const resultItem of b64Results) {
          if (resultItem.b64_json) {
            const dataUri = `data:image/jpeg;base64,${resultItem.b64_json}`;
            const blob = dataUriToBlob(dataUri);
            const supabaseUrl = await uploadImageToSupabaseStorage(userId, blob);
            if (supabaseUrl) {
              imageUrlsToSave.push(supabaseUrl);
            }
          }
        }

        // Фильтруем валидные URL
        imageUrlsToSave = imageUrlsToSave.filter(
          url => typeof url === 'string' && url.startsWith('http')
        );

        // Для моделей, которые используют webhook (Replicate API), не сохраняем здесь
        // чтобы избежать дублирования. Webhook сам сохранит результат.
        const isWebhookBasedModel = ['ideogram', 'hidream', 'ideogram3', 'minimax-image01', 'imagen4', 'bagel', 'flux-kontext-pro', 'flux-kontext-max'].includes(model);

        if (imageUrlsToSave.length > 0 && !isWebhookBasedModel) {
          await saveImageGeneration({
            user_id: userId,
            prompt,
            image_url: imageUrlsToSave,
            style,
            aspect_ratio: aspectRatio,
            model: model
          });
          /* console.log(`Saved ${imageUrlsToSave.length} image(s) to database`); */
        } else if (isWebhookBasedModel) {
          /* console.log(`Skipping client-side save for webhook-based model: ${model}`); */
        } else {
          console.warn('[generateImageWithTogether] No valid image URLs found to save.');
        }
      }
    }

    // Для Together API возвращаем data URI, так как реальные URL будут созданы в saveImageGeneration
    return imageUrlsToSave.map(url => ({ url }));
  } catch (error) {
    console.error('Error generating images:', error);
    throw error;
  }
}
