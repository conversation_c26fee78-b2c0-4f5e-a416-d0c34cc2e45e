-- Скрипт для полного исправления проблем с RLS и таблицей profiles

-- 1. Временно отключаем RLS для таблицы profiles, чтобы сначала исправить все проблемы
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Создаем недостающие колонки (если их нет)
DO $$ 
BEGIN
  -- Проверяем существует ли колонка updated_at в таблице profiles
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
      AND column_name = 'updated_at'
  ) THEN
    -- Если колонка не существует, добавляем её
    ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Колонка updated_at добавлена в таблицу profiles';
  ELSE
    RAISE NOTICE 'Колонка updated_at уже существует в таблице profiles';
  END IF;
END $$;

-- 3. Обновляем существующие записи, чтобы установить updated_at
UPDATE profiles
SET updated_at = created_at
WHERE updated_at IS NULL;

-- 4. Создаем или обновляем триггер для автообновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_updated_at ON profiles;
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 5. Создаем или обновляем триггер для автоматического создания профилей
-- Сначала удаляем существующие, если они есть
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Создаем функцию для автоматического создания профиля
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name, credits, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name', ''),
    10,
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Создаем триггер для вызова функции
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- 6. Настраиваем правильные политики RLS для таблицы profiles
-- Включаем RLS для таблицы profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Удаляем существующие политики
DROP POLICY IF EXISTS "Пользователи могут создавать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут просматривать свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Пользователи могут обновлять свои профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может просматривать все профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может создавать профили" ON public.profiles;
DROP POLICY IF EXISTS "Сервис может обновлять профили" ON public.profiles;

-- Создаем политику для создания профиля (анонимные и аутентифицированные пользователи)
CREATE POLICY "Пользователи могут создавать свои профили"
ON public.profiles FOR INSERT 
TO authenticated, anon
WITH CHECK (auth.uid() = id);

-- Создаем политику для чтения профиля
CREATE POLICY "Пользователи могут просматривать свои профили"
ON public.profiles FOR SELECT 
TO authenticated, anon
USING (auth.uid() = id);

-- Создаем политику для обновления профиля
CREATE POLICY "Пользователи могут обновлять свои профили"
ON public.profiles FOR UPDATE 
TO authenticated
USING (auth.uid() = id);

-- Создаем политики для сервисной роли
CREATE POLICY "Сервис может просматривать все профили"
ON public.profiles FOR SELECT 
TO service_role
USING (true);

CREATE POLICY "Сервис может создавать профили"
ON public.profiles FOR INSERT 
TO service_role
WITH CHECK (true);

CREATE POLICY "Сервис может обновлять профили"
ON public.profiles FOR UPDATE 
TO service_role
USING (true);

-- 7. Проверяем наличие конкретного профиля или создаем его
DO $$
DECLARE
  user_id_1 UUID := '512bf940-fd5a-4e90-a10c-c50f4c4aa8d3'::UUID;
  user_id_2 UUID := '085f7aba-5f49-4231-a28d-c6c520c07b5c'::UUID;
  email_1 TEXT := '<EMAIL>';
  profile_exists BOOLEAN;
BEGIN
  -- Проверяем существует ли профиль для первого ID
  SELECT EXISTS (
    SELECT 1 FROM profiles WHERE id = user_id_1
  ) INTO profile_exists;
  
  -- Если профиль не существует, создаем его
  IF NOT profile_exists THEN
    INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
    VALUES (user_id_1, email_1, 'User', 10, NOW(), NOW());
    RAISE NOTICE 'Создан профиль для пользователя: %', user_id_1;
  ELSE
    RAISE NOTICE 'Профиль для пользователя % уже существует', user_id_1;
  END IF;
  
  -- Проверяем существует ли профиль для второго ID
  SELECT EXISTS (
    SELECT 1 FROM profiles WHERE id = user_id_2
  ) INTO profile_exists;
  
  -- Если профиль не существует, создаем его
  IF NOT profile_exists THEN
    INSERT INTO profiles (id, email, name, credits, created_at, updated_at)
    VALUES (user_id_2, email_1, 'User', 10, NOW(), NOW());
    RAISE NOTICE 'Создан профиль для пользователя: %', user_id_2;
  ELSE
    RAISE NOTICE 'Профиль для пользователя % уже существует', user_id_2;
  END IF;
END $$;

-- 8. Вывод информации о применении политик
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public' AND tablename = 'profiles'
ORDER BY tablename, policyname; 

-- 9. Настраиваем политики RLS для таблицы user_credits
-- Временно отключаем RLS для таблицы user_credits
ALTER TABLE public.user_credits DISABLE ROW LEVEL SECURITY;

-- Удаляем существующие политики
DROP POLICY IF EXISTS "Пользователи могут просматривать свои кредиты" ON user_credits;
DROP POLICY IF EXISTS "Пользователи могут добавлять свои кредиты" ON user_credits;
DROP POLICY IF EXISTS "Service role может просматривать кредиты" ON user_credits;
DROP POLICY IF EXISTS "Service role может создавать кредиты" ON user_credits;

-- Включаем RLS для таблицы user_credits
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;

-- Политика для просмотра кредитов пользователями
CREATE POLICY "Пользователи могут просматривать свои кредиты" 
ON user_credits FOR SELECT 
TO authenticated 
USING (auth.uid() = user_id);

-- Политика для добавления кредитов пользователями
CREATE POLICY "Пользователи могут добавлять свои кредиты" 
ON user_credits FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid() = user_id);

-- Политики для service_role
CREATE POLICY "Service role может просматривать кредиты" 
ON user_credits FOR SELECT 
TO service_role 
USING (true);

CREATE POLICY "Service role может создавать кредиты" 
ON user_credits FOR INSERT 
TO service_role 
WITH CHECK (true);

-- Вывод информации о применении политик для user_credits
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public' AND tablename = 'user_credits'
ORDER BY tablename, policyname; 