import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next'; // Import useTranslation

const HeroSection = () => {
  const { t } = useTranslation(); // Initialize useTranslation
  return (
    <section className="min-h-[57vh] flex flex-col items-center justify-center relative overflow-hidden px-6">
      <div className="absolute inset-0 dotted-bg opacity-30" />
      
      <div className="relative z-10 max-w-4xl mx-auto text-center">
        <div className="inline-block mb-3 px-4 py-1 rounded-full bg-foreground/5 border border-border">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
            <span className="text-sm font-medium text-foreground/80">{t('landing.introducing')}</span>
          </div>
        </div>
        
        <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tight">
          <span className="text-gradient-foreground">{t('landing.hero.title')}</span>
        </h1>
        
        <p className="text-lg md:text-xl text-foreground/80 mb-8 max-w-2xl mx-auto">
          {t('landing.subtitle')}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-primary hover:bg-primary/90" asChild>
            <Link to="/login">
              {t('landing.getStarted')}
              <ArrowRight className="ml-2" size={18} />
            </Link>
          </Button>
          <Button size="lg" variant="outline" className="border-border hover:bg-foreground/5" asChild>
            <Link to="/dashboard">
              {t('landing.viewDemos')}
            </Link>
          </Button>
        </div>
      </div>
      
      <div className="absolute bottom-12 left-0 right-0 flex justify-center animate-bounce">
        <div className="w-8 h-12 rounded-full border-2 border-border flex items-start justify-center p-2">
          <div className="w-1 h-2 bg-foreground/60 rounded-full animate-pulse-slow"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
