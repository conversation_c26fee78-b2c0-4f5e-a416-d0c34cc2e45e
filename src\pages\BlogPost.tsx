import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, Link, Navigate } from 'react-router-dom';
import { Calendar, User, Clock, ArrowLeft, Share2, BookOpen } from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import SEOHead from '@/components/SEOHead';

// Типы для статей блога (дублируем из Blog.tsx)
interface BlogPost {
  id: string;
  title: string;
  titleEn: string;
  excerpt: string;
  excerptEn: string;
  content: string;
  contentEn: string;
  author: string;
  date: string;
  readTime: number;
  image?: string;
  tags: string[];
  published: boolean;
}

// Реальные статьи для блога (синхронизируем с Blog.tsx)
const mockPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Как создать максимально крутое видео с помощью Veo3',
    titleEn: 'How to Create Amazing Videos with Veo3',
    excerpt: 'Полное руководство по использованию Veo3 - самой продвинутой модели генерации видео от Google. Узнайте секреты создания профессиональных видео с аудио.',
    excerptEn: 'Complete guide to using Veo3 - Google\'s most advanced video generation model. Learn the secrets of creating professional videos with audio.',
    content: `
      <h2>Что такое Veo3 и почему это революция</h2>
      <p>Veo3 от Google - это прорыв в области генерации видео с помощью ИИ. Эта модель не просто создает видео, она генерирует полноценные 8-секундные ролики с профессиональным звуковым сопровождением, включая звуковые эффекты и даже голосовое сопровождение.</p>

      <h3>Ключевые преимущества Veo3:</h3>
      <ul>
        <li><strong>Высочайшее качество:</strong> Разрешение до 1280p с невероятной детализацией</li>
        <li><strong>Автоматическое аудио:</strong> Генерация звуков, музыки и голоса</li>
        <li><strong>Понимание контекста:</strong> Модель отлично понимает сложные сцены</li>
        <li><strong>Стабильность движения:</strong> Плавные и реалистичные анимации</li>
      </ul>

      <h2>Пошаговое руководство по созданию видео</h2>

      <h3>Шаг 1: Подготовка промпта</h3>
      <p>Качественный промпт - основа успешного видео. Для Veo3 используйте следующую структуру:</p>
      <ul>
        <li>Описание сцены (что происходит)</li>
        <li>Стиль съемки (крупный план, панорама, движение камеры)</li>
        <li>Настроение и атмосфера</li>
        <li>Детали освещения и времени суток</li>
      </ul>

      <p><strong>Пример хорошего промпта:</strong><br>
      "Cinematic shot of a majestic eagle soaring over snow-capped mountains at golden hour, slow motion, dramatic lighting, professional wildlife documentary style"</p>

      <h3>Шаг 2: Настройка параметров</h3>
      <p>В Uma AI для Veo3 доступны следующие настройки:</p>
      <ul>
        <li><strong>Улучшение промпта:</strong> Включите для автоматической оптимизации</li>
        <li><strong>Генерация аудио:</strong> Оставьте включенной для полного эффекта</li>
        <li><strong>Соотношение сторон:</strong> Только 16:9 (оптимально для большинства платформ)</li>
      </ul>

      <h3>Шаг 3: Секреты профессиональных результатов</h3>
      <p>Чтобы получить максимум от Veo3, следуйте этим советам:</p>

      <h4>Для экшн-сцен:</h4>
      <ul>
        <li>Используйте слова "dynamic", "fast-paced", "energetic"</li>
        <li>Указывайте направление движения</li>
        <li>Добавляйте детали о скорости действия</li>
      </ul>

      <h4>Для атмосферных видео:</h4>
      <ul>
        <li>Описывайте освещение детально</li>
        <li>Используйте эмоциональные прилагательные</li>
        <li>Указывайте погодные условия</li>
      </ul>

      <h4>Для технических кадров:</h4>
      <ul>
        <li>Указывайте тип камеры и объектива</li>
        <li>Описывайте движение камеры</li>
        <li>Добавляйте профессиональные термины</li>
      </ul>

      <h2>Примеры успешных промптов</h2>

      <h3>Природа и пейзажи:</h3>
      <p>"Aerial drone shot of a pristine lake surrounded by autumn forest, morning mist rising from water, golden sunlight filtering through trees, peaceful and serene atmosphere"</p>

      <h3>Городские сцены:</h3>
      <p>"Time-lapse of busy city intersection at night, neon lights reflecting on wet pavement, cars and people moving in fast motion, cyberpunk aesthetic"</p>

      <h3>Портреты и люди:</h3>
      <p>"Close-up portrait of an elderly craftsman working with wood, warm workshop lighting, focused expression, hands in motion, documentary style"</p>

      <h2>Оптимизация стоимости</h2>
      <p>Veo3 стоит 1400 кредитов с аудио и 1280 без аудио. Чтобы получить максимальную отдачу:</p>
      <ul>
        <li>Тщательно продумывайте промпт перед генерацией</li>
        <li>Используйте функцию улучшения промпта</li>
        <li>Отключайте аудио только если оно точно не нужно</li>
        <li>Экспериментируйте с разными стилями описания</li>
      </ul>

      <h2>Заключение</h2>
      <p>Veo3 открывает невероятные возможности для создания профессионального видеоконтента. Следуя этим рекомендациям, вы сможете создавать видео, которые будут выглядеть как работа профессиональной студии. Помните: качество результата напрямую зависит от качества промпта и понимания возможностей модели.</p>
    `,
    contentEn: `
      <h2>What is Veo3 and Why It's Revolutionary</h2>
      <p>Veo3 from Google is a breakthrough in AI video generation. This model doesn't just create videos - it generates complete 8-second clips with professional audio accompaniment, including sound effects and even voiceovers.</p>

      <h3>Key Advantages of Veo3:</h3>
      <ul>
        <li><strong>Highest Quality:</strong> Up to 1280p resolution with incredible detail</li>
        <li><strong>Automatic Audio:</strong> Generation of sounds, music, and voice</li>
        <li><strong>Context Understanding:</strong> Excellent comprehension of complex scenes</li>
        <li><strong>Motion Stability:</strong> Smooth and realistic animations</li>
      </ul>

      <h2>Step-by-Step Video Creation Guide</h2>

      <h3>Step 1: Prompt Preparation</h3>
      <p>A quality prompt is the foundation of successful video. For Veo3, use this structure:</p>
      <ul>
        <li>Scene description (what's happening)</li>
        <li>Shooting style (close-up, panorama, camera movement)</li>
        <li>Mood and atmosphere</li>
        <li>Lighting and time of day details</li>
      </ul>

      <p><strong>Example of a good prompt:</strong><br>
      "Cinematic shot of a majestic eagle soaring over snow-capped mountains at golden hour, slow motion, dramatic lighting, professional wildlife documentary style"</p>

      <h3>Step 2: Parameter Settings</h3>
      <p>In Uma AI for Veo3, the following settings are available:</p>
      <ul>
        <li><strong>Prompt Enhancement:</strong> Enable for automatic optimization</li>
        <li><strong>Audio Generation:</strong> Keep enabled for full effect</li>
        <li><strong>Aspect Ratio:</strong> Only 16:9 (optimal for most platforms)</li>
      </ul>

      <h3>Step 3: Professional Results Secrets</h3>
      <p>To get the most out of Veo3, follow these tips:</p>

      <h4>For Action Scenes:</h4>
      <ul>
        <li>Use words like "dynamic", "fast-paced", "energetic"</li>
        <li>Specify direction of movement</li>
        <li>Add details about action speed</li>
      </ul>

      <h4>For Atmospheric Videos:</h4>
      <ul>
        <li>Describe lighting in detail</li>
        <li>Use emotional adjectives</li>
        <li>Specify weather conditions</li>
      </ul>

      <h4>For Technical Shots:</h4>
      <ul>
        <li>Specify camera and lens type</li>
        <li>Describe camera movement</li>
        <li>Add professional terminology</li>
      </ul>

      <h2>Examples of Successful Prompts</h2>

      <h3>Nature and Landscapes:</h3>
      <p>"Aerial drone shot of a pristine lake surrounded by autumn forest, morning mist rising from water, golden sunlight filtering through trees, peaceful and serene atmosphere"</p>

      <h3>Urban Scenes:</h3>
      <p>"Time-lapse of busy city intersection at night, neon lights reflecting on wet pavement, cars and people moving in fast motion, cyberpunk aesthetic"</p>

      <h3>Portraits and People:</h3>
      <p>"Close-up portrait of an elderly craftsman working with wood, warm workshop lighting, focused expression, hands in motion, documentary style"</p>

      <h2>Cost Optimization</h2>
      <p>Veo3 costs 1400 credits with audio and 1280 without audio. To get maximum value:</p>
      <ul>
        <li>Carefully think through your prompt before generation</li>
        <li>Use the prompt enhancement feature</li>
        <li>Disable audio only if definitely not needed</li>
        <li>Experiment with different description styles</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Veo3 opens incredible possibilities for creating professional video content. Following these recommendations, you'll be able to create videos that look like professional studio work. Remember: result quality directly depends on prompt quality and understanding the model's capabilities.</p>
    `,
    author: 'Uma AI Team',
    date: '2024-12-15',
    readTime: 8,
    image: '/veo3-banner.webm',
    tags: ['Veo3', 'Видео', 'Руководство', 'Google'],
    published: true
  },
  {
    id: '2',
    title: 'Полное руководство по Uma AI: от регистрации до профессиональных результатов',
    titleEn: 'Complete Uma AI Guide: From Registration to Professional Results',
    excerpt: 'Исчерпывающее руководство по использованию платформы Uma AI. Узнайте, как максимально эффективно использовать все возможности для генерации изображений, видео и текста.',
    excerptEn: 'Comprehensive guide to using the Uma AI platform. Learn how to make the most of all capabilities for generating images, videos, and text.',
    content: '<h2>Демо-статья</h2><p>Это демонстрационная статья. Отредактируйте её в админке блога.</p>',
    contentEn: '<h2>Demo Article</h2><p>This is a demo article. Edit it in the blog admin panel.</p>',
    author: 'Uma AI Team',
    date: '2024-12-10',
    readTime: 12,
    image: '/umaicon.png',
    tags: ['Руководство', 'Uma AI', 'Обучение', 'Платформа'],
    published: true
  },
  {
    id: '3',
    title: 'Секреты создания идеальных изображений с Flux Kontext и Ideogram',
    titleEn: 'Secrets of Creating Perfect Images with Flux Kontext and Ideogram',
    excerpt: 'Подробное сравнение лучших моделей генерации изображений на Uma AI. Узнайте, когда использовать Flux Kontext Pro/Max и Ideogram 3 для достижения профессиональных результатов.',
    excerptEn: 'Detailed comparison of the best image generation models on Uma AI. Learn when to use Flux Kontext Pro/Max and Ideogram 3 to achieve professional results.',
    content: '<h2>Демо-статья</h2><p>Это демонстрационная статья. Отредактируйте её в админке блога.</p>',
    contentEn: '<h2>Demo Article</h2><p>This is a demo article. Edit it in the blog admin panel.</p>',
    author: 'Uma AI Team',
    date: '2024-12-05',
    readTime: 10,
    image: '/FluxKontextMaxLight.webp',
    tags: ['Flux Kontext', 'Ideogram', 'Изображения', 'Сравнение'],
    published: true
  }
];

const BlogPost = () => {
  const { i18n } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    // Загрузка данных из localStorage (в реальном проекте - из API)
    setTimeout(() => {
      try {
        const savedPosts = localStorage.getItem('blog_posts');
        let allPosts = mockPosts;

        if (savedPosts) {
          allPosts = JSON.parse(savedPosts);
        }

        const foundPost = allPosts.find((p: BlogPost) => p.id === id && p.published);
        if (foundPost) {
          setPost(foundPost);
        } else {
          setNotFound(true);
        }
      } catch (error) {
        console.error('Error loading post:', error);
        setNotFound(true);
      }
      setLoading(false);
    }, 500);
  }, [id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return i18n.language === 'ru' 
      ? date.toLocaleDateString('ru-RU', { year: 'numeric', month: 'long', day: 'numeric' })
      : date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  const handleShare = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: i18n.language === 'ru' ? post.title : post.titleEn,
          text: i18n.language === 'ru' ? post.excerpt : post.excerptEn,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-black dark:via-gray-900/50 dark:to-gray-800/30">
        <Header />
        <div className="pt-24 pb-20 px-6">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4" />
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded mb-8" />
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (notFound) {
    return <Navigate to="/blog" replace />;
  }

  if (!post) {
    return null;
  }

  return (
    <>
      <SEOHead 
        title={`${i18n.language === 'ru' ? post.title : post.titleEn} - Uma AI Blog`}
        description={i18n.language === 'ru' ? post.excerpt : post.excerptEn}
        image={post.image}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 dark:from-black dark:via-gray-900/50 dark:to-gray-800/30">
        <Header />

        <article className="pt-24 pb-20 px-6">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Link to="/blog" className="inline-flex items-center gap-2 text-foreground/60 hover:text-foreground transition-colors mb-8">
              <ArrowLeft className="w-4 h-4" />
              {i18n.language === 'ru' ? 'Назад к блогу' : 'Back to blog'}
            </Link>
            
            {/* Article Header */}
            <header className="mb-8">
              <h1 className="text-3xl md:text-5xl font-bold mb-6 text-gradient leading-tight">
                {i18n.language === 'ru' ? post.title : post.titleEn}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 text-foreground/60 mb-6">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  {post.author}
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {formatDate(post.date)}
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  {post.readTime} {i18n.language === 'ru' ? 'мин чтения' : 'min read'}
                </div>
                <Button variant="ghost" size="sm" onClick={handleShare} className="ml-auto">
                  <Share2 className="w-4 h-4 mr-2" />
                  {i18n.language === 'ru' ? 'Поделиться' : 'Share'}
                </Button>
              </div>
              
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 text-sm bg-black/5 dark:bg-white/5 rounded-full text-foreground/60"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </header>
            
            {/* Featured Image */}
            {post.image && (
              <div className="mb-8 rounded-2xl overflow-hidden">
                {post.image.endsWith('.webm') || post.image.endsWith('.mp4') ? (
                  <video
                    src={post.image}
                    className="w-full h-64 md:h-96 object-cover"
                    autoPlay
                    loop
                    muted
                    playsInline
                    controls
                  />
                ) : (
                  <img
                    src={post.image}
                    alt={i18n.language === 'ru' ? post.title : post.titleEn}
                    className="w-full h-64 md:h-96 object-cover"
                  />
                )}
              </div>
            )}
            
            {/* Article Content */}
            <div 
              className="prose prose-lg max-w-none dark:prose-invert prose-headings:text-gradient prose-a:text-blue-600 dark:prose-a:text-blue-400"
              dangerouslySetInnerHTML={{ 
                __html: i18n.language === 'ru' ? post.content : post.contentEn 
              }}
            />
            
            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-black/10 dark:border-white/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-foreground/60">
                  <BookOpen className="w-4 h-4" />
                  <span>{i18n.language === 'ru' ? 'Статья прочитана' : 'Article read'}</span>
                </div>
                
                <Link to="/blog">
                  <Button variant="outline">
                    {i18n.language === 'ru' ? 'Больше статей' : 'More articles'}
                  </Button>
                </Link>
              </div>
            </footer>
          </div>
        </article>
        
        <Footer />
      </div>
    </>
  );
};

export default BlogPost;
