
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next'; // Import useTranslation

const CtaSection = () => {
  const { t } = useTranslation(); // Initialize useTranslation
  return (
    <section className="py-20 px-6 relative">
      <div className="max-w-4xl mx-auto bg-background dark:bg-card rounded-2xl p-12 text-center relative overflow-hidden border border-black/10 dark:border-white/10 shadow-lg">
        <div className="relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gradient">{t('landing.ctaTitle')}</h2>
          
          <p className="text-lg text-foreground/80 mb-8 max-w-2xl mx-auto">
            {t('landing.ctaSubtitle')}
          </p>
          
          <Button size="lg" className="bg-primary hover:bg-primary/80 text-primary-foreground" asChild>
            <Link to="/login">
              {t('landing.getStarted')}
              <ArrowRight className="ml-2" size={18} />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
