import React from 'react'; // Import React
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import './i18n'; // Import the i18n configuration
import { clearCache } from './assets/no-cache'; // Импорт функции очистки кэша

// Активируем очистку кэша при загрузке приложения
clearCache();

createRoot(document.getElementById("root")!).render(
  <React.StrictMode> {/* Optional but recommended */}
    <React.Suspense fallback="Loading..."> {/* Wrap App in Suspense */}
      <App />
    </React.Suspense>
  </React.StrictMode>
);
