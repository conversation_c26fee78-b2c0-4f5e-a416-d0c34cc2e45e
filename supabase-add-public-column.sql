-- Добавляет поле public во все таблицы генераций
ALTER TABLE image_generations ADD COLUMN IF NOT EXISTS public boolean NOT NULL DEFAULT true;
ALTER TABLE video_generations ADD COLUMN IF NOT EXISTS public boolean NOT NULL DEFAULT true;
ALTER TABLE text_generations ADD COLUMN IF NOT EXISTS public boolean NOT NULL DEFAULT true;
-- До<PERSON>авить необходимые поля для синхронизации Dashboard с БД (если их нет):

ALTER TABLE image_generations ADD COLUMN IF NOT EXISTS public boolean DEFAULT true;
ALTER TABLE image_generations ADD COLUMN IF NOT EXISTS likes integer DEFAULT 0;
ALTER TABLE image_generations ADD COLUMN IF NOT EXISTS image_urls text[];

-- Сделать все существующие записи публичными:
UPDATE image_generations SET public = true WHERE public IS NULL;

-- Проставить пустой массив для image_urls, если поле есть, но пустое:
UPDATE image_generations SET image_urls = ARRAY[]::text[] WHERE image_urls IS NULL;