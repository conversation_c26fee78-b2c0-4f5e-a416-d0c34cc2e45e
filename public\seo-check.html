<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Check - UMA.AI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .check-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>SEO Check для UMA.AI</h1>
    <div id="results"></div>

    <script>
        function checkSEO() {
            const results = document.getElementById('results');
            const checks = [];

            // Проверяем title
            const title = document.title;
            if (title && title !== 'UMA.AI' && !title.includes('Lovable')) {
                checks.push({
                    type: 'success',
                    message: `✅ Title корректный: "${title}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ Title некорректный или содержит "Lovable": "${title}"`
                });
            }

            // Проверяем description
            const description = document.querySelector('meta[name="description"]');
            if (description && description.content && !description.content.includes('Lovable')) {
                checks.push({
                    type: 'success',
                    message: `✅ Description корректный: "${description.content}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ Description некорректный или содержит "Lovable"`
                });
            }

            // Проверяем Open Graph
            const ogTitle = document.querySelector('meta[property="og:title"]');
            const ogDescription = document.querySelector('meta[property="og:description"]');
            const ogImage = document.querySelector('meta[property="og:image"]');

            if (ogTitle && !ogTitle.content.includes('Lovable')) {
                checks.push({
                    type: 'success',
                    message: `✅ OG Title корректный: "${ogTitle.content}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ OG Title некорректный или содержит "Lovable"`
                });
            }

            if (ogDescription && !ogDescription.content.includes('Lovable')) {
                checks.push({
                    type: 'success',
                    message: `✅ OG Description корректный: "${ogDescription.content}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ OG Description некорректный или содержит "Lovable"`
                });
            }

            if (ogImage && ogImage.content.includes('umaai.site')) {
                checks.push({
                    type: 'success',
                    message: `✅ OG Image корректный: "${ogImage.content}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ OG Image некорректный`
                });
            }

            // Проверяем Twitter Card
            const twitterTitle = document.querySelector('meta[name="twitter:title"]');
            const twitterDescription = document.querySelector('meta[name="twitter:description"]');

            if (twitterTitle && !twitterTitle.content.includes('Lovable')) {
                checks.push({
                    type: 'success',
                    message: `✅ Twitter Title корректный: "${twitterTitle.content}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ Twitter Title некорректный или содержит "Lovable"`
                });
            }

            // Проверяем canonical URL
            const canonical = document.querySelector('link[rel="canonical"]');
            if (canonical && canonical.href.includes('umaai.site')) {
                checks.push({
                    type: 'success',
                    message: `✅ Canonical URL корректный: "${canonical.href}"`
                });
            } else {
                checks.push({
                    type: 'error',
                    message: `❌ Canonical URL некорректный`
                });
            }

            // Отображаем результаты
            results.innerHTML = checks.map(check => 
                `<div class="check-item ${check.type}">${check.message}</div>`
            ).join('');

            // Общий результат
            const errorCount = checks.filter(c => c.type === 'error').length;
            const successCount = checks.filter(c => c.type === 'success').length;
            
            results.innerHTML += `
                <div class="check-item info">
                    <strong>Итого: ${successCount} успешных проверок, ${errorCount} ошибок</strong>
                </div>
            `;
        }

        // Запускаем проверку при загрузке страницы
        window.onload = checkSEO;
    </script>
</body>
</html>
