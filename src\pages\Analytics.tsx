import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '../components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Activity,
  BarChart3,
  PieChart,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart as RechartsPieChart, Cell } from 'recharts';

interface AnalyticsData {
  summary: {
    total_payments_rubles: number;
    total_payments_credits: number;
    total_expenses_credits: number;
    total_costs_dollars: number;
    total_costs_rubles: number;
    profit_rubles: number;
    profit_margin: number;
  };
  model_stats: Array<{
    name: string;
    type: string;
    usage_count: number;
    total_cost_credits: number;
    revenue_credits: number;
    profit_credits: number;
    profit_margin: number;
  }>;
  user_stats: {
    total_users: number;
    paying_users: number;
    active_users: number;
    conversion_rate: number;
  };
  payments_by_day: Record<string, number>;
  recent_transactions: {
    payments: Array<any>;
    expenses: Array<any>;
  };
}

const Analytics: React.FC = () => {
  const { user } = useAuth();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [usingMockData, setUsingMockData] = useState(false);

  // Проверяем секретный доступ
  const hasSecretAccess = sessionStorage.getItem('secretAccess') === 'true';

  useEffect(() => {
    if (!hasSecretAccess && !user) {
      setError('Доступ запрещен. Требуется секретный код.');
      setLoading(false);
      return;
    }

    fetchAnalytics();
  }, [hasSecretAccess, user]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // Пытаемся получить данные из API
      try {
        let token = 'secret-access';
        if (user) {
          try {
            const session = await user.getSession?.() || user.session;
            token = session?.access_token || user.access_token || 'secret-access';
          } catch (e) {
            console.log('Could not get user token, using secret access');
          }
        }

        const response = await fetch('/api/analytics', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const analyticsData = await response.json();
          setData(analyticsData);
          setError(null);
          setUsingMockData(false);
          return;
        } else {
          console.log('API response not ok:', response.status, response.statusText);
        }
      } catch (apiError) {
        console.log('API недоступен, используем встроенные данные:', apiError);
      }

      // Правильные расчеты с учетом формулы ценообразования:
      // API цена × 1.5 (наценка) × 100 ($ в кредиты) ÷ 2 (кредиты в рубли)
      // Себестоимость: API цена × 50₽ (курс $1 = 50₽, так как 1$ = 100 кредитов = 50₽)

      const generations = [
        // Видео генерации
        {
          model: 'veo3-audio-on',
          type: 'video',
          duration: 8,
          api_cost_dollars: 6.0, // 8 сек * $0.75
          cost_credits: 1280, // (6.0 * 1.5 * 100) = 900 кредитов, но у вас 1280 - значит наценка больше
          cost_rubles: 640, // 1280 ÷ 2
          api_cost_rubles: 300 // $6.0 × 50₽
        },
        {
          model: 'kling-v2.0',
          type: 'video',
          duration: 5,
          api_cost_dollars: 1.4, // 5 сек * $0.28
          cost_credits: 320, // (1.4 * 1.5 * 100) = 210, округлим до 320 для прибыли
          cost_rubles: 160, // 320 ÷ 2
          api_cost_rubles: 70 // $1.4 × 50₽
        },
        {
          model: 'kling-v1.6-pro',
          type: 'video',
          duration: 5,
          api_cost_dollars: 0.475, // 5 сек * $0.095
          cost_credits: 120, // (0.475 * 1.5 * 100) = 71, округлим до 120
          cost_rubles: 60, // 120 ÷ 2
          api_cost_rubles: 23.75 // $0.475 × 50₽
        },
        {
          model: 'ray-flash-2-540p',
          type: 'video',
          duration: 5,
          api_cost_dollars: 0.165, // 5 сек * $0.033
          cost_credits: 50, // (0.165 * 1.5 * 100) = 25, округлим до 50
          cost_rubles: 25, // 50 ÷ 2
          api_cost_rubles: 8.25 // $0.165 × 50₽
        },

        // Изображения с правильными расчетами
        {
          model: 'flux-kontext-max',
          type: 'image',
          count: 1,
          api_cost_dollars: 0.08, // $0.08 за изображение
          cost_credits: 25, // (0.08 * 1.5 * 100) = 12, но округлим до 25 для прибыли
          cost_rubles: 12.5, // 25 ÷ 2
          api_cost_rubles: 4 // $0.08 × 50₽
        },
        {
          model: 'flux-kontext-pro',
          type: 'image',
          count: 3,
          api_cost_dollars: 0.12, // 3 × $0.04
          cost_credits: 30, // (0.12 * 1.5 * 100) = 18, округлим до 30
          cost_rubles: 15, // 30 ÷ 2
          api_cost_rubles: 6 // $0.12 × 50₽
        },
        {
          model: 'ideogram-v3-turbo',
          type: 'image',
          count: 2,
          api_cost_dollars: 0.06, // 2 × $0.03
          cost_credits: 20, // (0.06 * 1.5 * 100) = 9, округлим до 20
          cost_rubles: 10, // 20 ÷ 2
          api_cost_rubles: 3 // $0.06 × 50₽
        },
        {
          model: 'minimax/image-01',
          type: 'image',
          count: 5,
          api_cost_dollars: 0.05, // 5 × $0.01
          cost_credits: 15, // (0.05 * 1.5 * 100) = 7.5, округлим до 15
          cost_rubles: 7.5, // 15 ÷ 2
          api_cost_rubles: 2.5 // $0.05 × 50₽
        },
      ];

      // Считаем общие метрики
      const totalRevenue = generations.reduce((sum, gen) => sum + gen.cost_rubles, 0);
      const totalApiCosts = generations.reduce((sum, gen) => sum + gen.api_cost_rubles, 0);
      const totalProfit = totalRevenue - totalApiCosts;
      const overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

      // Статистика по моделям
      const modelStats = generations.map(gen => ({
        name: gen.model,
        type: gen.type,
        usage_count: gen.type === 'video' ? 1 : gen.count,
        total_cost_rubles: Math.round(gen.api_cost_rubles * 100) / 100,
        revenue_rubles: Math.round(gen.cost_rubles * 100) / 100,
        profit_rubles: Math.round((gen.cost_rubles - gen.api_cost_rubles) * 100) / 100,
        profit_margin: Math.round(((gen.cost_rubles - gen.api_cost_rubles) / gen.cost_rubles) * 10000) / 100
      })).sort((a, b) => b.profit_rubles - a.profit_rubles);

      const builtInData = {
        summary: {
          total_payments_rubles: Math.round(totalRevenue),
          total_payments_credits: Math.round(totalRevenue * 2), // 1₽ = 2 кредита
          total_expenses_credits: Math.round(totalRevenue * 2), // Потрачено столько же сколько заработано
          total_costs_dollars: Math.round(totalApiCosts / 100 * 100) / 100, // API costs в долларах
          total_costs_rubles: Math.round(totalApiCosts),
          profit_rubles: Math.round(totalProfit),
          profit_margin: Math.round(overallMargin * 100) / 100,
        },
        model_stats: modelStats,
        user_stats: {
          total_users: 12,
          paying_users: 8,
          active_users: 8,
          conversion_rate: 66.7
        },
        payments_by_day: {
          '2025-01-07': 160, // Kling v2.0
          '2025-01-06': 60, // Kling Pro
          '2025-01-05': 640, // Veo3
          '2025-01-04': 25, // Ray Flash
          '2025-01-03': 45 // Изображения
        },
        recent_transactions: {
          payments: [
            { id: 1, user_id: 'user-1', amount: 1280, created_at: new Date().toISOString(), description: 'Пополнение 640₽' },
            { id: 2, user_id: 'user-2', amount: 320, created_at: new Date(Date.now() - 86400000).toISOString(), description: 'Пополнение 160₽' },
            { id: 3, user_id: 'user-3', amount: 120, created_at: new Date(Date.now() - 172800000).toISOString(), description: 'Пополнение 60₽' },
            { id: 4, user_id: 'user-4', amount: 50, created_at: new Date(Date.now() - 259200000).toISOString(), description: 'Пополнение 25₽' },
            { id: 5, user_id: 'user-5', amount: 90, created_at: new Date(Date.now() - 345600000).toISOString(), description: 'Пополнение 45₽' }
          ],
          expenses: [
            { id: 1, user_id: 'user-1', amount: -1280, created_at: new Date().toISOString(), description: 'Генерация видео Veo3 8 сек с аудио' },
            { id: 2, user_id: 'user-2', amount: -320, created_at: new Date(Date.now() - 86400000).toISOString(), description: 'Генерация видео Kling v2.0 5 сек' },
            { id: 3, user_id: 'user-3', amount: -120, created_at: new Date(Date.now() - 172800000).toISOString(), description: 'Генерация видео Kling Pro 5 сек' },
            { id: 4, user_id: 'user-4', amount: -50, created_at: new Date(Date.now() - 259200000).toISOString(), description: 'Генерация видео Ray Flash 5 сек' },
            { id: 5, user_id: 'user-5', amount: -25, created_at: new Date(Date.now() - 345600000).toISOString(), description: 'Генерация изображения Flux Max' }
          ]
        }
      };

      // Имитируем задержку
      await new Promise(resolve => setTimeout(resolve, 1000));

      setData(builtInData);
      setError(null);
      setUsingMockData(true);
    } catch (err) {
      console.error('Analytics fetch error:', err);
      setError(err instanceof Error ? err.message : 'Неизвестная ошибка');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: 'RUB' | 'USD' = 'RUB') => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ru-RU').format(num);
  };

  if (!hasSecretAccess && !user) {
    return (
      <div className="min-h-screen bg-uma-black flex items-center justify-center">
        <Card className="w-96 bg-uma-dark border-uma-purple/20">
          <CardHeader>
            <CardTitle className="text-white text-center">🔐 Секретная аналитика</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-400 text-center">
              Для доступа к аналитике введите секретный код
            </p>

            <div className="bg-uma-black/50 p-4 rounded-lg border border-uma-purple/20">
              <p className="text-sm text-gray-300 mb-2 text-center">Konami Code:</p>
              <div className="flex justify-center space-x-1 text-xs">
                <span className="bg-uma-purple/20 px-2 py-1 rounded">↑</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">↑</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">↓</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">↓</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">←</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">→</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">←</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">→</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">B</span>
                <span className="bg-uma-purple/20 px-2 py-1 rounded">A</span>
              </div>
            </div>

            <div className="text-xs text-gray-500 text-center">
              <p>Или используйте консоль браузера:</p>
              <code className="bg-gray-800 px-2 py-1 rounded text-green-400">
                sessionStorage.setItem('secretAccess', 'true')
              </code>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-uma-black flex items-center justify-center">
        <div className="flex items-center space-x-2 text-white">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>Загрузка аналитики...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-uma-black flex items-center justify-center">
        <Card className="w-96 bg-uma-dark border-red-500/20">
          <CardHeader>
            <CardTitle className="text-red-400 text-center">Ошибка</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 text-center mb-4">{error}</p>
            <Button onClick={fetchAnalytics} className="w-full">
              Попробовать снова
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  // Подготавливаем данные для графиков
  const paymentsChartData = Object.entries(data.payments_by_day)
    .map(([date, amount]) => ({
      date,
      amount: Math.round(amount)
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(-30); // последние 30 дней

  const modelChartData = data.model_stats.slice(0, 10).map(model => ({
    name: model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name,
    usage: model.usage_count,
    profit: Math.round(model.profit_credits),
    revenue: Math.round(model.revenue_credits)
  }));

  const COLORS = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#EC4899', '#6366F1', '#84CC16'];

  return (
    <div className="min-h-screen bg-uma-black text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Заголовок */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Аналитика UMA.AI
              {usingMockData && (
                <span className="ml-3 text-sm bg-yellow-600 text-yellow-100 px-2 py-1 rounded-full">
                  DEMO данные
                </span>
              )}
            </h1>
            <p className="text-gray-400">
              Секретная панель администратора
              {usingMockData && (
                <span className="ml-2 text-yellow-400">
                  • Используются тестовые данные для демонстрации
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSensitiveData(!showSensitiveData)}
              className="border-uma-purple/20 text-white hover:bg-uma-purple/10"
            >
              {showSensitiveData ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {showSensitiveData ? 'Скрыть суммы' : 'Показать суммы'}
            </Button>
            <Button onClick={fetchAnalytics} size="sm" className="bg-uma-purple hover:bg-uma-purple/80">
              <RefreshCw className="h-4 w-4 mr-2" />
              Обновить
            </Button>
          </div>
        </div>

        {/* Основные метрики */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-uma-dark border-uma-purple/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Общие пополнения</CardTitle>
              <DollarSign className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {showSensitiveData ? formatCurrency(data.summary.total_payments_rubles) : '***'}
              </div>
              <p className="text-xs text-gray-400">
                {formatNumber(data.summary.total_payments_credits)} кредитов
              </p>
            </CardContent>
          </Card>

          <Card className="bg-uma-dark border-uma-purple/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Себестоимость</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {showSensitiveData ? formatCurrency(data.summary.total_costs_rubles) : '***'}
              </div>
              <p className="text-xs text-gray-400">
                ${data.summary.total_costs_dollars}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-uma-dark border-uma-purple/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Прибыль</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {showSensitiveData ? formatCurrency(data.summary.profit_rubles) : '***'}
              </div>
              <p className="text-xs text-gray-400">
                Маржа: {data.summary.profit_margin.toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card className="bg-uma-dark border-uma-purple/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Пользователи</CardTitle>
              <Users className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {formatNumber(data.user_stats.total_users)}
              </div>
              <p className="text-xs text-gray-400">
                Конверсия: {data.user_stats.conversion_rate.toFixed(1)}%
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Детальная аналитика */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-uma-dark border-uma-purple/20">
            <TabsTrigger value="overview" className="data-[state=active]:bg-uma-purple">
              Обзор
            </TabsTrigger>
            <TabsTrigger value="models" className="data-[state=active]:bg-uma-purple">
              Модели
            </TabsTrigger>
            <TabsTrigger value="users" className="data-[state=active]:bg-uma-purple">
              Пользователи
            </TabsTrigger>
            <TabsTrigger value="transactions" className="data-[state=active]:bg-uma-purple">
              Транзакции
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* График пополнений */}
            <Card className="bg-uma-dark border-uma-purple/20">
              <CardHeader>
                <CardTitle className="text-white">Пополнения по дням (последние 30 дней)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={paymentsChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="date" stroke="#9CA3AF" />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1F2937', 
                        border: '1px solid #8B5CF6',
                        borderRadius: '8px'
                      }}
                      labelStyle={{ color: '#F3F4F6' }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="amount" 
                      stroke="#8B5CF6" 
                      strokeWidth={2}
                      dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="models" className="space-y-6">
            {/* Статистика по моделям */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Топ моделей по использованию</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={modelChartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="name" stroke="#9CA3AF" angle={-45} textAnchor="end" height={80} />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          border: '1px solid #8B5CF6',
                          borderRadius: '8px'
                        }}
                      />
                      <Bar dataKey="usage" fill="#8B5CF6" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Прибыльность моделей</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={modelChartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="name" stroke="#9CA3AF" angle={-45} textAnchor="end" height={80} />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          border: '1px solid #8B5CF6',
                          borderRadius: '8px'
                        }}
                      />
                      <Bar dataKey="profit" fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Таблица моделей */}
            <Card className="bg-uma-dark border-uma-purple/20">
              <CardHeader>
                <CardTitle className="text-white">Детальная статистика моделей</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-400">Модель</th>
                        <th className="text-left py-2 text-gray-400">Тип</th>
                        <th className="text-right py-2 text-gray-400">Использований</th>
                        <th className="text-right py-2 text-gray-400">Доход (₽)</th>
                        <th className="text-right py-2 text-gray-400">Себестоимость (₽)</th>
                        <th className="text-right py-2 text-gray-400">Прибыль (₽)</th>
                        <th className="text-right py-2 text-gray-400">Маржа (%)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.model_stats.slice(0, 15).map((model, index) => (
                        <tr key={index} className="border-b border-gray-800">
                          <td className="py-2 text-white">
                            {model.name.length > 30 ? model.name.substring(0, 30) + '...' : model.name}
                          </td>
                          <td className="py-2">
                            <Badge variant="outline" className="text-xs">
                              {model.type}
                            </Badge>
                          </td>
                          <td className="text-right py-2 text-white">{formatNumber(model.usage_count)}</td>
                          <td className="text-right py-2 text-green-400">
                            {showSensitiveData ? `${formatNumber(Math.round(model.revenue_rubles))}₽` : '***₽'}
                          </td>
                          <td className="text-right py-2 text-red-400">
                            {showSensitiveData ? `${formatNumber(Math.round(model.total_cost_rubles))}₽` : '***₽'}
                          </td>
                          <td className="text-right py-2 text-white">
                            {showSensitiveData ? `${formatNumber(Math.round(model.profit_rubles))}₽` : '***₽'}
                          </td>
                          <td className="text-right py-2">
                            <span className={model.profit_margin > 0 ? 'text-green-400' : 'text-red-400'}>
                              {model.profit_margin.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            {/* Статистика пользователей */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Всего пользователей</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-white">
                    {formatNumber(data.user_stats.total_users)}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Платящие пользователи</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-400">
                    {formatNumber(data.user_stats.paying_users)}
                  </div>
                  <p className="text-sm text-gray-400">
                    {data.user_stats.conversion_rate.toFixed(1)}% конверсия
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Активные пользователи</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-400">
                    {formatNumber(data.user_stats.active_users)}
                  </div>
                  <p className="text-sm text-gray-400">
                    Создавали контент
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="transactions" className="space-y-6">
            {/* Последние транзакции */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Последние пополнения</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {data.recent_transactions.payments.slice(0, 20).map((payment, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-800">
                        <div>
                          <p className="text-sm text-white">
                            {showSensitiveData ? payment.user_id.substring(0, 8) + '...' : 'Пользователь'}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(payment.created_at).toLocaleDateString('ru-RU')}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-green-400">
                            {showSensitiveData ? `+${formatNumber(payment.amount)} кредитов` : '+*** кредитов'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-uma-dark border-uma-purple/20">
                <CardHeader>
                  <CardTitle className="text-white">Последние траты</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {data.recent_transactions.expenses.slice(0, 20).map((expense, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-800">
                        <div>
                          <p className="text-sm text-white">
                            {showSensitiveData ? expense.user_id.substring(0, 8) + '...' : 'Пользователь'}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(expense.created_at).toLocaleDateString('ru-RU')}
                          </p>
                          <p className="text-xs text-gray-500">
                            {expense.description?.substring(0, 30)}...
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-red-400">
                            {showSensitiveData ? `${formatNumber(expense.amount)} кредитов` : '-*** кредитов'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};



export default Analytics;
