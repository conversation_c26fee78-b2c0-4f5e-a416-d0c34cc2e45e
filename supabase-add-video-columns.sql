-- Добавляет отсутствующие столбцы в таблицу video_generations
-- Эти столбцы необходимы для сохранения параметров генерации видео

DO $$ 
BEGIN
  -- Проверяем и добавляем столбец duration
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'video_generations' 
      AND column_name = 'duration'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN duration INTEGER';
    RAISE NOTICE 'Столбец duration добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец duration уже существует в таблице video_generations';
  END IF;

  -- Проверяем и добавляем столбец quality
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'video_generations' 
      AND column_name = 'quality'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN quality TEXT';
    RAISE NOTICE 'Столбец quality добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец quality уже существует в таблице video_generations';
  END IF;

  -- Проверяем и добавляем столбец aspect_ratio
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'video_generations' 
      AND column_name = 'aspect_ratio'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN aspect_ratio TEXT';
    RAISE NOTICE 'Столбец aspect_ratio добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец aspect_ratio уже существует в таблице video_generations';
  END IF;

  -- Проверяем и добавляем столбец motion_mode
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'video_generations' 
      AND column_name = 'motion_mode'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN motion_mode TEXT';
    RAISE NOTICE 'Столбец motion_mode добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец motion_mode уже существует в таблице video_generations';
  END IF;

  -- Проверяем и добавляем столбец cost
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'video_generations'
      AND column_name = 'cost'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN cost INTEGER DEFAULT 0';
    RAISE NOTICE 'Столбец cost добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец cost уже существует в таблице video_generations';
  END IF;

  -- Проверяем и добавляем столбец thumbnail_url
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'video_generations'
      AND column_name = 'thumbnail_url'
  ) THEN
    EXECUTE 'ALTER TABLE video_generations ADD COLUMN thumbnail_url TEXT';
    RAISE NOTICE 'Столбец thumbnail_url добавлен в таблицу video_generations';
  ELSE
    RAISE NOTICE 'Столбец thumbnail_url уже существует в таблице video_generations';
  END IF;
END $$;

-- Обновляем все существующие записи, у которых cost равен NULL
UPDATE video_generations
SET cost = 0
WHERE cost IS NULL;

-- Добавляем комментарии к столбцам
COMMENT ON COLUMN video_generations.duration IS 'Длительность видео в секундах';
COMMENT ON COLUMN video_generations.quality IS 'Качество видео (например, 540p, 720p, 1080p)';
COMMENT ON COLUMN video_generations.aspect_ratio IS 'Соотношение сторон видео (например, 16:9, 1:1)';
COMMENT ON COLUMN video_generations.motion_mode IS 'Режим движения (normal, smooth)';
COMMENT ON COLUMN video_generations.cost IS 'Стоимость генерации видео в кредитах';
COMMENT ON COLUMN video_generations.thumbnail_url IS 'URL превью (миниатюры) видео';