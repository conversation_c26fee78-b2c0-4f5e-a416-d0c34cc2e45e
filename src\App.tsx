import React, { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth } from "@/context/AuthContext";
import { ThemeProvider } from "@/context/ThemeContext";
import { SidebarProvider } from "@/context/SidebarContext";

import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import TextGeneration from "./pages/TextGeneration";
import ImageGeneration from "./pages/ImageGeneration";
import VideoGeneration from "./pages/VideoGeneration";
import SpeechGeneration from "./pages/SpeechGeneration";
import History from "./pages/History";
import Profile from "./pages/Profile";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import InputDemo from "./pages/InputDemo";
import Callback from "./pages/auth/Callback";
import Terms from "./pages/terms";
import Privacy from "./pages/privacy";
import Refund from "./pages/refund";
import ServiceAgreement from "./pages/service-agreement";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import BlogAdmin from "./pages/BlogAdmin";
import PaymentCallback from "./pages/PaymentCallback";
import Analytics from "./pages/Analytics";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  
  // Проверяем секретный доступ при монтировании и принудительно обновляем состояние
  useEffect(() => {
    const checkSecretAccess = () => {
      const secretAccessEnabled = sessionStorage.getItem('secretAccess') === 'true';
      return secretAccessEnabled;
    };
    
    const hasAccess = checkSecretAccess();
    console.log('Secret access check:', hasAccess);
    
    // Если нет доступа, добавляем глобальный слушатель для хранилища сессии
    if (!hasAccess && !user) {
      const handleStorage = () => {
        const newAccessState = checkSecretAccess();
        if (newAccessState) {
          // Если получен секретный доступ, перезагружаем страницу
          window.location.reload();
        }
      };
      
      window.addEventListener('storage', handleStorage);
      return () => window.removeEventListener('storage', handleStorage);
    }
  }, [user]);
  
  if (isLoading) {
    return <div className="min-h-screen bg-uma-black flex items-center justify-center">
      <div className="animate-spin h-8 w-8 border-4 border-uma-purple border-t-transparent rounded-full"></div>
    </div>;
  }
  
  // Проверяем наличие секретного доступа напрямую при каждом рендере
  const secretAccessEnabled = sessionStorage.getItem('secretAccess') === 'true';
  
  if (!user && !secretAccessEnabled) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }
  
  return <>{children}</>;
};

const App = () => (
  <BrowserRouter>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
      <AuthProvider>
        <SidebarProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
            <Route path="/text" element={<ProtectedRoute><TextGeneration /></ProtectedRoute>} />
            <Route path="/image" element={<ProtectedRoute><ImageGeneration /></ProtectedRoute>} />
            <Route path="/video" element={<ProtectedRoute><VideoGeneration /></ProtectedRoute>} />
            <Route path="/speech" element={<ProtectedRoute><SpeechGeneration /></ProtectedRoute>} />
            <Route path="/history" element={<ProtectedRoute><History /></ProtectedRoute>} />
            <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
            <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
            <Route path="/analytics" element={<ProtectedRoute><Analytics /></ProtectedRoute>} />
            <Route path="/payment-callback" element={<ProtectedRoute><PaymentCallback /></ProtectedRoute>} />
            <Route path="/input-demo" element={<InputDemo />} />
            <Route path="/auth/callback" element={<Callback />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/refund" element={<Refund />} />
            <Route path="/service-agreement" element={<ServiceAgreement />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/blog/:id" element={<BlogPost />} />
            <Route path="/blog-admin" element={<ProtectedRoute><BlogAdmin /></ProtectedRoute>} />
            <Route path="*" element={<NotFound />} />
          </Routes>
          </TooltipProvider>
        </SidebarProvider>
      </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </BrowserRouter>
);

export default App;
