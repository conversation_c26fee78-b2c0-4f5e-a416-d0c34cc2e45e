import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Mic, MicOff, Play, Pause, RotateCcw, Upload } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { AudioRecorder, formatDuration, validateAudioFile, getAudioDuration } from '@/utils/audioUtils';
import { toast } from 'sonner';

interface VoiceRecorderProps {
  onRecordingComplete: (blob: Blob, duration: number) => void;
  onRecordingClear: () => void;
  className?: string;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onRecordingClear,
  className
}) => {
  const { t } = useTranslation();
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  
  const recorderRef = useRef<AudioRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const maxDuration = 5 * 60 * 1000; // 5 минут
  const minDuration = 10 * 1000; // 10 секунд

  useEffect(() => {
    // Проверяем поддержку MediaRecorder
    if (!window.MediaRecorder) {
      toast.error(t('speechGen.errors.microphoneAccess'));
      return;
    }

    // Инициализируем рекордер
    recorderRef.current = new AudioRecorder();

    return () => {
      // Очистка при размонтировании
      if (recorderRef.current) {
        recorderRef.current.cleanup();
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);

  // Запрос разрешения на микрофон
  const requestPermission = async () => {
    try {
      if (recorderRef.current) {
        await recorderRef.current.requestMicrophoneAccess();
        setHasPermission(true);
        toast.success('Доступ к микрофону получен');
      }
    } catch (error) {
      console.error('Permission denied:', error);
      setHasPermission(false);
      toast.error(t('speechGen.errors.microphoneAccess'));
    }
  };

  // Начать запись
  const startRecording = async () => {
    if (!recorderRef.current) return;

    try {
      if (hasPermission === null) {
        await requestPermission();
      }

      await recorderRef.current.startRecording();
      setIsRecording(true);
      setRecordingDuration(0);

      // Обновляем счетчик времени
      intervalRef.current = setInterval(() => {
        if (recorderRef.current) {
          const duration = recorderRef.current.getCurrentDuration();
          setRecordingDuration(duration);

          // Автоматическая остановка при достижении максимума
          if (duration >= maxDuration) {
            stopRecording();
          }
        }
      }, 100);

    } catch (error) {
      console.error('Recording error:', error);
      toast.error(t('speechGen.errors.microphoneAccess'));
      setIsRecording(false);
    }
  };

  // Остановить запись
  const stopRecording = async () => {
    if (!recorderRef.current || !isRecording) return;

    try {
      const recording = await recorderRef.current.stopRecording();
      
      setIsRecording(false);
      setAudioBlob(recording.blob);
      setAudioUrl(recording.url);
      setRecordingDuration(recording.duration);

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      onRecordingComplete(recording.blob, recording.duration);
      toast.success(t('speechGen.success.recorded'));

    } catch (error) {
      console.error('Stop recording error:', error);
      toast.error(error instanceof Error ? error.message : t('speechGen.errors.recording'));
      setIsRecording(false);
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  // Воспроизведение записи
  const togglePlayback = () => {
    if (!audioUrl) return;

    if (!audioRef.current) {
      audioRef.current = new Audio(audioUrl);
      audioRef.current.addEventListener('ended', () => {
        setIsPlaying(false);
      });
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // Очистить запись
  const clearRecording = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }

    setAudioBlob(null);
    setAudioUrl(null);
    setRecordingDuration(0);
    setIsPlaying(false);
    onRecordingClear();
  };

  // Загрузка файла
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateAudioFile(file);
    if (!validation.valid) {
      toast.error(validation.error);
      return;
    }

    try {
      const duration = await getAudioDuration(file);
      
      if (duration < minDuration) {
        toast.error(t('speechGen.errors.recordingTooShort'));
        return;
      }

      if (duration > maxDuration) {
        toast.error(t('speechGen.errors.recordingTooLong'));
        return;
      }

      const url = URL.createObjectURL(file);
      setAudioBlob(file);
      setAudioUrl(url);
      setRecordingDuration(duration);
      onRecordingComplete(file, duration);
      toast.success('Файл успешно загружен');

    } catch (error) {
      console.error('File upload error:', error);
      toast.error(t('speechGen.errors.invalidFile'));
    }
  };

  const progressPercentage = (recordingDuration / maxDuration) * 100;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Основные кнопки */}
      <div className="flex items-center gap-3">
        {!audioBlob ? (
          <>
            {/* Кнопка записи */}
            <Button
              onClick={isRecording ? stopRecording : startRecording}
              variant={isRecording ? "destructive" : "default"}
              size="lg"
              className={cn(
                "flex items-center gap-2 transition-all duration-200",
                isRecording && "animate-pulse"
              )}
              disabled={hasPermission === false}
            >
              {isRecording ? (
                <>
                  <MicOff size={20} />
                  {t('speechGen.recording.stop')}
                </>
              ) : (
                <>
                  <Mic size={20} />
                  {t('speechGen.recording.start')}
                </>
              )}
            </Button>

            {/* Кнопка загрузки файла */}
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <Upload size={20} />
              {t('speechGen.uploadAudio')}
            </Button>

            <input
              ref={fileInputRef}
              type="file"
              accept="audio/mp3,audio/wav,audio/m4a,audio/mpeg"
              onChange={handleFileUpload}
              className="hidden"
            />
          </>
        ) : (
          <>
            {/* Кнопка воспроизведения */}
            <Button
              onClick={togglePlayback}
              variant="default"
              size="lg"
              className="flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <Pause size={20} />
                  Пауза
                </>
              ) : (
                <>
                  <Play size={20} />
                  {t('speechGen.recording.preview')}
                </>
              )}
            </Button>

            {/* Кнопка перезаписи */}
            <Button
              onClick={clearRecording}
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <RotateCcw size={20} />
              {t('speechGen.recording.rerecord')}
            </Button>
          </>
        )}
      </div>

      {/* Индикатор записи */}
      {isRecording && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-red-500 font-medium flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              {t('speechGen.recording.recording')}
            </span>
            <span className="text-foreground/70">
              {t('speechGen.recording.duration', { 
                duration: formatDuration(recordingDuration) 
              })}
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="text-xs text-foreground/60 text-center">
            {t('speechGen.recording.maxDuration')}
          </div>
        </div>
      )}

      {/* Информация о записи */}
      {audioBlob && !isRecording && (
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <span className="text-foreground/70">
              {t('speechGen.recording.duration', { 
                duration: formatDuration(recordingDuration) 
              })}
            </span>
            <span className="text-green-600 font-medium">
              ✓ Готово к использованию
            </span>
          </div>
        </div>
      )}

      {/* Подсказки */}
      {!audioBlob && !isRecording && (
        <div className="text-xs text-foreground/60 space-y-1">
          <div>• {t('speechGen.recording.minDuration')}</div>
          <div>• {t('speechGen.recording.maxDuration')}</div>
          <div>• {t('speechGen.voiceCloning.supportedFormats')}MP3, WAV, M4A</div>
        </div>
      )}

      {/* Сообщение об отсутствии разрешения */}
      {hasPermission === false && (
        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
          <div className="text-sm text-destructive">
            {t('speechGen.errors.microphoneAccess')}
          </div>
          <Button
            onClick={requestPermission}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Запросить доступ
          </Button>
        </div>
      )}
    </div>
  );
};

export default VoiceRecorder;