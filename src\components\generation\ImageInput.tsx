import React, { useRef, useState } from 'react';
import { PlusIcon, XIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ImageInputProps {
  onImageSelect?: (imageDataUrl: string | null) => void;
  onImagesChange?: (images: string[]) => void;
  multiple?: boolean;
  value?: string[] | string | null;
  mode?: "default" | "style_reference";
}

const ImageInput: React.FC<ImageInputProps> = ({
  onImageSelect,
  onImagesChange,
  multiple = false,
  value,
  // mode = "default"
}) => {
  const [previews, setPreviews] = useState<string[]>(() => {
    if (Array.isArray(value)) return value;
    if (typeof value === "string" && value) return [value];
    return [];
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    const readers = files.map(file => {
      return new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.readAsDataURL(file);
      });
    });
    Promise.all(readers).then((dataUrls) => {
      let newPreviews = multiple ? [...previews, ...dataUrls] : [dataUrls[0]];
      // Ограничиваем максимум 3 картинки
      if (multiple) {
        newPreviews = newPreviews.slice(0, 3);
      }
      setPreviews(newPreviews);
      if (onImagesChange) onImagesChange(newPreviews);
      if (onImageSelect && !multiple) onImageSelect(dataUrls[0]);
    });
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const clearImage = (idx?: number) => {
    if (typeof idx === "number") {
      const newPreviews = previews.filter((_, i) => i !== idx);
      setPreviews(newPreviews);
      if (onImagesChange) onImagesChange(newPreviews);
    } else {
      setPreviews([]);
      if (onImagesChange) onImagesChange([]);
      if (onImageSelect) onImageSelect(null);
    }
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  // Синхронизация value <-> previews
  React.useEffect(() => {
    if (Array.isArray(value)) setPreviews(value);
    else if (typeof value === "string" && value) setPreviews([value]);
    else if (!value) setPreviews([]);
  }, [value]);

  return (
    <div className="relative inline-block">
      <input
        type="file"
        accept="image/*"
        multiple={multiple}
        onChange={handleImageSelect}
        ref={fileInputRef}
        className="hidden"
      />
      <div className="flex gap-1">
        {previews.map((preview, idx) => (
          <div className="relative group" key={idx}>
            <img
              src={preview}
              alt={`Selected ${idx + 1}`}
              className="w-10 h-10 rounded-lg object-cover border border-border"
            />
            <Button
              size="icon"
              variant="ghost"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-background border border-border opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => clearImage(idx)}
            >
              <XIcon size={12} />
            </Button>
          </div>
        ))}
        {previews.length < 3 && (
          <Button
            size="icon"
            variant="outline"
            className="h-10 w-10 rounded-lg bg-muted/50 flex items-center justify-center"
            onClick={() => fileInputRef.current?.click()}
          >
            <PlusIcon size={16} className="text-muted-foreground" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default ImageInput;