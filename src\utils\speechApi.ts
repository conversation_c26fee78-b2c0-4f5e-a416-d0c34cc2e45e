// Утилита для работы с Replicate Speech API
import { supabase } from './supabase';

export interface SpeechParams {
  text: string;
  voice_id: string;
  model: 'speech-02-turbo' | 'speech-02-hd' | 'elevenlabs-turbo-v2.5' | 'elevenlabs-multilingual-v2';
  userId: string;
  speed?: number;
  pitch?: number;
  volume?: number;
  emotion?: string;
  language_boost?: string;
  english_normalization?: boolean;
  sample_rate?: number;
  bitrate?: number;
  channel?: string;
  // ElevenLabs specific parameters
  stability?: number;
  similarity_boost?: number;
  style?: number;
  timestamps?: boolean;
}

export interface VoiceCloneParams {
  voice_file: string;
  model: 'speech-02-turbo' | 'speech-02-hd';
  accuracy?: number;
  need_noise_reduction?: boolean;
  need_volume_normalization?: boolean;
}

export interface SpeechResponse {
  prediction_id: string;
  status: string;
  audio_url: string | null;
}

export interface VoiceCloneResponse {
  voice_id: string;
  prediction_id?: string;
}

// Системные голоса из API
export const SYSTEM_VOICES = [
  { id: 'Wise_Woman', name: 'Wise Woman', category: 'female' },
  { id: 'Friendly_Person', name: 'Friendly Person', category: 'neutral' },
  { id: 'Inspirational_girl', name: 'Inspirational Girl', category: 'female' },
  { id: 'Deep_Voice_Man', name: 'Deep Voice Man', category: 'male' },
  { id: 'Calm_Woman', name: 'Calm Woman', category: 'female' },
  { id: 'Casual_Guy', name: 'Casual Guy', category: 'male' },
  { id: 'Lively_Girl', name: 'Lively Girl', category: 'female' },
  { id: 'Patient_Man', name: 'Patient Man', category: 'male' },
  { id: 'Young_Knight', name: 'Young Knight', category: 'male' },
  { id: 'Determined_Man', name: 'Determined Man', category: 'male' },
  { id: 'Lovely_Girl', name: 'Lovely Girl', category: 'female' },
  { id: 'Decent_Boy', name: 'Decent Boy', category: 'male' },
  { id: 'Imposing_Manner', name: 'Imposing Manner', category: 'neutral' },
  { id: 'Elegant_Man', name: 'Elegant Man', category: 'male' },
  { id: 'Abbess', name: 'Abbess', category: 'female' },
  { id: 'Sweet_Girl_2', name: 'Sweet Girl 2', category: 'female' },
  { id: 'Exuberant_Girl', name: 'Exuberant Girl', category: 'female' }
];

// Эмоции для генерации речи
export const EMOTIONS = [
  { value: 'auto', label: { ru: 'Автоматически', en: 'Auto' } },
  { value: 'neutral', label: { ru: 'Нейтральная', en: 'Neutral' } },
  { value: 'happy', label: { ru: 'Радостная', en: 'Happy' } },
  { value: 'sad', label: { ru: 'Грустная', en: 'Sad' } },
  { value: 'angry', label: { ru: 'Злая', en: 'Angry' } },
  { value: 'fearful', label: { ru: 'Испуганная', en: 'Fearful' } },
  { value: 'disgusted', label: { ru: 'Отвращение', en: 'Disgusted' } },
  { value: 'surprised', label: { ru: 'Удивленная', en: 'Surprised' } }
];

// Языки для усиления распознавания
export const LANGUAGE_BOOST_OPTIONS = [
  { value: 'None', label: { ru: 'Нет', en: 'None' } },
  { value: 'Automatic', label: { ru: 'Автоматически', en: 'Automatic' } },
  { value: 'Russian', label: { ru: 'Русский', en: 'Russian' } },
  { value: 'English', label: { ru: 'Английский', en: 'English' } },
  { value: 'Chinese', label: { ru: 'Китайский', en: 'Chinese' } },
  { value: 'Spanish', label: { ru: 'Испанский', en: 'Spanish' } },
  { value: 'French', label: { ru: 'Французский', en: 'French' } },
  { value: 'German', label: { ru: 'Немецкий', en: 'German' } },
  { value: 'Italian', label: { ru: 'Итальянский', en: 'Italian' } },
  { value: 'Japanese', label: { ru: 'Японский', en: 'Japanese' } },
  { value: 'Korean', label: { ru: 'Корейский', en: 'Korean' } }
];

// Функция для расчета стоимости генерации речи
export function calculateSpeechCost(text: string, model: string): number {
  if (!text || !model) return 0;

  const textLength = text.length;

  // Модели речи minimax
  if (model.includes("speech-02-turbo")) {
    const cost = Math.ceil(textLength * 0.1); // 0.1 кредита за символ, округление вверх
    return Math.max(5, cost); // Минимум 5 кредитов
  } else if (model.includes("speech-02-hd")) {
    const cost = Math.ceil(textLength * 0.2); // 0.2 кредита за символ, округление вверх
    return Math.max(5, cost); // Минимум 5 кредитов
  }

  // ElevenLabs модели через FAL API
  else if (model.includes("elevenlabs-turbo-v2.5")) {
    // 20 кредитов за 1000 символов
    const cost = Math.ceil((textLength / 1000) * 20);
    return Math.max(5, cost); // Минимум 5 кредитов
  } else if (model.includes("elevenlabs-multilingual-v2")) {
    // 40 кредитов за 1000 символов
    const cost = Math.ceil((textLength / 1000) * 40);
    return Math.max(5, cost); // Минимум 5 кредитов
  }

  return 0;
}

// Стоимость клонирования голоса
export const VOICE_CLONE_COST = 900;

// ElevenLabs голоса для FAL API
export const ELEVENLABS_VOICES = [
  { id: 'Rachel', name: 'Rachel', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { id: 'Aria', name: 'Aria', gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
  { id: 'Roger', name: 'Roger', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
  { id: 'Sarah', name: 'Sarah', gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' },
  { id: 'Laura', name: 'Laura', gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
  { id: 'Charlie', name: 'Charlie', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
  { id: 'George', name: 'George', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
  { id: 'Callum', name: 'Callum', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
  { id: 'River', name: 'River', gradient: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)' },
  { id: 'Liam', name: 'Liam', gradient: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)' },
  { id: 'Charlotte', name: 'Charlotte', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
  { id: 'Alice', name: 'Alice', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%)' },
  { id: 'Matilda', name: 'Matilda', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
  { id: 'Will', name: 'Will', gradient: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' },
  { id: 'Jessica', name: 'Jessica', gradient: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)' },
  { id: 'Eric', name: 'Eric', gradient: 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)' },
  { id: 'Chris', name: 'Chris', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
  { id: 'Brian', name: 'Brian', gradient: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)' },
  { id: 'Daniel', name: 'Daniel', gradient: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)' },
  { id: 'Lily', name: 'Lily', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
  { id: 'Bill', name: 'Bill', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%)' }
];



/**
 * Генерация речи через Replicate API
 */
export async function generateSpeech(params: SpeechParams): Promise<SpeechResponse> {
  console.log('Starting speech generation with params:', params);
  
  try {
    const response = await fetch('/api/replicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: `minimax/${params.model}`,
        input: {
          text: params.text,
          voice_id: params.voice_id,
          userId: params.userId,
          speed: params.speed || 1,
          pitch: params.pitch || 0,
          volume: params.volume || 1,
          emotion: params.emotion || 'auto',
          language_boost: params.language_boost || 'None',
          english_normalization: params.english_normalization || false,
          sample_rate: params.sample_rate || 32000,
          bitrate: params.bitrate || 128000,
          channel: params.channel || 'mono'
        }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Speech API Error (${response.status}): ${errorText}`);
      throw new Error(`Speech generation failed: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    console.log('Speech generation response:', data);
    
    // Для асинхронных задач Replicate возвращает prediction с status 'starting'
    if (data.id) {
      return {
        prediction_id: data.id,
        status: data.status,
        audio_url: data.output ? (Array.isArray(data.output) ? data.output[0] : data.output) : null
      };
    }

    throw new Error('No prediction ID received from API');
  } catch (error) {
    console.error('Error generating speech:', error);
    throw error;
  }
}

/**
 * Получение статуса prediction из Replicate
 */
export async function getPredictionStatus(predictionId: string): Promise<any> {
  try {
    const response = await fetch('/api/replicate/status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        predictionId: predictionId
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get prediction status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting prediction status:', error);
    throw error;
  }
}

/**
 * Клонирование голоса через Replicate API
 */
export async function cloneVoice(params: VoiceCloneParams): Promise<VoiceCloneResponse> {
  console.log('Starting voice cloning with params:', params);
  
  try {
    const response = await fetch('/api/replicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'minimax/voice-cloning',
        input: {
          voice_file: params.voice_file,
          model: params.model || 'speech-02-turbo',
          accuracy: params.accuracy || 0.7,
          need_noise_reduction: params.need_noise_reduction || false,
          need_volume_normalization: params.need_volume_normalization || false
        }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Voice cloning API Error (${response.status}): ${errorText}`);
      throw new Error(`Voice cloning failed: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    console.log('Voice cloning response:', data);
    
    if (!data.output) {
      throw new Error('No voice ID received from API');
    }

    return {
      voice_id: data.output,
      prediction_id: data.id
    };
  } catch (error) {
    console.error('Error cloning voice:', error);
    throw error;
  }
}

/**
 * Получение списка голосов пользователя
 */
export async function getUserVoices(userId: string) {
  try {
    const { data, error } = await supabase
      .from('voice_clones')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user voices:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting user voices:', error);
    throw error;
  }
}

/**
 * Получение списка голосов сообщества
 */
export async function getCommunityVoices() {
  try {
    const { data, error } = await supabase
      .from('voice_clones')
      .select('*')
      .eq('public', true)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching community voices:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting community voices:', error);
    throw error;
  }
}

/**
 * Сохранение клонированного голоса в базу данных
 */
export async function saveVoiceClone(data: {
  user_id: string;
  name: string;
  voice_id: string;
  audio_url: string;
  model: string;
  accuracy?: number;
  public?: boolean;
}) {
  try {
    const { error } = await supabase
      .from('voice_clones')
      .insert({
        user_id: data.user_id,
        name: data.name,
        voice_id: data.voice_id,
        audio_url: data.audio_url,
        model: data.model,
        accuracy: data.accuracy || 0.7,
        public: data.public !== false, // по умолчанию публичный
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving voice clone:', error);
      throw error;
    }

    console.log('Voice clone saved successfully');
  } catch (error) {
    console.error('Error saving voice clone:', error);
    throw error;
  }
}

/**
 * Сохранение генерации речи в базу данных
 */
export async function saveSpeechGeneration(data: {
  user_id: string;
  text: string;
  audio_url: string;
  voice_id: string;
  voice_name: string;
  model: string;
  parameters: any;
  cost: number;
}) {
  try {
    const { error } = await supabase
      .from('speech_generations')
      .insert({
        user_id: data.user_id,
        text: data.text,
        audio_url: data.audio_url,
        voice_id: data.voice_id,
        voice_name: data.voice_name,
        model: data.model,
        parameters: data.parameters,
        cost: data.cost,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving speech generation:', error);
      throw error;
    }

    console.log('Speech generation saved successfully');
  } catch (error) {
    console.error('Error saving speech generation:', error);
    throw error;
  }
}

/**
 * Получение истории генераций речи пользователя
 */
export async function getSpeechGenerations(userId: string) {
  try {
    console.log('Fetching speech generations for user:', userId);
    
    const { data, error } = await supabase
      .from('speech_generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching speech generations:', error);
      throw error;
    }

    console.log('Speech generations fetched:', data?.length || 0, 'items');
    console.log('Speech generations data:', data);
    
    return data || [];
  } catch (error) {
    console.error('Error getting speech generations:', error);
    throw error;
  }
}

/**
 * Обновление счетчика использования голоса
 */
export async function incrementVoiceUsage(voiceId: string) {
  try {
    const { error } = await supabase.rpc('increment_voice_usage', {
      voice_id_param: voiceId
    });

    if (error) {
      console.error('Error incrementing voice usage:', error);
      // Не бросаем ошибку, так как это не критично
    }
  } catch (error) {
    console.error('Error incrementing voice usage:', error);
    // Не бросаем ошибку, так как это не критично
  }
}

/**
 * Генерация речи через FAL API (ElevenLabs TTS)
 */
export async function generateSpeechWithFal(params: {
  text: string;
  voice: string;
  model: string;
  userId: string;
  stability?: number;
  similarity_boost?: number;
  style?: number;
  speed?: number;
  timestamps?: boolean;
  visibility?: string;
  onProgress?: (progress: number, step: number) => void;
}): Promise<string> {
  console.log('=== FAL SPEECH GENERATION STARTED ===');
  console.log('Starting FAL speech generation with params:', params);

  try {
    // Подготавливаем input для FAL API
    const input: any = {
      text: params.text,
      voice: params.voice || 'Rachel',
      stability: params.stability || 0.5,
      similarity_boost: params.similarity_boost || 0.75,
      style: params.style || 0,
      speed: params.speed || 1,
      timstamps: params.timestamps || false  // Обратите внимание: FAL API использует "timstamps" (без 'e')
    };

    console.log('Sending request to FAL Speech API:', { model: params.model, input });

    const requestBody = {
      model: params.model,
      userId: params.userId,
      input: {
        ...input,
        metadata_visibility: params.visibility || 'public'
      }
    };

    // Отправляем запрос к нашему FAL Speech API
    const response = await fetch('/api/fal/speech', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('FAL Speech API error:', errorData);
      throw new Error(errorData.error || 'Speech generation failed');
    }

    const result = await response.json();
    console.log('FAL Speech API response:', result);

    if (!result.request_id) {
      throw new Error('No request ID received from FAL API');
    }

    // Запускаем постоянный polling как в видео генерации
    console.log('=== STARTING POLLING ===');
    console.log('Speech generation started, request ID:', result.request_id);

    if (params.onProgress) {
      params.onProgress(20, 2); // Запрос отправлен
    }

    // Постоянный polling статуса как в видео генерации
    const maxAttempts = 60; // 5 минут максимум (60 * 5 секунд)
    let attempt = 0;
    let retryDelay = 5000; // Начинаем с 5 секунд
    const maxRetryDelay = 10000; // Максимум 10 секунд

    console.log('=== ENTERING POLLING LOOP ===');

    while (attempt < maxAttempts) {
      try {
        console.log(`Polling speech status (attempt ${attempt + 1}/${maxAttempts})`);

        // Обновляем прогресс на основе времени
        const progressPercent = Math.min(20 + (attempt / maxAttempts) * 70, 90); // От 20% до 90%
        const currentStep = attempt < 5 ? 2 : attempt < 15 ? 3 : attempt < 30 ? 4 : 5;

        if (params.onProgress) {
          params.onProgress(progressPercent, currentStep);
        }

        // Проверяем статус через наш save endpoint
        const saveUrl = `/api/fal-speech-save`;
        console.log(`Speech save request attempt ${attempt + 1}/${maxAttempts} for request ${result.request_id}`);

        const saveResponse = await fetch(saveUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            requestId: result.request_id,
            model: params.model,
            userId: params.userId,
            text: params.text || '',
            voice: params.voice || 'Rachel',
            visibility: params.visibility || 'public'
          })
        });

        console.log(`Speech save response status: ${saveResponse.status}`);

        if (saveResponse.ok) {
          // Генерация завершена и сохранена
          const saveData = await saveResponse.json();
          console.log('Speech generation completed! Result:', saveData);
          console.log('=== DEBUGGING SAVE DATA ===');
          console.log('saveData.audioUrl:', saveData.audioUrl);
          console.log('saveData.url:', saveData.url);
          console.log('saveData.audio:', saveData.audio);
          console.log('saveData.audio?.url:', saveData.audio?.url);

          // Устанавливаем прогресс на 100% при сохранении
          if (params.onProgress) {
            params.onProgress(100, 5);
          }

          const finalUrl = saveData.audioUrl || saveData.url || saveData.audio?.url;
          console.log('Final URL extracted:', finalUrl);

          if (!finalUrl) {
            console.error('No audio URL found in response:', saveData);
            throw new Error('No audio URL in save response');
          }

          console.log('FAL speech generation completed, final URL:', finalUrl);
          return finalUrl;

        } else if (saveResponse.status === 202) {
          // Генерация еще не завершена, продолжаем ждать
          console.log('Generation not completed yet, waiting...');
        } else {
          const errorText = await saveResponse.text();
          console.error(`Error getting speech result (${saveResponse.status}):`, errorText);

          // Если это серьезная ошибка, прекращаем попытки
          if (saveResponse.status >= 400 && saveResponse.status < 500) {
            throw new Error(`Speech generation failed: ${errorText}`);
          }
        }

        // Ждем перед следующей попыткой
        console.log(`Waiting ${retryDelay}ms before next check...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));

        // Увеличиваем задержку для следующей попытки
        retryDelay = Math.min(retryDelay * 1.2, maxRetryDelay);
        attempt++;

      } catch (error) {
        console.error(`Error during speech polling (attempt ${attempt + 1}):`, error);

        const errorMessage = error instanceof Error ? error.message : String(error);

        // Если это не последняя попытка, пробуем снова
        if (attempt < maxAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          retryDelay = Math.min(retryDelay * 2, maxRetryDelay);
          attempt++;
        } else {
          throw new Error(`Превышено максимальное время ожидания генерации речи: ${errorMessage}`);
        }
      }
    }

    throw new Error('Timeout waiting for speech generation');

  } catch (error) {
    console.error('Error in generateSpeechWithFal:', error);
    throw error;
  }
}

