const express = require('express');
const cors = require('cors');
const path = require('path');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
const Replicate = require('replicate');
const fs = require('fs');
const multer = require('multer');

// Загружаем переменные окружения
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Добавляем CSP middleware
app.use((req, res, next) => {
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.together.ai " +
    "https://api.replicate.com https://generativelanguage.googleapis.com https://umaai-api.vercel.app " +
    "https://umaaiapi.vercel.app http://localhost:3000 http://localhost:* https://localhost:* " +
    "https://uma-ai.vercel.app https://*.uma-ai.vercel.app; " +
    "img-src 'self' data: blob: https://* http://*; " +
    "media-src 'self' data: blob: https://* http://*; " +
    "style-src 'self' 'unsafe-inline'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval';"
  );
  next();
});

// Создаем клиент Replicate с API ключом
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN || "****************************************",
});

// Получаем переменные окружения
const supabaseUrl = process.env.SUPABASE_URL || 'https://ehamdaltpbuicmggxhbn.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseServiceKey) {
  console.warn('ВНИМАНИЕ: SUPABASE_SERVICE_KEY не установлен. API не сможет создавать профили!');
}

// Создаем клиент Supabase с сервисным ключом для админского доступа
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Настройка CORS middleware
const allowedOrigins = [
  'https://umaai.vercel.app',
  'https://www.umaai.vercel.app',
  'http://localhost:3000',
  'http://localhost:5173',
  'http://localhost:8080',
  // Добавьте другие разрешенные origin, если необходимо
];

app.use(cors({
  origin: function (origin, callback) {
    // Разрешаем запросы без origin (например, из мобильных приложений)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1 || origin.includes('localhost')) {
      callback(null, true);
    } else {
      console.warn(`CORS отклонён для ${origin}`);
      callback(null, true); // Временно разрешаем все запросы для отладки
    }
  },
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400 // 24 часа
}));

// Добавляем CORS заголовки для всех запросов
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Для OPTIONS запросов сразу отвечаем успехом
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Парсинг JSON и формата
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware для логирования запросов
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Проверяем, запущены ли мы в Vercel или локально
const isVercel = process.env.VERCEL === '1';

// Статика для загруженных файлов и Multer
let upload;
let uploadsDir;

try {
  // В Vercel используем временную директорию /tmp
  if (isVercel) {
    uploadsDir = '/tmp';
    console.log('Запуск в Vercel - используем директорию /tmp');
  } else {
    uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
  }

app.use('/uploads', express.static(uploadsDir));

// Настройка Multer для загрузки файлов
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir)
  },
  filename: function (req, file, cb) {
    const uniquePrefix = uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, uniquePrefix + ext);
  }
});

  upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB лимит
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Разрешены только изображения!'), false);
    }
  }
});
} catch (fsError) {
  console.error('Ошибка при настройке файловой системы:', fsError);
  // Если не получилось настроить файловую систему, используем память
  upload = multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 10 * 1024 * 1024 }
  });
}

// Проверка здоровья API
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'API server is running' });
});

// Эндпоинт для выдачи тестовых токенов
app.post('/api/test-credits', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Проверяем текущий баланс пользователя
    const { data: credits, error: creditError } = await supabase
      .from('user_credits')
      .select('amount')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (creditError) {
      console.error('Ошибка при проверке баланса:', creditError);
      return res.status(500).json({ error: 'Не удалось проверить баланс' });
    }

    // Определяем новый баланс
    let newBalance = 300; // Выдаем 300 тестовых токенов

    if (credits && credits.length > 0) {
      newBalance = credits[0].amount + 300;
    }

    // Добавляем токены
    const { error: updateError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount: newBalance,
        transaction_type: 'test_credits',
        description: 'Выдача тестовых токенов: +300 токенов',
        created_at: new Date().toISOString()
      });

    if (updateError) {
      console.error('Ошибка при выдаче тестовых токенов:', updateError);
      return res.status(500).json({ error: 'Не удалось выдать тестовые токены' });
    }

    console.log(`Пользователю ${userId} выдано 300 тестовых токенов. Новый баланс: ${newBalance}`);

    return res.json({
      success: true,
      message: 'Тестовые токены выданы успешно',
      balance: newBalance
    });
  } catch (error) {
    console.error('Ошибка при выдаче тестовых токенов:', error);
    return res.status(500).json({
      error: 'Произошла ошибка при выдаче тестовых токенов',
      details: error.message
    });
  }
});

// Эндпоинт для добавления кредитов (по секретному коду)
app.post('/api/add-credits', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Получаем текущий баланс пользователя
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Ошибка при получении кредитов пользователя:', profileError);
      return res.status(500).json({ error: 'Не удалось получить баланс' });
    }

    // Текущий баланс и размер начисления
    const currentCredits = profileData?.credits || 0;
    const addAmount = 300; // Увеличиваем до 300 токенов
    const newBalance = currentCredits + addAmount;

    // Обновляем баланс кредитов пользователя
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ credits: newBalance })
      .eq('id', userId);

    if (updateError) {
      console.error('Ошибка при обновлении кредитов пользователя:', updateError);
      return res.status(500).json({ error: 'Не удалось обновить баланс' });
    }

    // Записываем транзакцию в историю
    const { error: transactionError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount: addAmount,
        transaction_type: 'secret_code',
        description: 'Начисление за секретный код: +300 токенов',
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      console.error('Ошибка при записи транзакции кредитов:', transactionError);
      // Продолжаем выполнение, так как основная операция (обновление баланса) успешна
    }

    console.log(`Пользователю ${userId} начислено ${addAmount} токенов. Новый баланс: ${newBalance}`);

    return res.json({
      success: true,
      message: 'Токены успешно начислены',
      balance: newBalance
    });
  } catch (error) {
    console.error('Ошибка при начислении токенов:', error);
    return res.status(500).json({
      error: 'Произошла ошибка при начислении токенов',
      details: error.message
    });
  }
});

// Списываем кредиты
async function deductCredits(userId, mode, model = "") {
  if (!userId) return; // Пропускаем для анонимных пользователей

  try {
    // Проверяем, что HiDream не используется для img2img
    if ((mode === 'img2img' || mode === 'img2video') && model.toLowerCase().includes('hidream')) {
      throw new Error('Модель HiDream не поддерживает режим генерации на основе изображений');
    }

    // Получаем текущий баланс пользователя
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Ошибка при проверке баланса:', profileError);
      throw new Error('Не удалось проверить баланс кредитов');
    }

    // Определяем стоимость операции
    let cost;
    if (mode === 'image') {
      cost = model.toLowerCase().includes('hidream') ? 10 : 5;
    } else if (mode === 'img2img') {
      cost = 5;
    } else if (mode === 'text2video') {
      cost = 15;
    } else if (mode === 'img2video') {
      cost = 15;
    } else {
      cost = 5; // Значение по умолчанию
    }

    // Проверяем, достаточно ли кредитов
    if (profile.credits < cost) {
      throw new Error(`Недостаточно кредитов. Требуется: ${cost}, доступно: ${profile.credits}`);
    }

    // Обновляем баланс
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ credits: profile.credits - cost })
      .eq('id', userId);

    if (updateError) {
      console.error('Ошибка при обновлении баланса:', updateError);
      throw new Error('Не удалось обновить баланс кредитов');
    }

    console.log(`Списано ${cost} кредитов у пользователя ${userId}. Новый баланс: ${profile.credits - cost}`);
    return profile.credits - cost; // Возвращаем новый баланс
  } catch (error) {
    console.error('Ошибка при списании кредитов:', error);
    throw error;
  }
}

// Эндпоинт для проксирования запросов к Replicate API
app.post('/api/replicate', async (req, res) => {
  try {
    const { model, input, userId } = req.body;

    if (!model) {
      return res.status(400).json({ error: 'Model name is required' });
    }

    // Проверка совместимости модели и режима
    if (input.start_image && (model.includes('hidream') || model.includes('prunaai/hidream'))) {
        return res.status(400).json({
        error: 'Модель HiDream не поддерживает генерацию на основе изображения'
      });
    }

    // Проверяем, если есть userId, списываем кредиты
    if (userId) {
      // Определяем стоимость в зависимости от модели
      let creditCost = 2; // Стоимость по умолчанию для Flux schnell
      let transactionType = 'text2img';
      let description = `Генерация изображения: ${input.prompt?.substring(0, 30)}...`;

      // Цены в зависимости от модели
      if (model.includes('ideogram-v2a')) {
        // Ideogram V2A (Ideogram 2) - 10 токенов за любой режим
        creditCost = 10;
        description = `Ideogram 2: ${input.prompt?.substring(0, 30)}...`;
      } else if (model.includes('ideogram-v3') || model.includes('hidream') || model.includes('prunaai/hidream')) {
        // Ideogram V3 (Ideogram 3) / HiDream - 15 токенов за генерацию
        creditCost = 15;
        description = `Ideogram 3: ${input.prompt?.substring(0, 30)}...`;
      } else if (model.includes('ideogram')) {
        // Общий случай для других ideogram моделей - 10 токенов
        creditCost = 10;
        description = `Ideogram: ${input.prompt?.substring(0, 30)}...`;
      } else if (model.includes('ray-flash')) {
        // Ray 2 flash 540p - 100 токенов
        creditCost = 100;
        transactionType = 'text2video';
        description = `Ray Flash (видео): ${input.prompt?.substring(0, 30)}...`;
      } else if (model.includes('kling-v1.6-standard')) {
        // Kling 1.6 standart - 150 токенов
        creditCost = 150;
        transactionType = 'text2video';
        description = `Kling Standard (видео): ${input.prompt?.substring(0, 30)}...`;
      } else if (model.includes('kling-v1.6-pro')) {
        // Kling 1.6 Pro - 300 токенов
        creditCost = 300;
        transactionType = 'text2video';
        description = `Kling Pro (видео): ${input.prompt?.substring(0, 30)}...`;
      }

      // Если есть start_image, это img2img или img2video
      if (input.start_image) {
        if (transactionType === 'text2img') {
          transactionType = 'img2img';
          description = `Изображение-в-изображение (${model.split('/').pop()}): ${input.prompt?.substring(0, 30)}...`;
        } else if (transactionType === 'text2video') {
          transactionType = 'img2video';
          description = `Изображение-в-видео (${model.split('/').pop()}): ${input.prompt?.substring(0, 30)}...`;
        }
      }

      try {
        // Получаем текущий баланс пользователя
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits')
          .eq('id', userId)
          .single();

        if (profileError) {
          console.error('Ошибка при получении кредитов пользователя:', profileError);
          return res.status(500).json({ error: 'Не удалось проверить баланс' });
        }

        const currentCredits = profileData?.credits || 0;

        // Проверяем, достаточно ли кредитов
        if (currentCredits < creditCost) {
          return res.status(400).json({
            error: 'Недостаточно кредитов',
            currentCredits,
            requiredCredits: creditCost
          });
        }

        // Списываем кредиты
        const newBalance = currentCredits - creditCost;

        // Обновляем баланс
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ credits: newBalance })
          .eq('id', userId);

        if (updateError) {
          console.error('Ошибка при списании кредитов:', updateError);
          return res.status(500).json({ error: 'Не удалось списать кредиты' });
        }

        // Записываем транзакцию
        const { error: transactionError } = await supabase
          .from('user_credits')
          .insert({
            user_id: userId,
            amount: -creditCost,
            transaction_type: transactionType,
            description,
            created_at: new Date().toISOString()
          });

        if (transactionError) {
          console.error('Ошибка при записи транзакции:', transactionError);
          // Продолжаем выполнение, основная операция успешна
        }

        console.log(`Списано ${creditCost} кредитов у пользователя ${userId}. Новый баланс: ${newBalance}`);
      } catch (creditError) {
        console.error('Ошибка при обработке кредитов:', creditError);
        return res.status(500).json({ error: 'Не удалось обработать кредиты' });
      }
    }

    // Проксируем запрос к Replicate API
    const replicateApiUrl = 'https://api.replicate.com/v1/predictions';
    const replicateApiKey = process.env.REPLICATE_API_KEY;

    if (!replicateApiKey) {
      return res.status(500).json({ error: 'REPLICATE_API_KEY is not configured' });
    }

    const replicateResponse = await fetch(replicateApiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${replicateApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        version: model,
        input
      })
    });

    if (!replicateResponse.ok) {
      const errorData = await replicateResponse.text();
      console.error(`Replicate API Error (${replicateResponse.status}):`, errorData);
      return res.status(replicateResponse.status).json({
        error: `Replicate API error: ${replicateResponse.status}`,
        details: errorData
      });
    }

    const prediction = await replicateResponse.json();

    // Ждем завершения предсказания
    let result = prediction;
    if (prediction.status === 'starting' || prediction.status === 'processing') {
      while (result.status !== 'succeeded' && result.status !== 'failed') {
        // Ждем 1 секунду перед следующей проверкой
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Проверяем статус
        const checkResponse = await fetch(`${replicateApiUrl}/${prediction.id}`, {
          headers: {
            'Authorization': `Token ${replicateApiKey}`,
          }
        });

        if (!checkResponse.ok) {
          const errorData = await checkResponse.text();
          console.error(`Error checking prediction status (${checkResponse.status}):`, errorData);
          return res.status(checkResponse.status).json({
            error: `Error checking prediction status: ${checkResponse.status}`,
            details: errorData
          });
        }

        result = await checkResponse.json();
      }
    }

    // Возвращаем результат
    return res.json({
      status: result.status,
      output: result.output,
      error: result.error,
      id: result.id
    });
  } catch (error) {
    console.error('Error proxying to Replicate API:', error);
    return res.status(500).json({
      error: 'Error proxying to Replicate API',
      details: error.message
    });
  }
});

// Эндпоинт для Replicate API (генерация img2img)
app.post('/api/replicate-img2img', async (req, res) => {
  try {
    const {
      prompt,
      image,
      userId,
      aspectRatio = "1:1"
    } = req.body;

    console.log('Received img2img generation request:', {
      prompt,
      hasImage: !!image,
      aspectRatio,
      userId
    });

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    if (!image) {
      return res.status(400).json({ error: 'Image is required for img2img' });
    }

    // Проверяем и списываем токены, если передан userId
    if (userId && userId !== 'anonymous') {
      try {
        // Списываем кредиты
        await deductCredits(userId, 'img2img', 'ideogram');
      } catch (creditError) {
        return res.status(400).json({
          error: 'Ошибка при списании кредитов',
          details: creditError.message
        });
      }
    }

    // Запрос к Replicate API для модели ideogram-v2a-turbo
    try {
      // Создаем запрос к API Replicate
      const prediction = await replicate.run(
        "ideogram-ai/ideogram-v2a-turbo",
        {
          input: {
            prompt: prompt,
            image: image,
            aspect_ratio: aspectRatio
          }
        }
      );

      console.log('img2img generated successfully, result:', prediction);

      let imageUrl = '';

      // Обработка различных форматов вывода
      if (typeof prediction === 'string') {
        imageUrl = prediction;
      } else if (Array.isArray(prediction)) {
        imageUrl = prediction[0] || '';
      } else if (prediction && typeof prediction === 'object') {
        imageUrl = prediction.output || prediction.url || '';
      }

      if (!imageUrl) {
        console.error('No image URL found in Replicate response');
        return res.status(500).json({
          error: 'Failed to generate image',
          details: 'No image URL found in response'
        });
      }

      res.json({
        success: true,
        imageUrl: imageUrl
      });
    } catch (replicateError) {
      console.error('Error in Replicate API call:', replicateError);
      return res.status(500).json({
        error: 'Failed to generate image',
        details: replicateError.message
      });
    }
  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).json({
      error: 'Failed to generate image',
      details: error.message
    });
  }
});

// Эндпоинт для загрузки изображений
app.post('/api/upload', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Формируем URL для доступа к загруженному файлу
    const baseUrl = `${req.protocol}://${req.get('host')}`;

    // Проверяем, используем ли память или файловую систему
    if (req.file.buffer) {
      // Для memoryStorage мы не сохраняем файл, а возвращаем его как Data URL
      const base64Image = req.file.buffer.toString('base64');
      const dataUrl = `data:${req.file.mimetype};base64,${base64Image}`;

      console.log(`File uploaded to memory successfully`);
      return res.status(200).json({
        success: true,
        imageUrl: dataUrl
      });
    } else {
      // Для дискового хранилища - возвращаем URL файла
    const imageUrl = `${baseUrl}/uploads/${req.file.filename}`;

      console.log(`File uploaded to disk successfully: ${imageUrl}`);
    return res.status(200).json({
      success: true,
      imageUrl: imageUrl
    });
    }
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({
      error: 'File upload failed',
      details: error.message
    });
  }
});

// API эндпоинт для создания профиля
app.post('/api/create-profile', async (req, res) => {
  try {
    const { userId, email, name } = req.body;

    if (!userId || !email) {
      console.error('Отсутствуют обязательные поля: userId или email');
      return res.status(400).json({
        error: 'Необходимы userId и email',
        received: { userId, email }
      });
    }

    console.log(`Запрос на создание профиля для пользователя: ${userId} (${email})`);

    // Проверяем существование профиля
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Ошибка при проверке профиля:', checkError);
      return res.status(500).json({
        error: 'Ошибка при проверке профиля',
        details: checkError.message
      });
    }

    // Если профиль существует, возвращаем его
    if (existingProfile) {
      console.log(`Профиль для ${userId} уже существует, возвращаем существующий`);
      return res.json({ profile: existingProfile });
    }

    // Создаем новый профиль
    console.log(`Создаем новый профиль для ${userId}`);
    const nowIso = new Date().toISOString();

    const { data: profile, error } = await supabase
        .from('profiles')
      .insert({
        id: userId,
        email,
        name: name || '',
        credits: 10, // Начальное количество кредитов
        created_at: nowIso,
        updated_at: nowIso
      })
        .select()
        .single();

    if (error) {
      console.error('Ошибка при создании профиля:', error);

      // Если ошибка связана с тем, что профиль уже существует, пробуем получить его
      if (error.code === '23505') { // Unique constraint violation
        console.log('Профиль уже существует (constraint violation), пробуем получить его');
        const { data: retryProfile, error: retryError } = await supabase
        .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

        if (retryError || !retryProfile) {
          console.error('Не удалось получить существующий профиль:', retryError);
          return res.status(500).json({
            error: 'Профиль существует, но не удалось его получить',
            details: retryError?.message || 'Неизвестная ошибка'
          });
        }

        return res.status(200).json(retryProfile);
      }

      return res.status(500).json({
        error: 'Ошибка при создании профиля',
        details: error.message
      });
    }

    console.log(`Профиль успешно создан для ${userId}`);
    return res.status(201).json(profile);
  } catch (error) {
    console.error('Серверная ошибка:', error);
    return res.status(500).json({
      error: 'Внутренняя ошибка сервера',
      details: error.message
    });
  }
});

// API endpoint для проверки существования профиля пользователя
app.post('/api/check-profile', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID обязателен' });
    }

    console.log(`Проверка существования профиля для пользователя: ${userId}`);

    // Проверяем, существует ли профиль
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Ошибка при проверке профиля:', checkError);
      return res.status(500).json({ error: 'Ошибка при проверке профиля', details: checkError.message });
    }

    if (existingProfile) {
      console.log('Профиль существует:', existingProfile.id);
      return res.json({ exists: true, id: existingProfile.id });
    } else {
      console.log('Профиль не найден');
      return res.json({ exists: false });
    }
  } catch (error) {
    console.error('Критическая ошибка при проверке профиля:', error);
    return res.status(500).json({
      error: 'Критическая ошибка при проверке профиля',
      details: error.message
    });
  }
});

// Маршрут для сохранения результатов генерации
app.post('/api/save-generation', async (req, res) => {
  try {
    const { userId, prompt, imageUrl, style, aspectRatio } = req.body;

    if (!userId || !prompt || !imageUrl) {
      return res.status(400).json({ error: 'Не указаны обязательные поля' });
    }

    const { data, error } = await supabase
      .from('image_generations')
      .insert({
        user_id: userId,
        prompt,
        image_url: imageUrl,
        style: style || 'standart',
        aspect_ratio: aspectRatio || '1:1',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Ошибка при сохранении генерации:', error);
      return res.status(500).json({ error: 'Не удалось сохранить генерацию' });
    }

    return res.status(201).json(data);
  } catch (error) {
    console.error('Ошибка при сохранении генерации:', error);
    return res.status(500).json({ error: 'Внутренняя ошибка сервера' });
  }
});

// Документация API
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>UMA.AI API</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        h1 { color: #2563eb; }
        h2 { color: #3b82f6; margin-top: 2em; }
        code {
          background-color: #f1f5f9;
          padding: 2px 4px;
          border-radius: 4px;
          font-family: monospace;
        }
        pre {
          background-color: #f1f5f9;
          padding: 12px;
          border-radius: 8px;
          overflow-x: auto;
        }
        .endpoint {
          border-left: 4px solid #3b82f6;
          padding-left: 12px;
          margin-bottom: 24px;
        }
        .method {
          font-weight: bold;
          display: inline-block;
          padding: 2px 6px;
          border-radius: 4px;
          margin-right: 8px;
        }
        .get { background-color: #10b981; color: white; }
        .post { background-color: #f59e0b; color: white; }
      </style>
    </head>
    <body>
      <h1>UMA.AI API Documentation</h1>
      <p>Добро пожаловать в API для UMA.AI. Ниже описаны доступные эндпоинты.</p>

      <h2>Эндпоинты</h2>

      <div class="endpoint">
        <h3><span class="method post">POST</span> /api/create-profile</h3>
        <p>Создает или получает профиль пользователя в Supabase</p>
        <h4>Параметры запроса (JSON):</h4>
        <pre>{
  "userId": "uuid-пользователя",
  "email": "email-пользователя",
  "name": "имя-пользователя" // опционально
}</pre>
        <h4>Пример ответа:</h4>
        <pre>{
  "id": "uuid-пользователя",
  "email": "email-пользователя",
  "name": "имя-пользователя",
  "credits": 10,
  "created_at": "2023-08-15T12:34:56.789Z",
  "updated_at": "2023-08-15T12:34:56.789Z"
}</pre>
      </div>

      <div class="endpoint">
        <h3><span class="method post">POST</span> /api/save-generation</h3>
        <p>Сохраняет результаты генерации изображения в БД</p>
        <h4>Параметры запроса (JSON):</h4>
        <pre>{
  "userId": "uuid-пользователя",
  "prompt": "текст запроса",
  "imageUrl": "ссылка на изображение",
  "style": "стиль изображения", // опционально
  "aspectRatio": "соотношение сторон" // опционально
}</pre>
        <h4>Пример ответа:</h4>
        <pre>{
  "id": "uuid-генерации",
  "user_id": "uuid-пользователя",
  "prompt": "текст запроса",
  "image_url": "ссылка на изображение",
  "style": "стиль изображения",
  "aspect_ratio": "соотношение сторон",
  "created_at": "2023-08-15T12:34:56.789Z"
}</pre>
      </div>

      <p>Текущий статус API: работает</p>
    </body>
    </html>
  `);
});

// Обработка ошибок
app.use((err, req, res, next) => {
  console.error('Ошибка сервера:', err);
  res.status(500).json({
    error: 'Внутренняя ошибка сервера',
    message: err.message
  });
});

// Запуск сервера
app.listen(PORT, () => {
  console.log(`
    🚀 API сервер запущен на порту ${PORT}
    📝 Документация доступна по адресу: http://localhost:${PORT}

    Настройки Supabase:
    - URL: ${supabaseUrl}
    - Service Key: ${supabaseServiceKey ? 'Установлен' : 'НЕ УСТАНОВЛЕН!'}
  `);
});

// Экспортируем app для Vercel Serverless Functions
module.exports = app;