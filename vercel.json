{"version": 2, "regions": ["fra1"], "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}, {"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist", "zeroConfig": true}}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Surrogate-Control", "value": "no-store"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' 'inline-speculation-rules' https://cdn.gpteng.co; script-src-elem 'self' 'unsafe-inline' https://cdn.gpteng.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https://ehamdaltpbuicmggxhbn.supabase.co https://replicate.delivery https://*.replicate.delivery https://fal.media https://*.fal.media https://v3.fal.media; connect-src 'self' data: https://*.supabase.co wss://*.supabase.co https://api.together.ai https://api.replicate.com https://generativelanguage.googleapis.com https://uma-ai.vercel.app https://umaai-api.vercel.app https://umaaiapi.vercel.app https://umaai.vercel.app https://uma-al.vercel.app https://umaai.site https://umaai.site/api https://replicate.delivery https://*.replicate.delivery https://fal.run https://*.fal.run https://queue.fal.run http://localhost:3000 http://localhost:* https://localhost:*; media-src 'self' blob: https://replicate.delivery https://*.replicate.delivery https://fal.media https://*.fal.media https://v3.fal.media https://umaai.site https://umaai.site/api https://ehamdaltpbuicmggxhbn.supabase.co;"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/yukassa/payment/:path*", "destination": "/api/yukassa/payment/:path*"}, {"source": "/api/yukassa/payment/check", "destination": "/api/yukassa/payment/check.js"}, {"source": "/api/google/generate-text", "destination": "/api/google/generate-text.js"}, {"source": "/api/replicate/status", "destination": "/api/replicate/status.js"}, {"source": "/api/replicate/webhook", "destination": "/api/replicate/webhook.js"}, {"source": "/api/fal/poll", "destination": "/api/fal/poll.js"}, {"source": "/api/fal-correct", "destination": "/api/fal-correct.js"}, {"source": "/api/fal-poll-correct", "destination": "/api/fal-poll-correct.js"}, {"source": "/api/fal-save", "destination": "/api/fal-save.js"}, {"source": "/api/fal-speech-save", "destination": "/api/fal-speech-save.js"}, {"source": "/api/falresult", "destination": "/api/falresult.js"}, {"source": "/api/fal", "destination": "/api/fal/index.js"}, {"source": "/api/fal/webhook", "destination": "/api/fal/webhook.js"}, {"source": "/api/fal/veo3", "destination": "/api/fal/veo3/index.js"}, {"source": "/api/fal/veo3/status", "destination": "/api/fal/veo3/status/index.js"}, {"source": "/api/fal/veo3/result", "destination": "/api/fal/veo3/result/index.js"}, {"source": "/api/fal/veo3/webhook", "destination": "/api/fal/veo3/webhook/index.js"}, {"source": "/api/analytics", "destination": "/api/analytics/index.js"}, {"source": "/yukassa/webhook", "destination": "/api/yukassa/payment/webhook"}, {"source": "/api/mistral/generate-text", "destination": "/api/mistral/generate-text.js"}, {"source": "/((?!api/).*)", "destination": "/"}], "build": {"env": {"VITE_SUPABASE_URL": "https://ehamdaltpbuicmggxhbn.supabase.co", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4ODI4NzIsImV4cCI6MjA1OTQ1ODg3Mn0.E-Q4-HfKege8ipj2JFGsl8sCFlUbbDhJqTue7A6Uhgo", "VITE_API_URL": "https://umaai.site"}}, "env": {"GOOGLE_AI_API_KEY": "AIzaSyCA3bJ9QEYKynM7lJpt0LenetnIyjNFmVc", "YUKASSA_SHOP_ID": "1067016", "YUKASSA_SECRET_KEY": "live_nNIgnTH7iFB6f7hCJKJcn37BZUEalVVEj5Q0fvtWfqw", "FAL_KEY": "fa85e53b-4347-4eb7-89cf-59db3a2a4644:a38599593556e96e12dbd91744aa8607", "SUPABASE_URL": "https://ehamdaltpbuicmggxhbn.supabase.co", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoYW1kYWx0cGJ1aWNtZ2d4aGJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzg4Mjg3MiwiZXhwIjoyMDU5NDU4ODcyfQ.nhTTkxS8gam_1PejxDX93nsVgKmy61BJdc_ss8xuAYc", "REPLICATE_API_TOKEN": "****************************************"}, "github": {"silent": true, "autoAlias": true}}