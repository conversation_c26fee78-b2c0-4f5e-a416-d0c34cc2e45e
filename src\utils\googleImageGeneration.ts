import { toast } from 'sonner';
import type { GeneratedImage } from './stabilityImageGeneration'; // Используем интерфейс из stabilityImageGeneration.ts

// Базовый URL API
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

export interface GoogleImageGenerationOptions {
  prompt: string;
  negativePrompt?: string;
  size?: string;
  numberOfImages?: number;
  model?: string;
  userId?: string; // Добавляем userId для отслеживания кредитов
}

const DEFAULT_MODEL = "imagegeneration@005";

export const generateImageWithGoogle = async (
  options: GoogleImageGenerationOptions
): Promise<GeneratedImage[]> => {
  try {
    const {
      prompt,
      negativePrompt = "",
      size = "1024x1024",
      numberOfImages = 1,
      model = DEFAULT_MODEL,
      userId
    } = options;

    console.log('Starting image generation with Google:', { prompt, size, numberOfImages, model });
    
    // Создаем запрос к бессерверной функции
    const response = await fetch('/api/google/generate-image', {
          method: "POST",
          headers: {
        "Content-Type": "application/json"
          },
          body: JSON.stringify({
        prompt,
        negative_prompt: negativePrompt,
        size,
        model,
        userId
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Error response from Google API:', errorData);
      throw new Error(errorData.error || errorData.message || 'Failed to generate image');
        }

        const data = await response.json();
    
    const results: GeneratedImage[] = [];
        
        if (data.candidates && data.candidates.length > 0 && 
            data.candidates[0].content && 
            data.candidates[0].content.parts) {
          
          const parts = data.candidates[0].content.parts;
          const imageParts = parts.filter(part => part.inlineData && part.inlineData.mimeType.startsWith('image/'));
      
      // Разбиваем размер на ширину и высоту
      const [width, height] = size.split('x').map(dim => parseInt(dim, 10));
          
          for (const part of imageParts) {
            if (part.inlineData && part.inlineData.data) {
              const imageUrl = `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`;
              
              results.push({
                url: imageUrl,
            width,
            height,
                prompt,
                negativePrompt,
                model
              });
            }
          }
    }
    
    if (results.length === 0) {
      throw new Error('No images were generated');
    }
    
    console.log(`Successfully generated ${results.length} images with Google AI`);
    return results;
  } catch (error) {
    console.error('Error in generateImageWithGoogle:', error);
    toast.error(`Ошибка генерации изображения: ${error.message || 'Неизвестная ошибка'}`);
    throw error;
  }
};

export default generateImageWithGoogle;